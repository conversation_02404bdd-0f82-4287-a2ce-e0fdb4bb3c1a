#!/bin/bash

TZ=${TZ:-"Asia/Shanghai"}
timedatectl set-timezone "$TZ"

cd /opt

if [ "$HEAP_SIZE" = '' ]; then
   HEAP_SIZE=2G
fi

JAVAAGENT_OPTS=''

if [ "$AGENT_SKYWALKING_ENABLE" = 'true' ]; then
  export SW_AGENT_NAME=%SKY_WALKING_SERVICE_NAME
  export SW_AGENT_COLLECTOR_BACKEND_SERVICES=$SKY_WALKING_HOST
  JAVAAGENT_OPTS=$JAVAAGENT_OPTS" -javaagent:/opt/skywalking/skywalking-agent.jar "; \
fi

echo "HEAP_SIZE IS $HEAP_SIZE"
echo "JAVAAGENT_OPTS IS $JAVAAGENT_OPTS"

java -Xmx$HEAP_SIZE -Xms$HEAP_SIZE $JAVAAGENT_OPTS -jar /opt/tas.jar \
  --NAMESPACE_ID=$NAMESPACE_ID --DATA_ID=$DATA_ID --NACOS_USERNAME=$NACOS_USERNAME --NACOS_PASSWORD=$NACOS_PASSWORD
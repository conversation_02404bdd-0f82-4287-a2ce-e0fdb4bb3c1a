########################
# OptaPlanner properties
########################

#optaplanner:
#  solver:
#    termination:
#      spent-limit: 30s  # The solver runs for 30 seconds. To run for 5 minutes use "5m" and for 2 hours use "2h".
#    move-thread-count: 2  # To run increase CPU cores usage per solver
#    environment-mode: FULL_ASSERT  # Temporary comment this out to detect bugs in your code (lowers performance)
#  benchmark:
#    solver:
#      termination:
#        spent-limit: 15s  # When benchmarking, each individual solver runs for 15 seconds. To run for 5 minutes use "5m" and for 2 hours use "2h".
#  solver-manager:
#    parallel-solver-count: 4  # To change how many solvers to run in parallel
#  solver-config-xml: solverConfig.xml  # XML file for power tweaking, defaults to solverConfig.xml (directly under src/main/resources)

########################
# Spring Boot properties
########################
server:
  port: 8080
logging:
  level:
    root: INFO
    com.angelalign: INFO
    org.hibernate: INFO
    org:
      optaplanner: DEBUG  # To see what OptaPlanner is doing, turn on DEBUG or TRACE logging.
  pattern:
    console: "%d{HH:mm:ss.SSS} %clr(${LOG_LEVEL_PATTERN:%5p}) %blue([%-15.15t]) %m%n"  # Make it easier to read OptaPlanner logging

tas:
  terminationMinuteLimit: 30
  terminationSecondBase: 1
  terminationDigitalRation: 1.2
  unimprovedSpentSecondLimit: 500

spring:
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        globally_quoted_identifiers: true
        connection:
          isolation: 2
  #        show_sql: true
  #        format_sql: true
  datasource:
    hikari:
      leak-detection-threshold: 6000000
      connection-timeout: 600000
      idle-timeout: 6000000
      maximum-pool-size: 10
    url: ********************************************************************************************************************************************************
    username: gms_dev
    password: aJlhjEb4ErYQ3mjU
#    url: ***************************************************************************************************************************************************************************************************************
#    username: gms_sit
#    password: xTpewS4pR8gMUepX
    #    url: ***************************************************************************************************************************************************************************************************************************
    #    username: mts2_sit
    #    password: FgJW0zcKqCy5qyDP
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: 127.0.0.1
    port: 6379
    database: 0
    timeout: 3000


<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <title>TAS 工单查询系统</title>
    <!-- 使用多个CDN备选方案 -->
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"
            onerror="this.onerror=null; this.src='https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js'"></script>
    <style>
        /* 基础样式 */
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f0f2f5;
        }

        /* 环境选择器 */
        .env-selector {
            margin-bottom: 20px;
        }

        select {
            padding: 8px 12px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        /* 查询表单 */
        #queryForm {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            align-items: center;
        }

        #taskId, #problemId, #designerAccount {
            padding: 8px;
            width: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }

        button {
            padding: 8px 16px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }

        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }

        /* Loading状态 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #1890ff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            0% {
                transform: rotate(0deg);
            }
            100% {
                transform: rotate(360deg);
            }
        }

        .loading-text {
            color: #1890ff;
            font-weight: bold;
        }

        /* 空结果提示样式 */
        .empty-result {
            text-align: center;
            padding: 40px 20px;
            color: #666;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin: 20px 0;
        }

        .empty-result .icon {
            font-size: 48px;
            color: #ccc;
            margin-bottom: 16px;
        }

        .empty-result .title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 8px;
            color: #333;
        }

        .empty-result .description {
            font-size: 14px;
            color: #666;
        }

        /* 抽屉头部按钮样式 */
        .drawer-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }

        .drawer-header h2 {
            margin: 0;
            color: #333;
        }

        .drawer-buttons {
            display: flex;
            gap: 10px;
        }

        .export-btn {
            padding: 8px 16px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .export-btn:hover {
            background: #40a9ff;
        }

        .comparison-btn {
            padding: 8px 16px;
            background: #52c41a;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .comparison-btn:hover {
            background: #389e0d;
        }

        .close-btn {
            padding: 8px 16px;
            background: #f5f5f5;
            color: #333;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }

        .close-btn:hover {
            background: #e6f7ff;
            border-color: #1890ff;
            color: #1890ff;
        }

        /* 页脚样式 */
        .footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: #f8f9fa;
            border-top: 1px solid #e9ecef;
            padding: 8px 20px;
            font-size: 12px;
            color: #6c757d;
            z-index: 1000;
        }

        .footer-content {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
        }

        .footer .separator {
            color: #dee2e6;
        }

        #currentEnvironment {
            font-weight: bold;
            color: #495057;
        }

        #systemVersion {
            font-weight: bold;
            color: #495057;
        }

        /* 为页面内容添加底部边距，避免被页脚遮挡 */
        body {
            padding-bottom: 40px;
        }

        /* 结果列表 */
        .task-item {
            background: white;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
        }

        /* 抽屉样式 */
        .drawer {
            position: fixed;
            top: 0;
            right: -100%;
            width: 90%;
            height: 95%;
            background: white;
            box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
            transition: right 0.3s;
            padding: 0;
            overflow-y: auto;
            display: flex;
        }

        .drawer.active {
            right: 0;
        }

        /* 抽屉左侧图表区域 */
        .drawer-charts {
            width: 40%;
            background: #f8f9fa;
            padding: 20px;
            border-right: 1px solid #dee2e6;
            overflow-y: auto;
        }

        /* 抽屉右侧详情区域 */
        .drawer-details {
            width: 60%;
            padding: 20px;
            overflow-y: auto;
        }

        /* 图表容器 */
        .chart-container {
            margin-bottom: 30px;
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .chart-title {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }

        .chart-canvas {
            position: relative;
            height: 300px;
        }

        /* 统计摘要 */
        .stats-summary {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .stats-summary h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .stat-item:last-child {
            margin-bottom: 0;
        }

        .stat-value {
            font-weight: bold;
            color: #1890ff;
        }

        /* 简单图表样式 */
        .simple-chart-bar {
            background: #e0e0e0;
            height: 20px;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 2px;
        }

        .simple-chart-fill {
            height: 100%;
            transition: width 0.5s ease;
            border-radius: 10px;
        }

        .simple-chart-item {
            margin-bottom: 12px;
            padding: 8px;
            background: #f9f9f9;
            border-radius: 4px;
        }

        .simple-chart-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
            font-size: 12px;
        }

        .simple-chart-value {
            font-weight: bold;
            color: #333;
        }

        /* 新增样式，让抽屉内数据容器宽度为100% */
        /* 表格样式优化 */
        .task-table {
            border-collapse: collapse;
            width: 100%;
            margin-top: 10px;
        }

        .task-table th, .task-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }

        .task-table th {
            background-color: #f2f2f2;
            color: #333;
            font-weight: bold;
        }

        .task-table tr:hover {
            background-color: #f5f5f5;
        }

        #detailContent,
        .designer-list,
        .designer-card,
        .task-table {
            width: 100%;
        }
    </style>
</head>

<body>
<!-- 环境选择 -->
<div class="env-selector">
    <select id="environment" onchange="updateCurrentEnvironment()">
        <option value="https://tas-sit.eainc.com">SIT环境</option>
        <option value="https://mts-tas-sit.angeltreat.my">马来SIT环境</option>
        <option value="https://tas-brza-sit.eainc.com">巴西SIT环境</option>
        <option value="https://tas-cnsha.eainc.com">design/wxi/atp生产环境</option>
        <option value="https://tas-brza.eainc.com">巴西生产环境</option>
        <option value="https://mts-tas.angeltreat.my">马来生产环境</option>
        <option value="http://localhost:8080">本地环境</option>
    </select>
</div>
<!-- 查询表单 -->
<form id="queryForm">
    <a>工单id</a>
    <input type="text" id="taskId" placeholder="输入工单ID（示例：335588）">
    <a>问题id</a>
    <input type="text" id="problemId" placeholder="输入问题ID">
    <a>设计师域账户</a>
    <input type="text" id="designerAccount" placeholder="输入设计师域账户">
    <button type="submit" id="queryButton">
        <span id="loadingSpinner" class="loading" style="display: none;"></span>
        <span id="buttonText">查询</span>
    </button>
</form>

<!-- 结果列表 -->
<div id="resultList"></div>

<!-- 抽屉面板 -->
<div class="drawer" id="drawer">
    <!-- 左侧图表区域 -->
    <div class="drawer-charts">
        <!-- 统计摘要 -->
        <div class="stats-summary">
            <h4>统计摘要</h4>
            <div class="stat-item">
                <span>设计师总数:</span>
                <span class="stat-value" id="totalDesigners">-</span>
            </div>
            <div class="stat-item">
                <span>工单总数:</span>
                <span class="stat-value" id="totalTasks">-</span>
            </div>
            <div class="stat-item">
                <span>总额度:</span>
                <span class="stat-value" id="totalQuota">-</span>
            </div>
            <div class="stat-item">
                <span>已用额度:</span>
                <span class="stat-value" id="usedQuota">-</span>
            </div>
        </div>

        <!-- 设计师额度统计图 -->
        <div class="chart-container">
            <div class="chart-title">设计师占用额度统计</div>
            <div class="chart-canvas">
                <canvas id="quotaChart"></canvas>
            </div>
        </div>

        <!-- 设计师工单数量统计图 -->
        <div class="chart-container">
            <div class="chart-title">设计师工单数量统计</div>
            <div class="chart-canvas">
                <canvas id="taskCountChart"></canvas>
            </div>
        </div>
    </div>

    <!-- 右侧详情区域 -->
    <div class="drawer-details">
        <div class="drawer-header">
            <h2>分配详情</h2>
            <div class="drawer-buttons">
                <button class="export-btn" onclick="exportInputJson()">导出输入json</button>
                <button class="comparison-btn" onclick="openComparisonAnalysis()">分配对比分析</button>
                <button class="close-btn" onclick="closeDrawer()">关闭</button>
            </div>
        </div>
        <div class="search-box">
            <span>输入后回车搜索:</span> <input type="text" id="searchInput" placeholder="搜索设计师或任务编码"
                                                onkeyup="filterDetails()">
        </div>
        <div id="detailContent"></div>
    </div>
</div>

<script>
    // 环境配置
    const ENV_MAP = {
        'SIT环境': 'https://tas-sit.eainc.com',
        '本地环境': 'http://localhost:8080',
        '生产环境': 'https://tas-cnsha.eainc.com'
    };

    // 图表实例
    let quotaChart = null;
    let taskCountChart = null;

    // 当前问题模型ID
    let currentProblemId = null;

    // localStorage管理工具
    const StorageManager = {
        // 检查localStorage可用空间
        getStorageSize() {
            let total = 0;
            for (let key in localStorage) {
                if (localStorage.hasOwnProperty(key)) {
                    total += localStorage[key].length + key.length;
                }
            }
            return total;
        },

        // 清理旧的缓存数据
        cleanOldCache() {
            const keys = Object.keys(localStorage);
            const taskKeys = keys.filter(key => key.startsWith('task_'));

            // 如果缓存的任务超过10个，删除最旧的
            if (taskKeys.length > 10) {
                taskKeys.sort().slice(0, taskKeys.length - 10).forEach(key => {
                    localStorage.removeItem(key);
                });
            }
        },

        // 安全地设置localStorage
        safeSetItem(key, value) {
            try {
                // 先尝试直接设置
                localStorage.setItem(key, value);
                return true;
            } catch (e) {
                if (e.name === 'QuotaExceededError' || e.name === 'NS_ERROR_DOM_QUOTA_REACHED') {
                    console.warn('localStorage空间不足，正在清理缓存...');

                    // 清理旧缓存
                    this.cleanOldCache();

                    try {
                        // 再次尝试设置
                        localStorage.setItem(key, value);
                        return true;
                    } catch (e2) {
                        console.error('localStorage空间仍然不足，跳过缓存');
                        return false;
                    }
                } else {
                    console.error('localStorage设置失败:', e);
                    return false;
                }
            }
        },

        // 安全地获取localStorage
        safeGetItem(key) {
            try {
                return localStorage.getItem(key);
            } catch (e) {
                console.error('localStorage读取失败:', e);
                return null;
            }
        }
    };

    // 页面加载时检查URL参数并自动查询
    window.addEventListener('DOMContentLoaded', function () {
        // 页面加载时清理旧缓存
        StorageManager.cleanOldCache();

        // 显示存储使用情况（调试用）
        const storageSize = StorageManager.getStorageSize();
        console.log(`localStorage使用情况: ${(storageSize / 1024).toFixed(2)} KB`);

        // 初始化环境和版本信息
        updateCurrentEnvironment();

        const urlParams = new URLSearchParams(window.location.search);
        const problemId = urlParams.get('problemId');
        const taskId = urlParams.get('taskId');
        const designerAccount = urlParams.get('designerAccount');

        if (problemId) {
            // 设置problemId输入框的值
            document.getElementById('problemId').value = problemId;
            // 自动触发查询
            setTimeout(() => {
                document.getElementById('queryForm').dispatchEvent(new Event('submit'));
            }, 100);
        } else if (taskId) {
            // 设置taskId输入框的值
            document.getElementById('taskId').value = taskId;
            // 自动触发查询
            setTimeout(() => {
                document.getElementById('queryForm').dispatchEvent(new Event('submit'));
            }, 100);
        } else if (designerAccount) {
            // 设置designerAccount输入框的值
            document.getElementById('designerAccount').value = designerAccount;
            // 自动触发查询
            setTimeout(() => {
                document.getElementById('queryForm').dispatchEvent(new Event('submit'));
            }, 100);
        }
    });

    // Loading状态控制函数
    function showLoading() {
        document.getElementById('loadingSpinner').style.display = 'inline-block';
        document.getElementById('buttonText').textContent = '查询中...';
        document.getElementById('queryButton').disabled = true;
    }

    function hideLoading() {
        document.getElementById('loadingSpinner').style.display = 'none';
        document.getElementById('buttonText').textContent = '查询';
        document.getElementById('queryButton').disabled = false;
    }

    // 查询功能
    document.getElementById('queryForm').addEventListener('submit', async (e) => {
        e.preventDefault();
        const problemId = document.getElementById('problemId').value.trim();
        const taskId = document.getElementById('taskId').value.trim();
        const designerAccount = document.getElementById('designerAccount').value.trim();
        const baseUrl = document.getElementById('environment').value;

        // 显示loading状态
        showLoading();

        try {
            if (problemId) {
                // 清空其他输入框
                document.getElementById('taskId').value = '';
                document.getElementById('designerAccount').value = '';

                // 保存当前问题模型ID
                currentProblemId = problemId;

                // 显示加载状态
                document.getElementById('detailContent').innerHTML = '<p class="loading-text">加载中...</p>';
                openDrawer();

                const controller = new AbortController();
                const timeoutId = setTimeout(() => {
                    controller.abort();
                    document.getElementById('detailContent').innerHTML = '<p style="color:red">请求超时，请检查网络</p>';
                }, 1000000);

                const response = await fetch(`${baseUrl}/api/task-assignment/solution/detail/${problemId}`, {
                    signal: controller.signal,
                    method: 'GET',
                    headers: {'Content-Type': 'application/json'},
                    mode: 'cors'
                });
                clearTimeout(timeoutId);

                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const {data} = await response.json();
                renderDetails(data);

            } else if (taskId) {
                // 清空其他输入框
                document.getElementById('problemId').value = '';
                document.getElementById('designerAccount').value = '';

                const response = await fetch(`${baseUrl}/api/task-assignment/findProblemByTaskId/${taskId}`, {
                    method: 'GET',
                    headers: {'Content-Type': 'application/json'},
                    mode: 'cors'
                });

                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const {data} = await response.json();
                renderResults(data, 'taskId');

            } else if (designerAccount) {
                // 清空其他输入框
                document.getElementById('problemId').value = '';
                document.getElementById('taskId').value = '';

                const response = await fetch(`${baseUrl}/api/task-assignment/findProblemByAccount/${designerAccount}`, {
                    method: 'GET',
                    headers: {'Content-Type': 'application/json'},
                    mode: 'cors'
                });

                if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
                const {data} = await response.json();
                renderResults(data, 'designerAccount');

            } else {
                alert('请输入工单ID、问题ID或设计师域账户中的任意一个');
            }
        } catch (error) {
            if (problemId) {
                document.getElementById('detailContent').innerHTML = `<p style="color:red">加载失败：${error.message}</p>`;
            } else {
                alert('查询失败: ' + error.message);
            }
        } finally {
            // 隐藏loading状态
            hideLoading();
        }
    });

    // 渲染结果
    function renderResults(data, queryType) {
        const container = document.getElementById('resultList');

        // 检查数据是否为空
        if (!data || !Array.isArray(data) || data.length === 0) {
            const queryTypeText = {
                'taskId': '工单ID',
                'designerAccount': '设计师域账户'
            };

            container.innerHTML = `
                <div class="empty-result">
                    <div class="icon">📭</div>
                    <div class="title">未找到相关数据</div>
                    <div class="description">
                        ${queryType ? `没有找到与该${queryTypeText[queryType] || '查询条件'}相关的问题模型` : '查询结果为空'}
                        <br>请检查输入的信息是否正确，或尝试其他查询条件
                    </div>
                </div>
            `;
            return;
        }

        container.innerHTML = data.map(item => `
            <div class="task-item" data-id="${item.id}">
                <h3>问题模型 ${item.code}</h3>
                <p>分配策略：${item.solverName}</p>
                <p>客户端：${item.client}</p>
                <p>评分：${item.score || '无'}</p>
            </div>
        `).join('');

        // 绑定点击事件
        document.querySelectorAll('.task-item').forEach(item => {
            item.addEventListener('click', async () => {
                const baseUrl = document.getElementById('environment').value;
                const taskId = item.dataset.id;
                const cacheKey = `task_${taskId}`;
                const cachedData = StorageManager.safeGetItem(cacheKey);

                // 保存当前问题模型ID
                currentProblemId = taskId;

                // 显示加载状态
                document.getElementById('detailContent').innerHTML = '<p class="loading-text"><span class="loading"></span>加载中...</p>';
                openDrawer();

                if (cachedData) {
                    try {
                        renderDetails(JSON.parse(cachedData));
                    } catch (e) {
                        console.error('缓存数据解析失败:', e);
                        // 删除损坏的缓存数据
                        localStorage.removeItem(cacheKey);
                        // 重新加载数据
                        loadTaskData(baseUrl, taskId, cacheKey);
                    }
                } else {
                    loadTaskData(baseUrl, taskId, cacheKey);
                }
            });
        });
    }

    // 提取数据加载逻辑
    async function loadTaskData(baseUrl, taskId, cacheKey) {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                controller.abort();
                document.getElementById('detailContent').innerHTML = '<p style="color:red">请求超时，请检查网络</p>';
            }, 1000000);

            const response = await fetch(`${baseUrl}/api/task-assignment/solution/detail/${taskId}`, {
                signal: controller.signal,
                method: 'GET',
                headers: {'Content-Type': 'application/json'},
                mode: 'cors'
            });
            clearTimeout(timeoutId);

            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const {data} = await response.json();

            // 使用安全的存储方法
            const dataString = JSON.stringify(data);
            const saved = StorageManager.safeSetItem(cacheKey, dataString);

            if (!saved) {
                console.warn(`数据过大，无法缓存任务 ${taskId}`);
            }

            renderDetails(data);
        } catch (error) {
            document.getElementById('detailContent').innerHTML =
                `<p style="color:red">加载失败：${error.message}</p>`;
        }
    }

    // 抽屉交互
    function openDrawer(data) {
        const drawer = document.getElementById('drawer');
        drawer.classList.add('active');
        if (data) {
            renderDetails(data);
            updateCharts(data);
        }
    }

    function closeDrawer() {
        document.getElementById('drawer').classList.remove('active');
    }

    // 导出输入json
    function exportInputJson() {
        if (!currentProblemId) {
            alert('没有选择问题模型，无法导出');
            return;
        }

        const baseUrl = document.getElementById('environment').value;
        const exportUrl = `${baseUrl}/api/task-assignment/problem/${currentProblemId}`;

        // 在新标签页中打开URL
        window.open(exportUrl, '_blank');
    }

    // 获取系统版本信息
    async function fetchSystemVersion() {
        try {
            const baseUrl = document.getElementById('environment').value;
            const response = await fetch(`${baseUrl}/api/version`, {
                method: 'GET',
                mode: 'cors'
            });

            if (response.ok) {
                const version = await response.text();
                document.getElementById('systemVersion').textContent = version;
            } else {
                document.getElementById('systemVersion').textContent = '获取失败';
            }
        } catch (error) {
            console.error('获取版本信息失败:', error);
            document.getElementById('systemVersion').textContent = '获取失败';
        }
    }

    // 更新当前环境显示
    function updateCurrentEnvironment() {
        const environmentSelect = document.getElementById('environment');
        const selectedOption = environmentSelect.options[environmentSelect.selectedIndex];
        document.getElementById('currentEnvironment').textContent = selectedOption.text;

        // 环境切换时重新获取版本信息
        fetchSystemVersion();
    }

    // 打开分配对比分析页面
    function openComparisonAnalysis() {
        if (!currentProblemId) {
            alert('请先选择一个问题模型');
            return;
        }

        const baseUrl = document.getElementById('environment').value;
        const comparisonUrl = `comparison.html?problemId=${currentProblemId}&baseUrl=${encodeURIComponent(baseUrl)}`;

        // 在新标签页中打开对比分析页面
        window.open(comparisonUrl, '_blank');
    }

    // 渲染详情
    function renderDetails(data) {
        const container = document.getElementById('detailContent');

        // 增强数据校验
        if (!data?.result?.length) {
            // 检查是否是真正的空数据还是加载中
            if (data && data.result && Array.isArray(data.result) && data.result.length === 0) {
                container.innerHTML = `
                    <div class="empty-result">
                        <div class="icon">📋</div>
                        <div class="title">暂无分配详情</div>
                        <div class="description">
                            该问题模型暂时没有设计师分配信息
                            <br>可能是数据还在处理中或者没有可分配的设计师
                        </div>
                    </div>
                `;
            } else {
                container.innerHTML = '<p class="loading-text"><span class="loading"></span>数据加载中,请稍等...</p>';
            }
            return;
        }

        // 更新图表
        updateCharts(data);

        container.innerHTML = `
            <div class="designer-list">
                ${data.result.map(designer => {
            const total = designer.designerCapacity || 0;
            const remain = designer.capacityRemain || 0;
            const used = total - remain;


            return `
                    <div class="designer-card">
                        <div class="designer-header" onclick="this.nextElementSibling.classList.toggle('active')">

                            <h4>设计师: ${designer.designerCode} 日额度: ${designer.dailyCapacity || 0} 超额上限: ${designer.designerCapacity || 0} 剩余产能: ${designer.capacityRemain || 0} 设计师职级: ${designer.designerLevel || '未知'}</h3>
                            <div class="capacity-progress">
                                <div class="capacity-filled"
                                     style="width: ${(used / total * 100 || 0)}%">
                                </div>

                            </div>
                                 <div>
                                <span>拥有的技能: ${designer.skill || '无指定技能'}</span>
                            </div>
                                     <div>
                                <span></span>
                            </div>
                               </br>
                              <div>
                                <span>偏好工单表达式: ${designer.preferTask || '无'}</span>
                            </div>
                            </br>
                                             <div>
                                 <span>偏好工单数量: ${designer.preferTaskCount || '0'}</span>
                            </div>
                               </br>
                                                       <div>
                                 <span>偏好工单额度: ${designer.preferTaskQuota || '0'}</span>
                            </div>
                               </br>
                                       <div>
                                <span>偏好技能表达式: ${designer.preferSkill || '无'}</span>
                            </div>

                               </br>
                                                                   <div>
                                 <span>偏好技能数量: ${designer.preferSkillCount || '0'}</span>
                            </div>
                               </br>
                                                                         <div>
                                 <span>偏好技能额度: ${designer.preferSkillQuota || '0'}</span>
                            </div>
                               </br>
                                       <div>
                                <span>全设计修改只分自己: ${designer.modifyDesignExclusive || '否'}</span>
                            </div>
                               </br>
                            <div>
                                <span>已用产能：${used}/${total}</span>
                            </div>
                               </br>
                              <div>
                                <span>在岗：${designer.onDuty | '不在岗'}</span>
                            </div>
                        </div>

                        <table class="task-table">
                            <thead>
                                <tr>
                                    <th>工单编码</th>
                                     <th>工单类型</th>
                                    <th>所需产能</th>
                                    <th>所需技能</th>
                                    <th>病例分配设计师</th>
                                     <th>该设计师的偏好工单</th>
                                      <th>该设计师的偏好技能</th>

                                </tr>
                            </thead>
                            <tbody>
                                ${(designer.assignedTask || []).map(task => `
                                <tr>
    <td>${task.taskCode || ''}</td>
    <td>${task.taskType || ''}</td>
    <td>${task.taskRequiredCapacityCount}个/${task.taskRequiredCapacityMinute}分钟</td>
    <td>${task.taskRequireSkill || '无'}</td>
    <td>${task.preferDesignerCode || '无'}</td>
    <td>${task.preferTask ? '是' : '否'}</td>
   <td>${task.preferSkill ? '是' : '否'}</td>
</tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>
                    `;
        }).join('')}
            </div>
        `;
    }

    // 搜索过滤
    function filterDetails() {
        const keyword = document.getElementById('searchInput').value.toLowerCase();
        const cards = document.querySelectorAll('.designer-card');

        cards.forEach(card => {
            const text = card.textContent.toLowerCase();
            card.style.display = text.includes(keyword) ? 'block' : 'none';
        });
    }

    // 检查Chart.js是否加载成功
    function isChartJsAvailable() {
        return typeof Chart !== 'undefined';
    }

    // 更新图表和统计数据
    function updateCharts(data) {
        if (!data?.result?.length) return;

        const designers = data.result;

        // 计算统计数据
        const totalDesigners = designers.length;
        let totalTasks = 0;
        let totalQuota = 0;
        let usedQuota = 0;

        const designerNames = [];
        const designerQuotas = [];
        const designerTaskCounts = [];

        designers.forEach(designer => {
            const designerCapacity = designer.designerCapacity || 0;
            const capacityRemain = designer.capacityRemain || 0;
            const used = designerCapacity - capacityRemain;
            const taskCount = designer.assignedTask ? designer.assignedTask.length : 0;

            designerNames.push(designer.designerCode || '未知');
            designerQuotas.push(used);
            designerTaskCounts.push(taskCount);

            totalTasks += taskCount;
            totalQuota += designerCapacity;
            usedQuota += used;
        });

        // 更新统计摘要
        document.getElementById('totalDesigners').textContent = totalDesigners;
        document.getElementById('totalTasks').textContent = totalTasks;
        document.getElementById('totalQuota').textContent = totalQuota.toFixed(1);
        document.getElementById('usedQuota').textContent = usedQuota.toFixed(1);

        // 创建或更新图表
        if (isChartJsAvailable()) {
            // 使用Chart.js创建图表
            createQuotaChart(designerNames, designerQuotas);
            createTaskCountChart(designerNames, designerTaskCounts);
        } else {
            // 使用HTML创建简单图表
            createSimpleQuotaChart(designerNames, designerQuotas);
            createSimpleTaskCountChart(designerNames, designerTaskCounts);
        }
    }

    // 创建设计师额度统计图
    function createQuotaChart(labels, data) {
        const ctx = document.getElementById('quotaChart').getContext('2d');

        if (quotaChart) {
            quotaChart.destroy();
        }

        quotaChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '占用额度',
                    data: data,
                    backgroundColor: 'rgba(54, 162, 235, 0.8)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '额度'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '设计师'
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    }
                }
            }
        });
    }

    // 创建设计师工单数量统计图
    function createTaskCountChart(labels, data) {
        const ctx = document.getElementById('taskCountChart').getContext('2d');

        if (taskCountChart) {
            taskCountChart.destroy();
        }

        taskCountChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: labels,
                datasets: [{
                    label: '工单数量',
                    data: data,
                    backgroundColor: 'rgba(255, 99, 132, 0.8)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        title: {
                            display: true,
                            text: '工单数量'
                        }
                    },
                    x: {
                        title: {
                            display: true,
                            text: '设计师'
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    }
                }
            }
        });
    }

    // 简单HTML图表 - 额度统计（备选方案）
    function createSimpleQuotaChart(labels, data) {
        const container = document.getElementById('quotaChart').parentElement;
        const maxValue = Math.max(...data);

        container.innerHTML = `
            <div class="chart-title">设计师占用额度统计</div>
            <div style="padding: 10px;">
                ${labels.map((label, index) => {
            const value = data[index];
            const percentage = maxValue > 0 ? (value / maxValue) * 100 : 0;
            return `
                        <div class="simple-chart-item">
                            <div class="simple-chart-label">
                                <span>${label}</span>
                                <span class="simple-chart-value">${value.toFixed(1)}</span>
                            </div>
                            <div class="simple-chart-bar">
                                <div class="simple-chart-fill" style="background: rgba(54, 162, 235, 0.8); width: ${percentage}%;"></div>
                            </div>
                        </div>
                    `;
        }).join('')}
            </div>
        `;
    }

    // 简单HTML图表 - 工单数量统计（备选方案）
    function createSimpleTaskCountChart(labels, data) {
        const container = document.getElementById('taskCountChart').parentElement;
        const maxValue = Math.max(...data);

        container.innerHTML = `
            <div class="chart-title">设计师工单数量统计</div>
            <div style="padding: 10px;">
                ${labels.map((label, index) => {
            const value = data[index];
            const percentage = maxValue > 0 ? (value / maxValue) * 100 : 0;
            return `
                        <div class="simple-chart-item">
                            <div class="simple-chart-label">
                                <span>${label}</span>
                                <span class="simple-chart-value">${value}</span>
                            </div>
                            <div class="simple-chart-bar">
                                <div class="simple-chart-fill" style="background: rgba(255, 99, 132, 0.8); width: ${percentage}%;"></div>
                            </div>
                        </div>
                    `;
        }).join('')}
            </div>
        `;
    }

    // 页面加载完成后检查Chart.js状态
    window.addEventListener('load', function () {
        setTimeout(() => {
            if (!isChartJsAvailable()) {
                console.warn('Chart.js未能加载，将使用简单HTML图表');
            }
        }, 1000);
    });
</script>

<!-- 页脚 -->
<footer class="footer">
    <div class="footer-content">
        <span>当前环境：<span id="currentEnvironment">-</span></span>
        <span class="separator">|</span>
        <span>系统版本：<span id="systemVersion">加载中...</span></span>
    </div>
</footer>

</body>

</html>
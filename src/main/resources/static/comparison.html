<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工单分配对比分析</title>
    <script src="https://unpkg.com/chart.js@4.4.0/dist/chart.umd.js"
            onerror="this.onerror=null; this.src='https://cdnjs.cloudflare.com/ajax/libs/Chart.js/4.4.0/chart.umd.js'"></script>

    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .header {
            background: white;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            color: #1890ff;
            margin-bottom: 10px;
        }

        .header-info {
            display: flex;
            gap: 20px;
            color: #666;
            font-size: 14px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 0 20px;
        }

        .loading {
            text-align: center;
            padding: 50px;
            font-size: 16px;
            color: #666;
        }

        .error {
            background: #fff2f0;
            border: 1px solid #ffccc7;
            color: #ff4d4f;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
        }

        .comparison-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .card {
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .card h3 {
            margin-bottom: 15px;
            color: #1890ff;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 10px;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .metric-item {
            background: #fafafa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #1890ff;
        }

        .metric-label {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
        }

        .metric-value {
            font-size: 18px;
            font-weight: bold;
            color: #333;
        }

        .improvement {
            color: #52c41a;
        }

        .degradation {
            color: #ff4d4f;
        }

        .chart-container {
            position: relative;
            height: 300px;
            margin: 20px 0;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        .comparison-table th,
        .comparison-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        .comparison-table th {
            background: #fafafa;
            font-weight: 600;
        }

        .comparison-table tr:hover {
            background: #f5f5f5;
        }

        .score-display {
            font-family: 'Courier New', monospace;
            background: #f6f8fa;
            padding: 10px;
            border-radius: 4px;
            border-left: 4px solid #1890ff;
            margin: 10px 0;
        }

        .actions {
            text-align: center;
            margin: 30px 0;
        }

        .btn {
            padding: 10px 20px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 0 10px;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #1890ff;
            color: white;
        }

        .btn-primary:hover {
            background: #40a9ff;
        }

        .btn-secondary {
            background: #f0f0f0;
            color: #333;
        }

        .btn-secondary:hover {
            background: #d9d9d9;
        }

        .summary-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin-bottom: 20px;
        }

        .summary-card h3 {
            color: white;
            border-bottom-color: rgba(255, 255, 255, 0.3);
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .summary-stat {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 6px;
        }

        .summary-stat-value {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .summary-stat-label {
            font-size: 12px;
            opacity: 0.9;
        }

        .task-filter {
            display: flex;
            gap: 15px;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: #fafafa;
            border-radius: 6px;
        }

        .task-filter input,
        .task-filter select {
            padding: 8px 12px;
            border: 1px solid #d9d9d9;
            border-radius: 4px;
            font-size: 14px;
        }

        .task-filter input {
            flex: 1;
            min-width: 200px;
        }

        .task-count {
            color: #666;
            font-size: 14px;
            margin-left: auto;
        }

        .task-table-container {
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #f0f0f0;
            border-radius: 6px;
        }

        .task-detail-table {
            margin: 0;
        }

        .task-detail-table th {
            position: sticky;
            top: 0;
            background: white;
            z-index: 10;
            box-shadow: 0 2px 2px rgba(0, 0, 0, 0.1);
        }

        .change-status {
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: bold;
        }

        .change-status.changed {
            background: #fff2e8;
            color: #fa8c16;
        }

        .change-status.unchanged {
            background: #f6ffed;
            color: #52c41a;
        }

        .designer-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .designer-name {
            font-weight: bold;
            color: #1890ff;
        }

        .designer-details {
            font-size: 12px;
            color: #666;
        }

        .task-code {
            font-family: 'Courier New', monospace;
            font-weight: bold;
            color: #1890ff;
        }

        .priority-badge {
            padding: 2px 6px;
            border-radius: 10px;
            font-size: 11px;
            font-weight: bold;
        }

        .priority-high {
            background: #fff1f0;
            color: #ff4d4f;
        }

        .priority-medium {
            background: #fff7e6;
            color: #fa8c16;
        }

        .priority-low {
            background: #f6ffed;
            color: #52c41a;
        }

        .detail-btn {
            padding: 4px 8px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }

        .detail-btn:hover {
            background: #40a9ff;
        }

        .metric-trend {
            margin-left: 8px;
            font-size: 16px;
            font-weight: bold;
        }

        .trend-up {
            color: #52c41a;
        }

        .trend-down {
            color: #ff4d4f;
        }

        .trend-equal {
            color: #666;
        }

        /* 气泡提示样式 */
        .tooltip {
            position: relative;
            cursor: help;
        }

        .tooltip .tooltiptext {
            visibility: hidden;
            min-width: 300px;
            max-width: 800px;
            width: max-content;
            background-color: #333;
            color: #fff;
            text-align: left;
            border-radius: 6px;
            padding: 15px;
            position: absolute;
            z-index: 1000;
            bottom: 125%;
            left: 50%;
            transform: translateX(-50%);
            opacity: 0;
            transition: opacity 0.3s;
            font-size: 12px;
            line-height: 1.4;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
            white-space: nowrap;
        }

        .tooltip .tooltiptext::after {
            content: "";
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            border-width: 5px;
            border-style: solid;
            border-color: #333 transparent transparent transparent;
        }

        .tooltip:hover .tooltiptext {
            visibility: visible;
            opacity: 1;
        }

        .score-detail {
            margin: 5px 0;
            padding: 3px 0;
            border-bottom: 1px solid #555;
        }

        .score-detail:last-child {
            border-bottom: none;
        }

        .score-label {
            font-weight: bold;
            color: #ffd700;
        }

        .score-value {
            font-family: 'Courier New', monospace;
        }

        .score-change {
            margin-left: 10px;
            font-weight: bold;
        }

        .score-change.positive {
            color: #52c41a;
        }

        .score-change.negative {
            color: #ff4d4f;
        }
    </style>
</head>
<body>
<div class="header">
    <div class="container">
        <h1>工单分配对比分析</h1>
        <div class="header-info">
            <span>问题ID: <span id="problemId">-</span></span>
            <span>分析时间: <span id="analysisTime">-</span></span>
            <span>环境: <span id="environment">-</span></span>
        </div>
    </div>
</div>

<div class="container">
    <div id="loading" class="loading">
        正在加载对比分析数据...
    </div>

    <div id="error" class="error" style="display: none;">
        <strong>加载失败：</strong>
        <span id="errorMessage"></span>
    </div>

    <div id="content" style="display: none;">
        <!-- 总体对比摘要 -->
        <div class="card summary-card full-width">
            <h3>对比摘要</h3>
            <div class="summary-stats">
                <div class="summary-stat">
                    <div class="summary-stat-value" id="overallImprovement">-</div>
                    <div class="summary-stat-label">整体改进度</div>
                </div>
                <div class="summary-stat tooltip">
                    <div class="summary-stat-value" id="scoreImprovement">-</div>
                    <div class="summary-stat-label">分数改进</div>
                    <span class="tooltiptext" id="scoreTooltip">
                        <div class="score-detail">
                            <span class="score-label">分数对比:</span><br>
                            <span id="scoreComparisonDetail" style="font-family: 'Courier New', monospace;">加载中...</span>
                        </div>
                    </span>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value" id="balanceImprovement">-</div>
                    <div class="summary-stat-label">平衡度改进</div>
                </div>
                <div class="summary-stat">
                    <div class="summary-stat-value" id="efficiencyImprovement">-</div>
                    <div class="summary-stat-label">效率改进</div>
                </div>
            </div>
        </div>

        <!-- 详细对比数据 -->
        <div class="comparison-grid">
            <!-- 原始分配指标 -->
            <div class="card">
                <h3>原始分配指标</h3>
                <div id="originalMetrics" class="metrics-grid">
                    <!-- 动态生成 -->
                </div>
            </div>

            <!-- 调整后分配指标 -->
            <div class="card">
                <h3>调整后分配指标</h3>
                <div id="adjustedMetrics" class="metrics-grid">
                    <!-- 动态生成 -->
                </div>
            </div>
        </div>

        <!-- 改进详情 -->
        <div class="card full-width">
            <h3>改进详情</h3>
            <div class="metrics-grid">
                <div class="metric-item">
                    <div class="metric-label">工作量平衡度改进</div>
                    <div class="metric-value" id="workloadBalanceDetail">-</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">效率改进</div>
                    <div class="metric-value" id="efficiencyDetail">-</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">公平性改进</div>
                    <div class="metric-value" id="fairnessDetail">-</div>
                </div>
                <div class="metric-item">
                    <div class="metric-label">偏好技能改进</div>
                    <div class="metric-value" id="preferredSkillDetail">-</div>
                </div>
            </div>
        </div>

        <!-- 图表展示 -->
        <div class="card full-width">
            <h3>可视化对比</h3>
            <div class="chart-container">
                <canvas id="comparisonChart"></canvas>
            </div>
        </div>

        <!-- 工单分配详情对比 -->
        <div class="card full-width">
            <h3>工单分配详情对比</h3>
            <div class="task-filter">
                <input type="text" id="taskSearch" placeholder="搜索工单编号、设计师姓名..." onkeyup="filterTasks()">
                <select id="changeTypeFilter" onchange="filterTasks()">
                    <option value="">所有变更</option>
                    <option value="changed">有变更</option>
                    <option value="unchanged">无变更</option>
                </select>
                <span class="task-count">共 <span id="taskCount">0</span> 个工单</span>
            </div>
            <div class="task-table-container">
                <table class="comparison-table task-detail-table">
                    <thead>
                    <tr>
                        <th>工单编号</th>
                        <th>工单类型</th>
                        <th>工时</th>
                        <th>原始负责人</th>
                        <th>调整后负责人</th>
                        <th>变更状态</th>
                        <th>操作</th>
                    </tr>
                    </thead>
                    <tbody id="taskDetailComparison">
                    <!-- 动态生成 -->
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 工单类型分布对比 -->
        <div class="card full-width">
            <h3>工单类型分布对比</h3>
            <table class="comparison-table">
                <thead>
                <tr>
                    <th>工单类型</th>
                    <th>原始分配方差</th>
                    <th>调整后方差</th>
                    <th>改进情况</th>
                    <th>总数量变化</th>
                    <th>分布描述</th>
                </tr>
                </thead>
                <tbody id="taskTypeComparison">
                <!-- 动态生成 -->
                </tbody>
            </table>
        </div>
    </div>

    <div class="actions">
        <button class="btn btn-primary" onclick="refreshAnalysis()">刷新分析</button>
        <button class="btn btn-secondary" onclick="exportReport()">导出报告</button>
        <button class="btn btn-secondary" onclick="debugData()">调试数据</button>
        <button class="btn btn-secondary" onclick="window.close()">关闭窗口</button>
    </div>
</div>

<script>
    let problemId = '';
    let baseUrl = '';
    let comparisonData = null;

    // 等待Chart.js加载完成的函数
    function waitForChart(callback, maxAttempts = 50) {
        let attempts = 0;
        const checkChart = () => {
            attempts++;
            if (typeof Chart !== 'undefined') {
                console.log('Chart.js加载完成');
                callback();
            } else if (attempts < maxAttempts) {
                setTimeout(checkChart, 100);
            } else {
                console.error('Chart.js加载超时');
                callback(); // 即使Chart.js未加载也继续执行
            }
        };
        checkChart();
    }

    // 页面加载时初始化
    window.addEventListener('DOMContentLoaded', function () {
        waitForChart(() => {
            initializePage();
        });
    });

    function initializePage() {
        // 获取URL参数
        const urlParams = new URLSearchParams(window.location.search);
        problemId = urlParams.get('problemId');
        baseUrl = urlParams.get('baseUrl');

        if (!problemId || !baseUrl) {
            showError('缺少必要的参数：problemId 或 baseUrl');
            return;
        }

        // 更新页面信息
        document.getElementById('problemId').textContent = problemId;
        document.getElementById('analysisTime').textContent = new Date().toLocaleString();
        document.getElementById('environment').textContent = baseUrl;

        // 加载对比数据
        loadComparisonData();
    }

    async function loadComparisonData() {
        try {
            showLoading();

            const response = await fetch(`${baseUrl}/api/task-assignment/analysis/compare/${problemId}`, {
                method: 'GET',
                mode: 'cors'
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            if (result.success) {
                comparisonData = result.data;
                displayComparisonData(comparisonData);
            } else {
                throw new Error(result.message || '获取数据失败');
            }

        } catch (error) {
            console.error('加载对比数据失败:', error);
            showError('加载对比数据失败: ' + error.message);
        }
    }

    function displayComparisonData(data) {
        hideLoading();
        document.getElementById('content').style.display = 'block';

        console.log('Comparison data:', data);
        console.log('Original metrics:', data.originalMetrics);
        console.log('Adjusted metrics:', data.adjustedMetrics);

        // 显示总体摘要
        displaySummary(data);

        // 显示详细指标
        displayMetrics(data.originalMetrics, 'originalMetrics');
        displayMetrics(data.adjustedMetrics, 'adjustedMetrics', data.originalMetrics);

        // 显示改进详情
        displayImprovementDetails(data.improvements);

        // 显示图表
        displayChart(data);

        // 显示工单详情对比
        displayTaskDetailComparison(data);

        // 显示工单类型对比
        displayTaskTypeComparison(data);
    }

    function displaySummary(data) {
        const improvements = data.improvements || {};

        // 整体改进状态
        const overallElement = document.getElementById('overallImprovement');
        if (improvements.hasOverallImprovement) {
            overallElement.innerHTML = '<span class="trend-up">↗</span> 有改进';
            overallElement.style.color = '#52c41a';
        } else {
            overallElement.innerHTML = '<span class="trend-down">↘</span> 无改进';
            overallElement.style.color = '#ff4d4f';
        }

        // 计算综合改进分数
        const improvementScore = improvements.improvementScore || 0;
        const scoreElement = document.getElementById('scoreImprovement');
        if (improvementScore > 0) {
            scoreElement.innerHTML = `<span class="trend-up">↗</span> +${improvementScore.toFixed(2)*100}%`;
            scoreElement.style.color = '#52c41a';
        } else if (improvementScore < 0) {
            scoreElement.innerHTML = `<span class="trend-down">↘</span> ${improvementScore.toFixed(2)*100}%`;
            scoreElement.style.color = '#ff4d4f';
        } else {
            scoreElement.innerHTML = `<span class="trend-equal">→</span> ${improvementScore.toFixed(2)*100}%`;
            scoreElement.style.color = '#666';
        }

        // 更新分数tooltip内容
        updateScoreTooltip(data);

        // 平衡度改进
        const balanceImprovement = improvements.workloadBalanceImprovement || 0;
        const balanceElement = document.getElementById('balanceImprovement');
        if (balanceImprovement > 0) {
            balanceElement.innerHTML = `<span class="trend-up">↗</span> +${(balanceImprovement * 100).toFixed(2)}%`;
            balanceElement.style.color = '#52c41a';
        } else if (balanceImprovement < 0) {
            balanceElement.innerHTML = `<span class="trend-down">↘</span> ${(balanceImprovement * 100).toFixed(2)}%`;
            balanceElement.style.color = '#ff4d4f';
        } else {
            balanceElement.innerHTML = `<span class="trend-equal">→</span> 0%`;
            balanceElement.style.color = '#666';
        }

        // 效率改进
        const efficiencyImprovement = improvements.efficiencyImprovement || 0;
        const efficiencyElement = document.getElementById('efficiencyImprovement');
        if (efficiencyImprovement > 0) {
            efficiencyElement.innerHTML = `<span class="trend-up">↗</span> +${(efficiencyImprovement * 100).toFixed(2)}%`;
            efficiencyElement.style.color = '#52c41a';
        } else if (efficiencyImprovement < 0) {
            efficiencyElement.innerHTML = `<span class="trend-down">↘</span> ${(efficiencyImprovement * 100).toFixed(2)}%`;
            efficiencyElement.style.color = '#ff4d4f';
        } else {
            efficiencyElement.innerHTML = `<span class="trend-equal">→</span> 0%`;
            efficiencyElement.style.color = '#666';
        }
    }

    function updateScoreTooltip(data) {
        const originalScore = data.originalMetrics?.score;
        const adjustedScore = data.adjustedMetrics?.score;

        // 只更新对比详情
        const comparisonElement = document.getElementById('scoreComparisonDetail');
        if (originalScore && adjustedScore) {
            if (originalScore === adjustedScore) {
                comparisonElement.innerHTML = '<span style="color: #666;">分数无变化</span>';
            } else {
                comparisonElement.innerHTML = `
                    <div style="margin-bottom: 8px;">
                        <span style="color: #ffd700;">原始:</span> ${originalScore}
                    </div>
                    <div>
                        <span style="color: #87ceeb;">调整:</span> ${adjustedScore}
                    </div>
                `;
            }
        } else {
            comparisonElement.textContent = '无法对比';
        }
    }



    function displayMetrics(metrics, containerId, originalMetrics = null) {
        const container = document.getElementById(containerId);
        container.innerHTML = '';

        const metricsToShow = [
            {
                key: 'sameRankWorkHoursVariance',
                label: '相同职级工时方差',
                format: (v) => v?.toFixed(4),
                higherIsBetter: false
            },
            {
                key: 'sameQuotaDesignerWorkHoursVariance',
                label: '相同额度设计师工时方差',
                format: (v) => v?.toFixed(4),
                higherIsBetter: false
            },
            {
                key: 'preferredSkillAchievementRatio',
                label: '工单偏好技能达成比例',
                format: (v) => `${(v * 100)?.toFixed(1)}%`,
                higherIsBetter: true
            },
            {
                key: 'designerPreferredSkillCoverageRatio',
                label: '设计师偏好技能覆盖率',
                format: (v) => `${(v * 100)?.toFixed(1)}%`,
                higherIsBetter: true
            },
            {
                key: 'overallWorkloadBalance',
                label: '整体工作量平衡度',
                format: (v) => v?.toFixed(4),
                higherIsBetter: true
            },
            {
                key: 'allocationEfficiencyIndex',
                label: '计算分配效率指数',
                format: (v) => v?.toFixed(4),
                higherIsBetter: true
            },
            {
                key: 'fairnessIndex',
                label: '公平性指数',
                format: (v) => v?.toFixed(4),
                higherIsBetter: true
            }
        ];

        metricsToShow.forEach(metric => {
            const value = metrics[metric.key];
            const metricDiv = document.createElement('div');
            metricDiv.className = 'metric-item';

            let trendIndicator = '';
            if (originalMetrics && value !== undefined && originalMetrics[metric.key] !== undefined) {
                const originalValue = originalMetrics[metric.key];
                const currentValue = value;

                if (Math.abs(currentValue - originalValue) < 0.0001) {
                    // 值基本相等
                    trendIndicator = '<span class="metric-trend trend-equal">→</span>';
                } else if (currentValue > originalValue) {
                    // 当前值更大
                    if (metric.higherIsBetter) {
                        trendIndicator = '<span class="metric-trend trend-up">↗</span>';
                    } else {
                        trendIndicator = '<span class="metric-trend trend-down">↗</span>';
                    }
                } else {
                    // 当前值更小
                    if (metric.higherIsBetter) {
                        trendIndicator = '<span class="metric-trend trend-down">↘</span>';
                    } else {
                        trendIndicator = '<span class="metric-trend trend-up">↘</span>';
                    }
                }
            }

            metricDiv.innerHTML = `
                    <div class="metric-label">${metric.label}</div>
                    <div class="metric-value">
                        ${value !== undefined ? metric.format(value) : '-'}
                        ${trendIndicator}
                    </div>
                `;
            container.appendChild(metricDiv);
        });
    }

    function displayImprovementDetails(improvements) {
        if (!improvements) return;

        // 工作量平衡度改进
        const workloadImprovement = improvements.workloadBalanceImprovement || 0;
        const workloadElement = document.getElementById('workloadBalanceDetail');
        if (workloadImprovement > 0) {
            workloadElement.innerHTML = `<span class="trend-up">↗</span> +${(workloadImprovement * 100).toFixed(2)}%`;
            workloadElement.style.color = '#52c41a';
        } else if (workloadImprovement < 0) {
            workloadElement.innerHTML = `<span class="trend-down">↘</span> ${(workloadImprovement * 100).toFixed(2)}%`;
            workloadElement.style.color = '#ff4d4f';
        } else {
            workloadElement.innerHTML = `<span class="trend-equal">→</span> 0%`;
            workloadElement.style.color = '#666';
        }

        // 效率改进
        const efficiencyImprovement = improvements.efficiencyImprovement || 0;
        const efficiencyElement = document.getElementById('efficiencyDetail');
        if (efficiencyImprovement > 0) {
            efficiencyElement.innerHTML = `<span class="trend-up">↗</span> +${(efficiencyImprovement * 100).toFixed(2)}%`;
            efficiencyElement.style.color = '#52c41a';
        } else if (efficiencyImprovement < 0) {
            efficiencyElement.innerHTML = `<span class="trend-down">↘</span> ${(efficiencyImprovement * 100).toFixed(2)}%`;
            efficiencyElement.style.color = '#ff4d4f';
        } else {
            efficiencyElement.innerHTML = `<span class="trend-equal">→</span> 0%`;
            efficiencyElement.style.color = '#666';
        }

        // 公平性改进
        const fairnessImprovement = improvements.fairnessImprovement || 0;
        const fairnessElement = document.getElementById('fairnessDetail');
        if (fairnessImprovement > 0) {
            fairnessElement.innerHTML = `<span class="trend-up">↗</span> +${(fairnessImprovement * 100).toFixed(2)}%`;
            fairnessElement.style.color = '#52c41a';
        } else if (fairnessImprovement < 0) {
            fairnessElement.innerHTML = `<span class="trend-down">↘</span> ${(fairnessImprovement * 100).toFixed(2)}%`;
            fairnessElement.style.color = '#ff4d4f';
        } else {
            fairnessElement.innerHTML = `<span class="trend-equal">→</span> 0%`;
            fairnessElement.style.color = '#666';
        }

        // 偏好技能改进
        const preferredSkillImprovement = improvements.preferredSkillImprovement || 0;
        const preferredSkillElement = document.getElementById('preferredSkillDetail');
        if (preferredSkillImprovement > 0) {
            preferredSkillElement.innerHTML = `<span class="trend-up">↗</span> +${(preferredSkillImprovement * 100).toFixed(2)}%`;
            preferredSkillElement.style.color = '#52c41a';
        } else if (preferredSkillImprovement < 0) {
            preferredSkillElement.innerHTML = `<span class="trend-down">↘</span> ${(preferredSkillImprovement * 100).toFixed(2)}%`;
            preferredSkillElement.style.color = '#ff4d4f';
        } else {
            preferredSkillElement.innerHTML = `<span class="trend-equal">→</span> 0%`;
            preferredSkillElement.style.color = '#666';
        }
    }

    function displayChart(data) {
        // 检查Chart.js是否可用
        if (typeof Chart === 'undefined') {
            console.warn('Chart.js未加载，跳过图表显示');
            const chartContainer = document.querySelector('.chart-container');
            if (chartContainer) {
                chartContainer.innerHTML = `
                        <div style="text-align: center; padding: 50px; color: #666;">
                            <p>图表库加载失败，无法显示可视化图表</p>
                            <p style="font-size: 12px; margin-top: 10px;">请检查网络连接或刷新页面重试</p>
                        </div>
                    `;
            }
            return;
        }

        try {
            const ctx = document.getElementById('comparisonChart').getContext('2d');

            const chartData = {
                labels: ['工作量平衡度', '分配效率', '公平性指数', '偏好技能达成率'],
                datasets: [{
                    label: '原始分配',
                    data: [
                        data.originalMetrics?.overallWorkloadBalance || 0,
                        data.originalMetrics?.allocationEfficiencyIndex || 0,
                        data.originalMetrics?.fairnessIndex || 0,
                        data.originalMetrics?.preferredSkillAchievementRatio || 0
                    ],
                    backgroundColor: 'rgba(54, 162, 235, 0.2)',
                    borderColor: 'rgba(54, 162, 235, 1)',
                    borderWidth: 2
                }, {
                    label: '调整后分配',
                    data: [
                        data.adjustedMetrics?.overallWorkloadBalance || 0,
                        data.adjustedMetrics?.allocationEfficiencyIndex || 0,
                        data.adjustedMetrics?.fairnessIndex || 0,
                        data.adjustedMetrics?.preferredSkillAchievementRatio || 0
                    ],
                    backgroundColor: 'rgba(255, 99, 132, 0.2)',
                    borderColor: 'rgba(255, 99, 132, 1)',
                    borderWidth: 2
                }]
            };

            new Chart(ctx, {
                type: 'radar',
                data: chartData,
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        r: {
                            beginAtZero: true,
                            max: 1
                        }
                    },
                    plugins: {
                        title: {
                            display: true,
                            text: '关键指标对比雷达图'
                        },
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });

            console.log('图表渲染成功');

        } catch (error) {
            console.error('图表渲染失败:', error);
            const chartContainer = document.querySelector('.chart-container');
            if (chartContainer) {
                chartContainer.innerHTML = `
                        <div style="text-align: center; padding: 50px; color: #ff4d4f;">
                            <p>图表渲染失败</p>
                            <p style="font-size: 12px; margin-top: 10px;">错误: ${error.message}</p>
                        </div>
                    `;
            }
        }
    }

    function displayTaskDetailComparison(data) {
        console.log('=== 开始处理工单详情对比 ===');
        console.log('输入数据:', data);

        // 检查数据结构
        if (!data.originalMetrics || !data.adjustedMetrics) {
            displayTaskDetailsError('缺少原始或调整后的指标数据');
            return;
        }

        // 从originalMetrics和adjustedMetrics中获取solutionDetailVo信息
        const originalSolution = data.originalMetrics.solutionDetailVo;
        const adjustedSolution = data.adjustedMetrics.solutionDetailVo;

        console.log('原始解决方案详情:', originalSolution);
        console.log('调整后解决方案详情:', adjustedSolution);

        if (!originalSolution || !adjustedSolution) {
            console.error('缺少解决方案详情数据');
            console.log('原始指标字段:', Object.keys(data.originalMetrics));
            console.log('调整后指标字段:', Object.keys(data.adjustedMetrics));

            displayTaskDetailsError(`
                    缺少解决方案详情数据。<br>
                    原始数据solutionDetailVo: ${originalSolution ? '存在' : '不存在'}<br>
                    调整后数据solutionDetailVo: ${adjustedSolution ? '存在' : '不存在'}<br>
                    <button onclick="debugData()" style="margin-top: 10px; padding: 5px 10px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                        查看详细调试信息
                    </button>
                `);
            return;
        }

        try {
            // 构建工单对比数据
            const taskComparisons = buildTaskComparisons(originalSolution, adjustedSolution);
            console.log('构建的工单对比数据:', taskComparisons);
            displayTaskDetails(taskComparisons);
        } catch (error) {
            console.error('构建工单对比数据时出错:', error);
            displayTaskDetailsError('构建工单对比数据时出错: ' + error.message);
        }
    }

    function buildTaskComparisons(originalSolution, adjustedSolution) {
        const taskComparisons = [];

        // 创建工单映射：taskCode -> {designer, task}
        const originalTaskMap = new Map();
        const adjustedTaskMap = new Map();

        // 处理原始分配
        originalSolution.result?.forEach(designer => {
            designer.assignedTask?.forEach(task => {
                originalTaskMap.set(task.taskCode, {
                    designer: designer,
                    task: task
                });
            });
        });

        // 处理调整后分配
        adjustedSolution.result?.forEach(designer => {
            designer.assignedTask?.forEach(task => {
                adjustedTaskMap.set(task.taskCode, {
                    designer: designer,
                    task: task
                });
            });
        });

        // 获取所有工单编号
        const allTaskCodes = new Set([...originalTaskMap.keys(), ...adjustedTaskMap.keys()]);

        // 构建对比数据
        allTaskCodes.forEach(taskCode => {
            const original = originalTaskMap.get(taskCode);
            const adjusted = adjustedTaskMap.get(taskCode);

            taskComparisons.push({
                taskCode: taskCode,
                taskType: original?.task?.taskType || adjusted?.task?.taskType || '-',
                priority: original?.task?.priorityScore || adjusted?.task?.priorityScore || '-',
                workHours: original?.task?.taskRequiredCapacityMinute || adjusted?.task?.taskRequiredCapacityMinute || '-',
                requiredSkills: original?.task?.taskRequireSkill || adjusted?.task?.taskRequireSkill || '-',
                originalDesigner: original ? {
                    code: original.designer.designerCode,
                    name: original.designer.designerCode, // 使用code作为name，如果有name字段可以替换
                    rank: original.designer.designerLevel,
                    capacity: original.designer.designerCapacity,
                    currentLoad: original.designer.designerCapacity - original.designer.capacityRemain,
                    skills: original.designer.skill ? original.designer.skill.split(' | ') : [],
                    attributes: {}
                } : null,
                adjustedDesigner: adjusted ? {
                    code: adjusted.designer.designerCode,
                    name: adjusted.designer.designerCode,
                    rank: adjusted.designer.designerLevel,
                    capacity: adjusted.designer.designerCapacity,
                    currentLoad: adjusted.designer.designerCapacity - adjusted.designer.capacityRemain,
                    skills: adjusted.designer.skill ? adjusted.designer.skill.split(' | ') : [],
                    attributes: {}
                } : null,
                attributes: {}
            });
        });

        return taskComparisons;
    }

    function displayTaskDetails(taskDetails) {
        const tbody = document.getElementById('taskDetailComparison');
        tbody.innerHTML = '';

        if (!taskDetails || taskDetails.length === 0) {
            tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #666;">暂无工单详情数据</td></tr>';
            document.getElementById('taskCount').textContent = '0';
            return;
        }

        // 存储原始数据用于过滤
        window.allTaskDetails = taskDetails;

        renderTaskTable(taskDetails);
    }

    function renderTaskTable(tasks) {
        const tbody = document.getElementById('taskDetailComparison');
        tbody.innerHTML = '';

        tasks.forEach(task => {
            const isChanged = task.originalDesigner?.code !== task.adjustedDesigner?.code;
            const changeStatus = isChanged ? 'changed' : 'unchanged';
            const changeText = isChanged ? '已变更' : '未变更';

            const row = document.createElement('tr');
            row.innerHTML = `
                    <td><span class="task-code">${task.taskCode || '-'}</span></td>
                    <td>${task.taskType || '-'}</td>
                    <!--  <td><span class="priority-badge priority-${getPriorityClass(task.priority)}">${formatPriority(task.priority)}</span></td>-->
                    <td>${formatWorkHours(task.workHours)}</td>
                    <td>${formatDesignerInfo(task.originalDesigner)}</td>
                    <td>${formatDesignerInfo(task.adjustedDesigner)}</td>
                    <td><span class="change-status ${changeStatus}">${changeText}</span></td>
                    <td><button class="detail-btn" onclick="showTaskDetail('${task.taskCode}')">详情</button></td>
                `;
            tbody.appendChild(row);
        });

        document.getElementById('taskCount').textContent = tasks.length;
    }

    function formatDesignerInfo(designer) {
        if (!designer) return '<span style="color: #999;">未分配或不在分配列表中</span>';

        return `
                <div class="designer-info">
                    <span class="designer-name">${designer.name || designer.code}</span>
                    <span class="designer-details">
                        职级: ${designer.rank || '-'} |
                        额度: ${designer.capacity || '-'} |
                        占用: ${designer.currentLoad || '-'} |
                        技能: ${designer.skills && designer.skills.length > 0 ? designer.skills.join(', ') : '-'}
                    </span>
                </div>
            `;
    }

    function formatPriority(priority) {
        if (!priority) return '-';
        if (typeof priority === 'number') return priority.toString();
        return priority.toString();
    }

    function formatWorkHours(workHours) {
        if (!workHours) return '-';
        if (typeof workHours === 'number') {
            // 如果是分钟，转换为小时
            if (workHours > 100) {
                return `${(workHours / 60).toFixed(1)}h`;
            }
            return `${workHours}min`;
        }
        return workHours.toString();
    }

    function getPriorityClass(priority) {
        if (!priority) return 'low';
        const p = priority.toString().toLowerCase();
        if (p.includes('高') || p.includes('high') || parseInt(p) >= 8) return 'high';
        if (p.includes('中') || p.includes('medium') || parseInt(p) >= 5) return 'medium';
        return 'low';
    }

    function filterTasks() {
        if (!window.allTaskDetails) return;

        const searchText = document.getElementById('taskSearch').value.toLowerCase();
        const changeType = document.getElementById('changeTypeFilter').value;

        const filteredTasks = window.allTaskDetails.filter(task => {
            // 搜索过滤
            const matchesSearch = !searchText ||
                (task.taskCode && task.taskCode.toLowerCase().includes(searchText)) ||
                (task.originalDesigner?.name && task.originalDesigner.name.toLowerCase().includes(searchText)) ||
                (task.adjustedDesigner?.name && task.adjustedDesigner.name.toLowerCase().includes(searchText));

            // 变更类型过滤
            const isChanged = task.originalDesigner?.code !== task.adjustedDesigner?.code;
            const matchesChangeType = !changeType ||
                (changeType === 'changed' && isChanged) ||
                (changeType === 'unchanged' && !isChanged);

            return matchesSearch && matchesChangeType;
        });

        renderTaskTable(filteredTasks);
    }

    function showTaskDetail(taskCode) {
        const task = window.allTaskDetails?.find(t => t.taskCode === taskCode);
        if (!task) {
            alert('未找到工单详情');
            return;
        }

        // 构建详情弹窗内容
        const detailContent = `
                <div style="max-width: 700px; max-height: 600px; overflow-y: auto;">
                    <h3>工单详情 - ${task.taskCode}</h3>

                    <h4>基本信息</h4>
                    <p><strong>工单编号:</strong> ${task.taskCode || '-'}</p>
                    <p><strong>工单类型:</strong> ${task.taskType || '-'}</p>
                    <p><strong>工时:</strong> ${formatWorkHours(task.workHours)}</p>
                    <p><strong>所需技能:</strong> ${Array.isArray(task.requiredSkills) ? task.requiredSkills.join(', ') : (task.requiredSkills || '-')}</p>

                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                        <div>
                            <h4>原始分配</h4>
                            ${formatDesignerDetailInfo(task.originalDesigner)}
                        </div>
                        <div>
                            <h4>调整后分配</h4>
                            ${formatDesignerDetailInfo(task.adjustedDesigner)}
                        </div>
                    </div>

                    <h4>变更分析</h4>
                    <p><strong>是否变更:</strong> ${task.originalDesigner?.code !== task.adjustedDesigner?.code ? '是' : '否'}</p>
                    ${task.originalDesigner?.code !== task.adjustedDesigner?.code ?
            `<p><strong>变更原因:</strong> 从 ${task.originalDesigner?.name || task.originalDesigner?.code || '未分配'} 调整到 ${task.adjustedDesigner?.name || task.adjustedDesigner?.code || '未分配或不在分配列表'}</p>` :
            '<p><strong>说明:</strong> 分配未发生变更</p>'
        }
                </div>
            `;

        // 简单的模态框实现
        const modal = document.createElement('div');
        modal.style.cssText = `
                position: fixed; top: 0; left: 0; right: 0; bottom: 0;
                background: rgba(0,0,0,0.5); z-index: 1000;
                display: flex; align-items: center; justify-content: center;
            `;

        const content = document.createElement('div');
        content.style.cssText = `
                background: white; padding: 20px; border-radius: 8px;
                position: relative; max-width: 90vw; max-height: 90vh; overflow: auto;
            `;

        content.innerHTML = detailContent + `
                <button onclick="this.closest('.modal').remove()"
                        style="position: absolute; top: 10px; right: 10px;
                               background: #ff4d4f; color: white; border: none;
                               border-radius: 4px; padding: 5px 10px; cursor: pointer;">
                    关闭
                </button>
            `;

        modal.className = 'modal';
        modal.appendChild(content);
        document.body.appendChild(modal);

        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) modal.remove();
        });
    }

    function formatDesignerDetailInfo(designer) {
        if (!designer) return '<div style="color: #999; padding: 15px; background: #f9f9f9; border-radius: 4px;">未分配</div>';

        return `
                <div style="background: #f9f9f9; padding: 15px; border-radius: 4px;">
                    <p><strong>姓名:</strong> ${designer.name || designer.code}</p>
                    <p><strong>编号:</strong> ${designer.code || '-'}</p>
                    <p><strong>职级:</strong> ${designer.rank || '-'}</p>
                    <p><strong>总额度:</strong> ${designer.capacity || '-'}</p>
                    <p><strong>占用额度:</strong> ${designer.currentLoad || '-'}</p>
                    <p><strong>剩余产能:</strong> ${designer.capacity && designer.currentLoad ? (designer.capacity - designer.currentLoad).toFixed(2) : '-'}</p>
                    <p><strong>利用率:</strong> ${designer.capacity && designer.currentLoad ? ((designer.currentLoad / designer.capacity) * 100).toFixed(1) + '%' : '-'}</p>
                    <p><strong>技能:</strong> ${designer.skills && designer.skills.length > 0 ? designer.skills.join(', ') : '-'}</p>
                </div>
            `;
    }

    function displayTaskDetailsError(message) {
        const tbody = document.getElementById('taskDetailComparison');
        tbody.innerHTML = `<tr><td colspan="8" style="text-align: center; color: #ff4d4f; padding: 20px;">${message}</td></tr>`;
        document.getElementById('taskCount').textContent = '0';
    }

    function displayTaskTypeComparison(data) {
        const tbody = document.getElementById('taskTypeComparison');
        tbody.innerHTML = '';

        console.log('=== 工单类型对比数据调试 ===');
        console.log('原始指标taskTypeDistributionVariance:', data.originalMetrics?.taskTypeDistributionVariance);
        console.log('调整后指标taskTypeDistributionVariance:', data.adjustedMetrics?.taskTypeDistributionVariance);

        // 从原始和调整后的指标中获取工单类型分布方差数据
        const originalTaskTypes = data.originalMetrics?.taskTypeDistributionVariance || [];
        const adjustedTaskTypes = data.adjustedMetrics?.taskTypeDistributionVariance || [];

        if (originalTaskTypes.length === 0 && adjustedTaskTypes.length === 0) {
            tbody.innerHTML = '<tr><td colspan="6" style="text-align: center; color: #666;">暂无工单类型对比数据</td></tr>';
            return;
        }

        // 创建工单类型映射
        const taskTypeMap = new Map();

        // 处理原始数据
        originalTaskTypes.forEach(item => {
            taskTypeMap.set(item.taskTypeCode, {
                taskTypeName: item.taskTypeCode,
                originalVariance: item.variance,
                originalMean: item.mean,
                originalTotalCount: item.totalCount,
                originalDescription: item.distributionDescription || (item.getDistributionDescription ? item.getDistributionDescription() : ''),
                adjustedVariance: null,
                adjustedMean: null,
                adjustedTotalCount: null,
                adjustedDescription: null
            });
        });

        // 处理调整后数据
        adjustedTaskTypes.forEach(item => {
            const key = item.taskTypeCode;
            if (taskTypeMap.has(key)) {
                const existing = taskTypeMap.get(key);
                existing.adjustedVariance = item.variance;
                existing.adjustedMean = item.mean;
                existing.adjustedTotalCount = item.totalCount;
                existing.adjustedDescription = item.distributionDescription || (item.getDistributionDescription ? item.getDistributionDescription() : '');
            } else {
                taskTypeMap.set(key, {
                    taskTypeName: key,
                    originalVariance: null,
                    originalMean: null,
                    originalTotalCount: null,
                    adjustedVariance: item.variance,
                    adjustedMean: item.mean,
                    adjustedTotalCount: item.totalCount,
                    originalDescription: null,
                    adjustedDescription: item.distributionDescription || (item.getDistributionDescription ? item.getDistributionDescription() : '')
                });
            }
        });

        // 渲染对比表格
        taskTypeMap.forEach(item => {
            const row = document.createElement('tr');

            const originalVariance = item.originalVariance || 0;
            const adjustedVariance = item.adjustedVariance || 0;
            const improvement = originalVariance - adjustedVariance;
            const improvementClass = improvement > 0 ? 'improvement' : (improvement < 0 ? 'degradation' : '');

            let improvementText = '-';
            let improvementIcon = '';
            if (item.originalVariance !== null && item.adjustedVariance !== null) {
                improvementText = improvement > 0 ? `+${improvement.toFixed(4)}` : improvement.toFixed(4);
                if (improvement > 0) {
                    improvementIcon = '<span class="trend-up">↗</span> ';
                } else if (improvement < 0) {
                    improvementIcon = '<span class="trend-down">↘</span> ';
                } else {
                    improvementIcon = '<span class="trend-equal">→</span> ';
                }
            }

            // 计算总数量变化
            let countChangeText = '-';
            if (item.originalTotalCount !== null && item.adjustedTotalCount !== null) {
                const countChange = item.adjustedTotalCount - item.originalTotalCount;
                if (countChange > 0) {
                    countChangeText = `<span class="trend-up">+${countChange}</span>`;
                } else if (countChange < 0) {
                    countChangeText = `<span class="trend-down">${countChange}</span>`;
                } else {
                    countChangeText = `<span class="trend-equal">0</span>`;
                }
            } else if (item.originalTotalCount !== null) {
                countChangeText = `${item.originalTotalCount} → -`;
            } else if (item.adjustedTotalCount !== null) {
                countChangeText = `- → ${item.adjustedTotalCount}`;
            }

            row.innerHTML = `
                    <td><strong>${item.taskTypeName}</strong></td>
                    <td>${item.originalVariance !== null ? item.originalVariance.toFixed(4) : '-'}</td>
                    <td>${item.adjustedVariance !== null ? item.adjustedVariance.toFixed(4) : '-'}</td>
                    <td class="${improvementClass}">${improvementIcon}${improvementText}</td>
                    <td>${countChangeText}</td>
                    <td>${item.adjustedDescription || item.originalDescription || '-'}</td>
                `;
            tbody.appendChild(row);
        });

        console.log('工单类型对比表格已生成，共', taskTypeMap.size, '种工单类型');
    }

    function showLoading() {
        document.getElementById('loading').style.display = 'block';
        document.getElementById('error').style.display = 'none';
        document.getElementById('content').style.display = 'none';
    }

    function hideLoading() {
        document.getElementById('loading').style.display = 'none';
    }

    function showError(message) {
        hideLoading();
        document.getElementById('error').style.display = 'block';
        document.getElementById('errorMessage').textContent = message;
    }

    function refreshAnalysis() {
        loadComparisonData();
    }

    function exportReport() {
        if (!comparisonData) {
            alert('没有可导出的数据');
            return;
        }

        const reportData = {
            problemId: problemId,
            analysisTime: new Date().toISOString(),
            comparisonData: comparisonData
        };

        const blob = new Blob([JSON.stringify(reportData, null, 2)], {type: 'application/json'});
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `comparison-report-${problemId}-${new Date().getTime()}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // 临时调试函数
    function debugData() {
        if (!comparisonData) {
            alert('没有数据可调试');
            return;
        }

        console.log('=== 调试数据结构 ===');
        console.log('完整数据:', comparisonData);
        console.log('originalMetrics:', comparisonData.originalMetrics);
        console.log('adjustedMetrics:', comparisonData.adjustedMetrics);

        if (comparisonData.originalMetrics) {
            console.log('originalMetrics.solutionDetailVo:', comparisonData.originalMetrics.solutionDetailVo);
        }
        if (comparisonData.adjustedMetrics) {
            console.log('adjustedMetrics.solutionDetailVo:', comparisonData.adjustedMetrics.solutionDetailVo);
        }

        alert('调试信息已输出到控制台，请按F12查看');
    }
</script>
</body>
</html>

spring:
  jpa:
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        globally_quoted_identifiers: true
  datasource:
    url: ***************************************************************************************************************************************************************************************************************************
    username: mts2_sit
    password: FgJW0zcKqCy5qyDP
    driver-class-name: com.mysql.cj.jdbc.Driver
<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <conversionRule conversionWord="crlf" converterClass="com.angelalign.gms.config.CRLFLogConverter" />
    <property name="CONSOLE_LOG_PATTERN" value="${CONSOLE_LOG_PATTERN:-%clr(%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd'T'HH:mm:ss.SSSXXX}}){faint} %clr(${LOG_LEVEL_PATTERN:-%5p}) %clr(${PID:- }){magenta} %clr(---){faint} %clr([%15.15t]){faint} %clr(%-40.40logger{39}){cyan} %clr(:){faint} %crlf(%m){red} %n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>
    <property name="FILE_LOG_PATTERN" value="${FILE_LOG_PATTERN:-%d{${LOG_DATEFORMAT_PATTERN:-yyyy-MM-dd'T'HH:mm:ss.SSSXXX}} ${LOG_LEVEL_PATTERN:-%5p} ${PID:- } &#45;&#45;&#45; [%t] %-40.40logger{39} : %crlf(%m) %n${LOG_EXCEPTION_CONVERSION_WORD:-%wEx}}"/>

    <include resource="org/springframework/boot/logging/logback/defaults.xml"/>
    <include resource="org/springframework/boot/logging/logback/console-appender.xml" />
    <!-- 文件输出，每天滚动 -->
    <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>logs/tas.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <!-- 每天滚动一次 -->
            <fileNamePattern>logs/tas.%d{yyyy-MM-dd}.log</fileNamePattern>
            <!-- 保留1天的日志 -->
            <maxHistory>1</maxHistory>
            <!-- 当日志文件超过1GB时，会触发滚动 -->
            <totalSizeCap>1GB</totalSizeCap>
        </rollingPolicy>
        <encoder class="ch.qos.logback.core.encoder.LayoutWrappingEncoder">
            <charset>UTF-8</charset>
            <layout class="ch.qos.logback.contrib.json.classic.JsonLayout">
                <!-- 设置时间格式 -->
                <timestampFormat>yyyy-MM-dd'T'HH:mm:ss.SSSXXX</timestampFormat>
                <!-- 设置时间字段的名称 -->
                <timestampFormatTimezoneId>UTC</timestampFormatTimezoneId>
                <!-- 设置 JSON 字段的分隔符 -->
                <appendLineSeparator>true</appendLineSeparator>
                <!-- 设置日志事件的字段 -->
                <includeContextName>false</includeContextName>
                <includeFormattedMessage>true</includeFormattedMessage>
                <includeLevel>true</includeLevel>
                <includeLoggerName>true</includeLoggerName>
                <includeThreadName>true</includeThreadName>
                <includeMDC>true</includeMDC>
                <includeException>true</includeException>
            </layout>
        </encoder>
    </appender>
    <appender name="ASYNC" class="ch.qos.logback.classic.AsyncAppender">
        <queueSize>1024</queueSize>
        <appender-ref ref="FILE"/>
    </appender>

    <!-- 设置日志级别为DEBUG -->
    <root level="DEBUG">
        <appender-ref ref="CONSOLE" />
        <appender-ref ref="FILE" />
    </root>

</configuration>
package com.angelalign.tas;

import org.optaplanner.spring.boot.autoconfigure.OptaPlannerAutoConfiguration;
import org.optaplanner.spring.boot.autoconfigure.OptaPlannerBenchmarkAutoConfiguration;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

@EnableScheduling
@SpringBootApplication(exclude = {
        OptaPlannerAutoConfiguration.class,
        OptaPlannerBenchmarkAutoConfiguration.class
})
public class OptaDemoApp {

        public static void main(String[] args) {
        SpringApplication.run(OptaDemoApp.class, args);
    }

}

package com.angelalign.tas.config;

import com.alibaba.nacos.api.config.annotation.NacosValue;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

@Data
@Component
@ConfigurationProperties(prefix = "tas")
public class TasConfiguration {
    @NacosValue(value = "${tas.terminationMinuteLimit:30}", autoRefreshed = true)
    private Integer terminationMinuteLimit;
    @NacosValue(value = "${tas.terminationSecondBase:1}", autoRefreshed = true)
    private Integer terminationSecondBase;
    @NacosValue(value = "${tas.terminationDigitalRation:0.5}", autoRefreshed = true)
    private double terminationDigitalRation;
    @NacosValue(value = "${tas.unimprovedSpentSecondLimit:100}", autoRefreshed = true)
    private Integer unimprovedSpentSecondLimit;
    @NacosValue(value = "${tas.enableHillClimbing:true}", autoRefreshed = true)
    private Boolean enableHillClimbing;
}

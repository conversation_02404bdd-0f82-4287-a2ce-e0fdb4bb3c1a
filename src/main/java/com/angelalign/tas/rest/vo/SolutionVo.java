package com.angelalign.tas.rest.vo;


import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;


@Data
@Builder
public class SolutionVo {
    private Long id;
    private String code;
    private String client;
    private String solverName;
    private String score;
    private String status;
    @Builder.Default
    private List<AssignedTaskVo> tasks = Lists.newArrayList();

    public static SolutionVo convertFrom(Problem problem) {
        return SolutionVo.builder()
                .id(problem.getId())
                .code(problem.getCode())
                .client(problem.getClient())
                .solverName(problem.getSolverName())
                .score(problem.getScore())
                .tasks(problem.getTasks().stream().map(AssignedTaskVo::convertFrom).collect(Collectors.toList()))
                .build();
    }

    public static SolutionVo convertFrom(Problem problem, BaseSolution solution) {
        SolutionVo build = SolutionVo.builder()
                .id(problem.getId())
                .code(problem.getCode())
                .client(problem.getClient())
                .solverName(problem.getSolverName())
                .score(problem.getScore())
                .build();

        // 总得分
        long[] hardScores = solution.getScore().hardScores();
        boolean match = Arrays.stream(hardScores).allMatch(s -> s >= 0);
        if (!match) return build;

        build.setTasks(solution.getTaskList().stream()
                .filter(task -> task.getDesigner() != null)
                .map(AssignedTaskVo::convertFrom).collect(Collectors.toList()));
        return build;
    }
}

package com.angelalign.tas.rest.vo;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.rest.vo.support.DesignerAssignedVo;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.*;
import java.util.stream.Collectors;

@Data
@Builder
public class SolutionDetailVo {
    private Long id;
    private String code;
    private String client;
    private String solverName;
    private String score;
    private String status;
    @Builder.Default
    private List<DesignerAssignedVo> result = Lists.newArrayList();

    public static SolutionDetailVo convertFrom(Problem problem) {
//        CacheManagementService cacheManagementService = ApplicationContextHolder.getBean(CacheManagementService.class);
//        cacheManagementService.cleanupCacheAfterSolving();
        Set<Task> unAssignTask = problem.getTasks()
                .stream()
                .filter(task -> task.getDesigner() == null)
                .collect(Collectors.toSet());
        Designer fakerDesigner = problem.buildFakerDesigner();
        fakerDesigner.setTasks(unAssignTask);
        problem.getDesigners().add(fakerDesigner);
        SolutionDetailVo build = SolutionDetailVo.builder()
                .id(problem.getId())
                .code(problem.getCode())
                .status(problem.getStatus().name())
                .client(problem.getClient())
                .solverName(problem.getSolverName())
                .score(problem.getScore())
                .result(problem.getDesigners().stream()
                        .sorted(Comparator.comparing(Designer::getDesignerLevelSequence).reversed())
                        .map(DesignerAssignedVo::convertFrom)
                        .collect(Collectors.toList()))
                .build();
//        cacheManagementService.cleanupCacheAfterSolving();
        return build;
    }

    public static SolutionDetailVo convertFrom(Problem problem, BaseSolution solution) {
        SolutionDetailVo build = SolutionDetailVo.builder()
                .id(problem.getId())
                .code(problem.getCode())
                .client(problem.getClient())
                .solverName(problem.getSolverName())
                .score(problem.getScore())
                .build();

        // 总得分
        long[] hardScores = solution.getScore().hardScores();
        boolean match = Arrays.stream(hardScores).allMatch(s -> s >= 0);
        if (!match) return build;
        build.setResult(solution.getDesignerList().stream().map(DesignerAssignedVo::convertFrom).toList());
        return build;
    }
    public static SolutionDetailVo convertFromWithNotVerifyScore(Problem problem, BaseSolution solution) {
        SolutionDetailVo build = SolutionDetailVo.builder()
                .id(problem.getId())
                .code(problem.getCode())
                .client(problem.getClient())
                .solverName(problem.getSolverName())
                .score(problem.getScore())
                .build();
        build.setResult(solution.getDesignerList().stream().map(DesignerAssignedVo::convertFrom).toList());
        return build;
    }
}
package com.angelalign.tas.rest.vo.support;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.assignment.enums.DesignerRankEnum;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Data
@Builder
public class DesignerVo {
    private String id;
    private String code;
    private String rank;
    private int designerLevelSequence;
    @Builder.Default
    private Boolean onDuty = Boolean.TRUE;
    // 该设计师已经消耗额度的所有工单
    @Builder.Default
    private List<DesignerConsumedQuotaTaskVO> consumedQuotaTasks = Lists.newArrayList();
    @Builder.Default
    private List<String> skillIds = Lists.newArrayList();
    @Builder.Default
    private List<String> medTagIds = Lists.newArrayList();
    private CapacityInfoVO capacityInfos;
    @Builder.Default
    private Map<String, String> attributes = new HashMap<>();


    public Designer convertToDomain(Problem problem) {
        Designer build = Designer.builder()
                .code(code)
                .problem(problem)
                .originId(id)
                .attributes(attributes)
                .onDuty(onDuty)
                .attributes(attributes)
                .rank(StringUtils.isNotBlank(rank) ? DesignerRankEnum.valueOf(rank) : null)
                .designerLevelSequence(designerLevelSequence)
                .skills(skillIds.stream().map(reqSkillId ->
                        {
                            return problem.getSkills().stream()
                                    .filter(s -> s.getOriginId().equals(reqSkillId))
                                    .findFirst()
                                    .orElseThrow(() -> new IllegalArgumentException("Can't find skill [" + reqSkillId + "] required by designer [" + code + "]"));
                        })
                        .collect(Collectors.toSet()))
                .build();
        build.setDesignerConsumedQuotaTasks(consumedQuotaTasks.stream().map(v -> v.covertToDomain(problem, build)).collect(Collectors.toSet()));
        build.setCapacity(capacityInfos != null ? capacityInfos.convertToDomain(build) : null);
        return build;
    }

    public static DesignerVo convertFrom(Designer designer) {
        if (designer == null) return null;
        return DesignerVo.builder()
                .id(designer.getOriginId())
                .code(designer.getCode())
                .rank(designer.getRank() != null ? designer.getRank().name() : null)
                .designerLevelSequence(designer.getDesignerLevelSequence())
                .onDuty(designer.getOnDuty())
                .consumedQuotaTasks(designer.getDesignerConsumedQuotaTasks() != null ?
                        designer.getDesignerConsumedQuotaTasks().stream()
                                .map(DesignerConsumedQuotaTaskVO::convertFrom)
                                .collect(Collectors.toList()) :
                        Lists.newArrayList())
                .skillIds(designer.getSkills() != null ?
                        designer.getSkills().stream()
                                .map(skill -> skill.getOriginId())
                                .collect(Collectors.toList()) :
                        Lists.newArrayList())
                .medTagIds(Lists.newArrayList()) // medTagIds are not directly available in Designer domain object
                .capacityInfos(CapacityInfoVO.convertFrom(designer.getCapacity()))
                .attributes(designer.getAttributes() != null ? designer.getAttributes() : new HashMap<>())
                .build();
    }
}

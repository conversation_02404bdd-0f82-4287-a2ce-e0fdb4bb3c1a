package com.angelalign.tas.rest.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.ZonedDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RespResult<T> implements java.io.Serializable {
    @Builder.Default
    private boolean success = true;
    private String errorCode;
    private String errorMessage;
    private String randomCode;
    @Builder.Default
    private ZonedDateTime timestamp = ZonedDateTime.now();
    private T data;

    public static <T> RespResult<T> success() {
        return RespResult.<T>builder().success(true).build();
    }

    public static <T> RespResult<T> success(T data) {
        return RespResult.<T>builder().success(true).data(data).build();
    }

    public static <T> RespResult<T> fail(String errorCode, String errorMessage, String randomCode) {
        return RespResult.<T>builder().success(false).errorCode(errorCode).randomCode(randomCode).errorMessage(errorMessage).build();
    }
}

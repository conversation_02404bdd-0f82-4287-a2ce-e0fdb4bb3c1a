package com.angelalign.tas.rest.vo;

import com.angelalign.tas.domain.assignment.Problem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProblemSimpleVO {
    private Long id;
    private String code;
    private String solverName;
    private String score;
    private String client;
    private String callBackUrl;

    public static ProblemSimpleVO createFromDO(Problem problem) {
        return ProblemSimpleVO.builder()
                .id(problem.getId())
                .solverName(problem.getSolverName())
                .score(problem.getScore())
                .code(problem.getCode())
                .client(problem.getClient())
                .callBackUrl(problem.getCallBackUrl())
                .build();
    }
}

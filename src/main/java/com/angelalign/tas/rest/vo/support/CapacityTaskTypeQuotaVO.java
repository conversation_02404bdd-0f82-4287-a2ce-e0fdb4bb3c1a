package com.angelalign.tas.rest.vo.support;

import com.angelalign.tas.domain.assignment.Capacity;
import com.angelalign.tas.domain.assignment.CapacityTaskTypeQuota;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class CapacityTaskTypeQuotaVO {
    private Long id;
    private Integer quota;
    private String taskType;

    public CapacityTaskTypeQuota convertToDomain( Capacity capacity) {
        return CapacityTaskTypeQuota.builder()
                .capacity(capacity)
                .quota(this.getQuota())
                .taskTypeCode(this.getTaskType())
                .build();
    }

    public static CapacityTaskTypeQuotaVO convertFrom(CapacityTaskTypeQuota capacityTaskTypeQuota) {
        if (capacityTaskTypeQuota == null) return null;
        return CapacityTaskTypeQuotaVO.builder()
                .id(capacityTaskTypeQuota.getId())
                .quota(capacityTaskTypeQuota.getQuota())
                .taskType(capacityTaskTypeQuota.getTaskTypeCode())
                .build();
    }
}

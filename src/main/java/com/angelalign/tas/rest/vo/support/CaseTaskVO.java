package com.angelalign.tas.rest.vo.support;

import com.angelalign.tas.domain.assignment.CaseTask;
import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.rest.vo.ProblemVo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import com.angelalign.tas.domain.assignment.enums.PhaseType;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CaseTaskVO {
    private String caseCode;
    private PhaseType phaseType;
    private String taskTypeCode;
    private Long taskId;
    private Long assigneeId;
    public CaseTask convertToDomain(Problem problem) {
        return CaseTask.builder()
                .caseCode(caseCode)
                .problem(problem)
                .phaseType(phaseType)
                .taskTypeCode(taskTypeCode)
                .taskId(taskId)
                .assigneeId(assigneeId)
                .build();
    }

    public static CaseTaskVO convertFrom(CaseTask caseTask) {
        if (caseTask == null) return null;
        return CaseTaskVO.builder()
                .caseCode(caseTask.getCaseCode())
                .phaseType(caseTask.getPhaseType())
                .taskTypeCode(caseTask.getTaskTypeCode())
                .taskId(caseTask.getTaskId())
                .assigneeId(caseTask.getAssigneeId())
                .build();
    }
}

package com.angelalign.tas.rest.vo.support;

import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.assignment.enums.PhaseType;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

@Builder
@Data
public class TaskVo {
    private String id;
    private String code;
    private String taskTypeId;
    private String caseCode;
    private List<String> requiredSkillId;
    private PhaseType phaseType;
    private Integer deadlineRemain;
    private Integer baseDurationInMinutes;
    @Builder.Default
    private Float baseDurationInCount = 0f;
    private Integer priorityScore;
    @Builder.Default
    private String originalTag = "";
    //    @Builder.Default
//    private List<String> medTagIds = Lists.newArrayList();
    private String dentistId;
    @Builder.Default
    private List<String> preferredDesignerIds = Lists.newArrayList();
    @Builder.Default
    private Map<String, String> attributes = new HashMap<>();

    public Task convertToDomain(Problem problem) {
        return Task.builder()
                .code(code)
                .problem(problem)
                .originId(id)
                .attributes(attributes)
                .phaseType(phaseType)
                .baseDurationInCount(baseDurationInCount)
                .originalTag(originalTag)
                .caseCode(caseCode)
                .priorityScore(priorityScore)
                .baseDurationInMinutes(baseDurationInMinutes)
                .requiredSkill(CollectionUtils.isNotEmpty(requiredSkillId) ?
                        requiredSkillId.stream()
                                .map(skill ->
                                        problem.getSkills().stream()
                                                .filter(s -> s.getOriginId().equals(skill))
                                                .findFirst().orElseThrow(() -> new IllegalArgumentException("Can't find skill [" + skill + "] required by designer [" + code + "]")))
                                .collect(Collectors.toSet()) :
                        new HashSet<>()
                )
                .taskType(problem.getTaskTypes().stream()
                        .filter(t -> t.getOriginId().equals(taskTypeId))
                        .findFirst()
                        .orElseThrow(() -> new IllegalArgumentException("Can't find taskType [" + taskTypeId + "] required by task [" + code + "]"))
                )
                .dentist(dentistId!=null?problem.getDentists().stream()
                        .filter(t -> t.getOriginId().equals(dentistId))
                        .findFirst()
                        .orElseThrow(() -> new IllegalArgumentException("Can't find dentist [" + dentistId + "] required by task [" + code + "]"))
                        :null
                )
                .preferredDesigner(preferredDesignerIds.stream()
                        .map(designerId ->
                                problem.getDesigners().stream()
                                        .filter(s -> s.getOriginId().equals(designerId))
                                        .findFirst()
                                        .orElseThrow(() -> new IllegalArgumentException("Can't find designer [" + designerId + "] required by designer [" + code + "]")))
                        .collect(Collectors.toSet()))
                .attributes(attributes)
                .build();
    }

    public static TaskVo convertFrom(Task task) {
        if (task == null) return null;

        return TaskVo.builder()
                .id(task.getOriginId())
                .code(task.getCode())
                .taskTypeId(task.getTaskType() != null ? task.getTaskType().getOriginId() : null)
                .caseCode(task.getCaseCode())
                .requiredSkillId(task.getRequiredSkill() != null ?
                        task.getRequiredSkill().stream()
                                .map(skill -> skill.getOriginId())
                                .collect(Collectors.toList()) :
                        Lists.newArrayList())
                .phaseType(task.getPhaseType())
                .baseDurationInMinutes(task.getBaseDurationInMinutes())
                .baseDurationInCount(task.getBaseDurationInCount())
                .priorityScore(task.getPriorityScore())
                .originalTag(task.getOriginalTag())
                .dentistId(task.getDentist() != null ? task.getDentist().getOriginId() : null)
                .preferredDesignerIds(task.getPreferredDesigner() != null ?
                        task.getPreferredDesigner().stream()
                                .map(designer -> designer.getOriginId())
                                .collect(Collectors.toList()) :
                        Lists.newArrayList())
                .attributes(task.getAttributes() != null ? task.getAttributes() : new HashMap<>())
                .build();
    }
}

package com.angelalign.tas.rest.vo.support;

import com.angelalign.tas.domain.assignment.*;
import com.angelalign.tas.domain.parser.DesignerAttributeParser;
import com.angelalign.tas.domain.parser.model.PreferTask;
import com.angelalign.tas.score.base.util.DesignerPreferTaskOrSkillUtil;
import lombok.Data;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Data
public class AssignTaskVO {
    private String id;
    private String taskCode;
    private Float taskRequiredCapacityCount;
    private Integer taskRequiredCapacityMinute;
    private String taskRequireSkill;
    private String taskType;
    private String preferDesignerCode;
    private Integer unAssignedReason;
    private Boolean preferTask = Boolean.FALSE;
    private Boolean preferSkill = Boolean.FALSE;
    private Boolean appointTask = Boolean.FALSE;

    public static AssignTaskVO convertFrom(Task task) {
        AssignTaskVO assignTaskVO = new AssignTaskVO();
        Optional.ofNullable(task.getDesigner())
                .ifPresent(designer -> {
                    List<PreferTask> preferTaskList = DesignerAttributeParser.getPreferTaskList(designer);
                    // 本次分配消耗的优先工单
                    Set<Task> designerPreferTask = DesignerPreferTaskOrSkillUtil.getDesignerPreferTask(preferTaskList, designer.getTasks());
                    List<String> designerPreferSkillExpression = DesignerAttributeParser.getPreferSkillExpression(designer);
                    // 本次分配的优先技能的工单
                    Set<Task> designerPreferSkillTask = DesignerPreferTaskOrSkillUtil.getDesignerPreferSkillTask(designerPreferSkillExpression, designer.getTasks());
                    assignTaskVO.setPreferTask(designerPreferTask.contains(task));
                    assignTaskVO.setPreferSkill(designerPreferSkillTask.contains(task));
                });
        assignTaskVO.setId(task.getOriginId());
        assignTaskVO.setTaskCode(task.getCode());
        assignTaskVO.setTaskRequiredCapacityCount(task.getBaseDurationInCount());
        assignTaskVO.setTaskRequiredCapacityMinute(task.getBaseDurationInMinutes());
        assignTaskVO.setAppointTask(task.getPreferredDesigner().contains(task.getDesigner()));
        assignTaskVO.setTaskRequireSkill(task.getRequiredSkill().stream().map(Skill::getCode).collect(Collectors.joining(" | ")));
        assignTaskVO.setTaskType(task.getTaskType().getCode());
        assignTaskVO.setPreferDesignerCode(task.getPreferredDesigner().stream().map(Designer::getCode).collect(Collectors.joining(" | ")));
        assignTaskVO.setUnAssignedReason(task.getUnassignedReason());
        return assignTaskVO;
    }
}

package com.angelalign.tas.rest.vo.support;

import com.angelalign.tas.domain.assignment.Dentist;
import com.angelalign.tas.domain.assignment.Problem;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class DentistVo {
    private String id;
    private String code;
    @Builder.Default
    private List<String> preferredDesignerIds = Lists.newArrayList();
    @Builder.Default
    private List<String> cooperatedDesignerIds = Lists.newArrayList();
    public Dentist convertToDomain(Problem problem) {
        return Dentist.builder()
                .code(code)
                .problem(problem)
                .originId(id)
                .preferredDesigners(
                        preferredDesignerIds.stream().map(designerId ->
                                {return problem.getDesigners().stream()
                                        .filter(s -> s.getOriginId().equals(designerId))
                                        .findFirst()
                                        .orElseThrow(() -> new IllegalArgumentException("Can't find designer ["+designerId+"] required by dentist ["+code+"]"));
                                })
                                .collect(java.util.stream.Collectors.toSet())
                )
                .cooperatedDesigners(
                        cooperatedDesignerIds.stream().map(designerId ->
                                {return problem.getDesigners().stream()
                                        .filter(s -> s.getOriginId().equals(designerId))
                                        .findFirst()
                                        .orElseThrow(() -> new IllegalArgumentException("Can't find designer ["+designerId+"] required by dentist ["+code+"]"));
                                })
                                .collect(java.util.stream.Collectors.toSet())
                )
                .build();
    }

    public static DentistVo convertFrom(Dentist dentist) {
        if (dentist == null) return null;
        return DentistVo.builder()
                .id(dentist.getOriginId())
                .code(dentist.getCode())
                .preferredDesignerIds(dentist.getPreferredDesigners() != null ?
                        dentist.getPreferredDesigners().stream()
                                .map(designer -> designer.getOriginId())
                                .collect(java.util.stream.Collectors.toList()) :
                        Lists.newArrayList())
                .cooperatedDesignerIds(dentist.getCooperatedDesigners() != null ?
                        dentist.getCooperatedDesigners().stream()
                                .map(designer -> designer.getOriginId())
                                .collect(java.util.stream.Collectors.toList()) :
                        Lists.newArrayList())
                .build();
    }
}

package com.angelalign.tas.rest.vo.support;

import com.angelalign.tas.domain.assignment.MedTag;
import com.angelalign.tas.domain.assignment.Problem;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MedTagVo {
    private String code;
    @Builder.Default
    private Integer levelScore = 0;

    public MedTag convertToDomain(Problem result) {
        return MedTag.builder()
                .problem(result)
                .code(code)
                .levelScore(levelScore)
                .build();
    }

    public static MedTagVo convertFrom(MedTag medTag) {
        if (medTag == null) return null;
        return MedTagVo.builder()
                .code(medTag.getCode())
                .levelScore(medTag.getLevelScore())
                .build();
    }
}

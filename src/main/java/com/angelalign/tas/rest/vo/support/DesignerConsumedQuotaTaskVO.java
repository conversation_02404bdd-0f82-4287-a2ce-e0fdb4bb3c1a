package com.angelalign.tas.rest.vo.support;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.DesignerConsumedQuotaTask;
import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.assignment.enums.PhaseType;
import com.fasterxml.jackson.annotation.JsonAnySetter;
import lombok.Builder;
import lombok.Data;

import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class DesignerConsumedQuotaTaskVO {
    private Long designerId;
    private Long taskId;
    private Long taskTypeId;
    @Builder.Default
    private Float consumedCountQuota = 0f;
    private Integer consumedMinuteQuota;
    private String dentistCode;
    @Builder.Default
    private Map<String, Object> attributes = new HashMap<>();

    /**
     * 处理JSON中的动态属性，支持字符串和数组类型
     */
    @JsonAnySetter
    public void setDynamicAttribute(String key, Object value) {
        if ("taskRequireSkillCode".equals(key) && value instanceof List) {
            // 将数组转换为逗号分隔的字符串
            List<?> list = (List<?>) value;
            String joinedValue = String.join(",", list.stream().map(Object::toString).toArray(String[]::new));
            attributes.put(key, joinedValue);
        } else {
            attributes.put(key, value);
        }
    }

    public DesignerConsumedQuotaTask covertToDomain(Problem problem, Designer designer) {
        // 转换attributes为String类型的Map
        Map<String, String> stringAttributes = new HashMap<>();
        for (Map.Entry<String, Object> entry : attributes.entrySet()) {
            stringAttributes.put(entry.getKey(), entry.getValue().toString());
        }

        return DesignerConsumedQuotaTask.builder()
                .designer(designer)
                .attributes(stringAttributes)
                .consumedCountQuota(consumedCountQuota)
                .consumedMinuteQuota(consumedMinuteQuota)
                .taskId(taskId)
                .dentistCode(dentistCode)
                .taskType(problem.getTaskTypes().stream()
                        .filter(t -> t.getOriginId().equals(taskTypeId.toString())).findFirst()
                        .orElseThrow(() -> new IllegalArgumentException("Can't find taskType [" + taskTypeId + "] required by task [" + taskTypeId + "]")))
                .build();
    }

    public static DesignerConsumedQuotaTaskVO convertFrom(DesignerConsumedQuotaTask consumedQuotaTask) {
        if (consumedQuotaTask == null) return null;

        // 转换attributes为Object类型的Map
        Map<String, Object> objectAttributes = new HashMap<>();
        if (consumedQuotaTask.getAttributes() != null) {
            for (Map.Entry<String, String> entry : consumedQuotaTask.getAttributes().entrySet()) {
                objectAttributes.put(entry.getKey(), entry.getValue());
            }
        }

        return DesignerConsumedQuotaTaskVO.builder()
                .designerId(consumedQuotaTask.getDesigner() != null ? consumedQuotaTask.getDesigner().getId() : null)
                .taskId(consumedQuotaTask.getTaskId())
                .taskTypeId(consumedQuotaTask.getTaskType() != null ? Long.valueOf(consumedQuotaTask.getTaskType().getOriginId()) : null)
                .consumedCountQuota(consumedQuotaTask.getConsumedCountQuota())
                .consumedMinuteQuota(consumedQuotaTask.getConsumedMinuteQuota())
                .dentistCode(consumedQuotaTask.getDentistCode())
                .attributes(objectAttributes)
                .build();
    }
}

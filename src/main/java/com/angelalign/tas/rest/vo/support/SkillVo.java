package com.angelalign.tas.rest.vo.support;

import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.assignment.Skill;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class SkillVo {
    private String id;
    private String code;

    public Skill convertToDomain(Problem problem) {
        return Skill.builder()
                .problem(problem)
                .originId(id)
                .code(code)
                .build();
    }

    public static SkillVo convertFrom(Skill skill) {
        if (skill == null) return null;
        return SkillVo.builder()
                .id(skill.getOriginId())
                .code(skill.getCode())
                .build();
    }
}

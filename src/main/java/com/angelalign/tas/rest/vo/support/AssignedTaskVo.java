package com.angelalign.tas.rest.vo.support;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Optional;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AssignedTaskVo {
    private String taskId;
    private String taskCode;
    private String designerId;
    private String designerCode;
    private String designerRand;

    public static AssignedTaskVo convertFrom(Task task) {
        return AssignedTaskVo.builder()
                .taskId(task.getOriginId())
                .taskCode(task.getCode())
                .designerRand(Optional.ofNullable(task.getDesigner()).map(Designer::getRank).map(Enum::name).orElse(null))
                .designerId(Optional.ofNullable(task.getDesigner()).map(Designer::getOriginId).orElse(null))
                .designerCode(Optional.ofNullable(task.getDesigner()).map(Designer::getCode).orElse(null))
                .build();
    }
}

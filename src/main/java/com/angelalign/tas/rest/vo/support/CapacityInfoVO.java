package com.angelalign.tas.rest.vo.support;

import com.angelalign.tas.domain.assignment.Capacity;
import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.enums.CaseAllocateTypeEnum;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Data
@Builder
public class CapacityInfoVO {
    private Long id;
    private String designerId;
    @Builder.Default
    private Boolean modifyDesignExclusive = Boolean.FALSE;
    private List<CapacityTaskTypeQuotaVO> capacityTaskTypeQuotaVOList;
    private CaseAllocateTypeEnum caseAllocateType;
    private LocalDate workingday;
    @Builder.Default
    private Integer overAllocationLimit = 0;
    private Integer originalQuota;

    public Capacity convertToDomain(Designer designer) {
        Capacity build = Capacity.builder()
                .designer(designer)
                .originalQuota(this.getOriginalQuota())
                .caseAllocateType(this.getCaseAllocateType())
                .overAllocationLimit(this.getOverAllocationLimit())
                .modifyDesignExclusive(this.getModifyDesignExclusive())
                .workingday(this.getWorkingday())
                .build();
        build.setCapacityTaskTypeQuotas(this.getCapacityTaskTypeQuotaVOList().stream()
                .map(capacityTaskTypeQuotaVO -> capacityTaskTypeQuotaVO.convertToDomain(build))
                .collect(Collectors.toSet()));
        return build;
    }

    public static CapacityInfoVO convertFrom(Capacity capacity) {
        if (capacity == null) return null;
        return CapacityInfoVO.builder()
                .id(capacity.getId())
                .designerId(capacity.getDesigner() != null ? capacity.getDesigner().getOriginId() : null)
                .modifyDesignExclusive(capacity.getModifyDesignExclusive())
                .capacityTaskTypeQuotaVOList(capacity.getCapacityTaskTypeQuotas() != null ?
                        capacity.getCapacityTaskTypeQuotas().stream()
                                .map(CapacityTaskTypeQuotaVO::convertFrom)
                                .collect(Collectors.toList()) :
                        List.of())
                .caseAllocateType(capacity.getCaseAllocateType())
                .workingday(capacity.getWorkingday())
                .overAllocationLimit(capacity.getOverAllocationLimit())
                .originalQuota(capacity.getOriginalQuota())
                .build();
    }
}

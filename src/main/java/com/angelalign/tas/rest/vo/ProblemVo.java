package com.angelalign.tas.rest.vo;


import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.assignment.ProblemStatus;
import com.angelalign.tas.rest.vo.support.*;
import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Data;

import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Data
@Builder
public class ProblemVo {
    private Long id;
    private String code;
    private String client;
    private String version;
    private String callBackUrl;
    private String solverName;
    private String status; //接口不用传入
    @Builder.Default
    private List<MedTagVo> medTags = Lists.newArrayList();
    @Builder.Default
    private List<SkillVo> skills = Lists.newArrayList();
    @Builder.Default
    private List<TaskTypeVo> taskTypes = Lists.newArrayList();
    @Builder.Default
    private List<DesignerVo> designers = Lists.newArrayList();
    @Builder.Default
    private List<DentistVo> dentists = Lists.newArrayList();
    @Builder.Default
    private List<TaskVo> tasks = Lists.newArrayList();
    @Builder.Default
    private List<CaseTaskVO> caseTasks = Lists.newArrayList();

    public Problem convertToDomain() {
        Problem result = Problem.builder()
                .code(code)
                .solverName(solverName)
                .client(client)
                .version(version)
                .callBackUrl(callBackUrl)
                .status(ProblemStatus.INITIAL)
                .created(LocalDate.now())
                .lastUpdated(LocalDate.now())
                .build();
        result.setSkills(skills.stream().map(v -> v.convertToDomain(result)).collect(Collectors.toSet()));
        result.setTaskTypes(taskTypes.stream().map(v -> v.convertToDomain(result)).collect(Collectors.toSet()));
        result.setDesigners(designers.stream().map(v -> v.convertToDomain(result)).collect(Collectors.toSet()));
        result.setDentists(dentists.stream().map(v -> v.convertToDomain(result)).collect(Collectors.toSet()));
        result.setTasks(tasks.stream().map(v -> v.convertToDomain(result)).collect(Collectors.toSet()));
        result.setCaseTasks(caseTasks.stream().map(v -> v.convertToDomain(result)).collect(Collectors.toSet()));
        return result;
    }

    public static Optional<ProblemVo> convertFrom(Problem problem) {
        if (problem == null) return Optional.empty();

        ProblemVo result = ProblemVo.builder()
                .id(problem.getId())
                .code(problem.getCode())
                .client(problem.getClient())
                .version(problem.getVersion())
                .callBackUrl(problem.getCallBackUrl())
                .solverName(problem.getSolverName())
                .status(problem.getStatus().name())
                .build();

        // 转换集合字段
        if (problem.getMedTags() != null) {
            result.setMedTags(problem.getMedTags().stream()
                    .map(MedTagVo::convertFrom)
                    .collect(Collectors.toList()));
        }

        if (problem.getSkills() != null) {
            result.setSkills(problem.getSkills().stream()
                    .map(SkillVo::convertFrom)
                    .collect(Collectors.toList()));
        }

        if (problem.getTaskTypes() != null) {
            result.setTaskTypes(problem.getTaskTypes().stream()
                    .map(TaskTypeVo::convertFrom)
                    .collect(Collectors.toList()));
        }

        if (problem.getDesigners() != null) {
            result.setDesigners(problem.getDesigners().stream()
                    .map(DesignerVo::convertFrom)
                    .collect(Collectors.toList()));
        }

        if (problem.getDentists() != null) {
            result.setDentists(problem.getDentists().stream()
                    .map(DentistVo::convertFrom)
                    .collect(Collectors.toList()));
        }

        if (problem.getTasks() != null) {
            result.setTasks(problem.getTasks().stream()
                    .map(TaskVo::convertFrom)
                    .collect(Collectors.toList()));
        }

        if (problem.getCaseTasks() != null) {
            result.setCaseTasks(problem.getCaseTasks().stream()
                    .map(CaseTaskVO::convertFrom)
                    .collect(Collectors.toList()));
        }

        return Optional.of(result);
    }
}

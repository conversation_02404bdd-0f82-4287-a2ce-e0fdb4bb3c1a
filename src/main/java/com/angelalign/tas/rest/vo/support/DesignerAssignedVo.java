package com.angelalign.tas.rest.vo.support;

import com.angelalign.tas.domain.assignment.*;
import com.angelalign.tas.domain.parser.DesignerAttributeParser;
import com.angelalign.tas.domain.parser.model.PreferTask;
import com.angelalign.tas.score.base.util.DesignerCapacityConvertor;
import com.angelalign.tas.score.base.util.DesignerPreferTaskOrSkillUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DesignerAssignedVo {
    private String designerCode;
    private Double designerCapacity;
    private int designerLevel;
    private Boolean onDuty;
    private Double capacityRemain;
    private Double dailyCapacity;
    private String skill;
    private String preferSkill;
    private String preferTask;
    private Boolean modifyDesignExclusive;
    private List<AssignTaskVO> assignedTask;
    @Builder.Default
    private Integer preferTaskCount = 0;
    @Builder.Default
    private Integer preferSkillCount = 0;
    private Double preferTaskQuota;
    private Double preferSkillQuota;

    public static DesignerAssignedVo convertFrom(Designer designer) {
        designer.init();
        // 已经消耗的优先工单
        List<DesignerConsumedQuotaTask> consumedPreferTask = DesignerPreferTaskOrSkillUtil.getDesignerConsumedPreferTask(designer);
        // 本次分配消耗的优先工单
        Set<Task> designerPreferTask = DesignerPreferTaskOrSkillUtil.getPreferTask(designer);
        List<String> designerPreferSkillExpression = DesignerAttributeParser.getPreferSkillExpression(designer);
        // 本次分配的优先技能的工单
        Set<Task> designerPreferSkillTask = DesignerPreferTaskOrSkillUtil.getDesignerPreferSkillTask(designerPreferSkillExpression, designer.getTasks());
        // 已经消耗的优先技能的工单
        List<DesignerConsumedQuotaTask> designerConsumedPreferSkillTask = DesignerPreferTaskOrSkillUtil.getDesignerConsumedPreferSkillTask(designerPreferSkillExpression, designer);

        return DesignerAssignedVo.builder()
                .preferTaskQuota(DesignerCapacityConvertor.covertConsumedTaskQuota(designer, consumedPreferTask)
                                 + DesignerCapacityConvertor.covertAssignedTaskQuota(designer, designerPreferTask))
                .preferSkillQuota(DesignerCapacityConvertor.covertConsumedTaskQuota(designer, designerConsumedPreferSkillTask)
                                  + DesignerCapacityConvertor.covertAssignedTaskQuota(designer, designerPreferSkillTask))
                .preferTaskCount(consumedPreferTask.size() + designerPreferTask.size())
                .preferSkillCount(designerPreferSkillTask.size() + designerConsumedPreferSkillTask.size())
                .dailyCapacity(Optional.of(designer).map(Designer::getCapacityDailyTotalQuota).orElse(0d))
                .preferSkill(String.join(" | ", DesignerAttributeParser.getPreferSkillExpression(designer)))
                .preferTask(DesignerAttributeParser.getPreferTaskList(designer).stream().map(PreferTask::toString).collect(Collectors.joining(" | ")))
                .skill(Optional.of(designer).map(Designer::getSkills).map(s -> s.stream().map(Skill::getCode).collect(Collectors.joining(" | "))).orElse(""))
                .assignedTask(designer.getTasks().stream().map(AssignTaskVO::convertFrom).toList())
                .designerLevel(Optional.of(designer).map(Designer::getDesignerLevelSequence).orElse(0))
                .capacityRemain(designer.getOverAllocationLimit() - designer.getTotalConsumedQuota())
                .designerCapacity(Optional.of(designer).map(Designer::getOverAllocationLimit).orElse(0d))
                .designerCode(Optional.of(designer).map(Designer::getCode).orElse(null))
                .onDuty(Optional.of(designer).map(Designer::getOnDuty).orElse(Boolean.FALSE))
                .modifyDesignExclusive(Optional.ofNullable(designer.getCapacity()).map(Capacity::getModifyDesignExclusive).orElse(Boolean.FALSE))
                .build();
    }
}

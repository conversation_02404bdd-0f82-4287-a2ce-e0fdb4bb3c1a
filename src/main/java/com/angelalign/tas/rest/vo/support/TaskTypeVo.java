package com.angelalign.tas.rest.vo.support;

import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.assignment.TaskType;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TaskTypeVo {
    private String id;
    private String code;
    private String requiredSkillId;
    private int baseDurationInMinutes;

    public TaskType convertToDomain(Problem problem) {
        return TaskType.builder()
                .problem(problem)
                .code(code)
                .originId(id)
                .build();
    }

    public static TaskTypeVo convertFrom(TaskType taskType) {
        if (taskType == null) return null;
        return TaskTypeVo.builder()
                .id(taskType.getOriginId())
                .code(taskType.getCode())
                // Note: requiredSkillId and baseDurationInMinutes are not available in TaskType domain object
                // These fields are only used during conversion to domain
                .build();
    }
}

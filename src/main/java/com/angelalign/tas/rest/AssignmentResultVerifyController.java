package com.angelalign.tas.rest;

import com.angelalign.tas.score.base.constraint.BaseConstraintPool;
import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.rest.vo.RespResult;
import com.angelalign.tas.service.ProblemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.function.Predicate;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/result")
@Slf4j
@CrossOrigin
public class AssignmentResultVerifyController {
    @Autowired
    private ProblemService problemService;
    @GetMapping("/designModify/verify/{id}")
    public ResponseEntity<RespResult<List<String>>> designModifyVerify(@PathVariable("id") Long problemId) {
        Problem problem = problemService.getProblemById(problemId).orElseThrow(() -> new IllegalArgumentException("Can't find problem by id [" + problemId + "]"));
        BaseConstraintPool baseConstraintPool = new BaseConstraintPool();
        List<String> assignWrongTaskCode = problem.getTasks()
                .stream()
                .filter(Predicate.not(baseConstraintPool.fullDesignModifyAssignLastDesignerPrivilege()))
                .map(task -> task.getCode() + " -> " + task.getDesigner().getCode())
                .toList();

        List<String> code = problem.getTasks()
                .stream()
                .filter(task -> task.getDesigner() == null)
                .map(task -> task.getCode() + " -> null")
                .collect(Collectors.toList());
        code.addAll(assignWrongTaskCode);
        return ResponseEntity.ok(RespResult.success(code));
    }
}

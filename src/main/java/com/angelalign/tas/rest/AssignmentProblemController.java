package com.angelalign.tas.rest;

import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.rest.vo.*;
import com.angelalign.tas.score.base.cache.CacheManagementService;
import com.angelalign.tas.service.ProblemService;
import com.angelalign.tas.service.SolverService;
import com.angelalign.tas.solver.OptaSolverFactory;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/task-assignment")
@Slf4j
@CrossOrigin
public class AssignmentProblemController {
    @Autowired
    private ProblemService problemService;
    @Autowired
    private OptaSolverFactory optaSolverFactory;
    @Autowired
    private SolverService solverService;
    @Autowired
    private CacheManagementService cacheManagementService;

    @PostMapping("/problem")
    public ResponseEntity<RespResult<CreatedId>> createProblem(@RequestBody ProblemVo problem) {
        log.info("create problem {}", problem.getCode());
        long id = problemService.saveProblem(problem);
        return ResponseEntity.ok(RespResult.success(CreatedId.builder().id(id).build()));
    }

    @GetMapping("/problem/{id}")
    public ProblemVo getProblem(@PathVariable Long id) {
        return problemService.getProblemById(id)
                .flatMap(ProblemVo::convertFrom)
                .orElseThrow(() -> new IllegalArgumentException("Can't find problem by id [" + id + "]"));
    }

//    @PostMapping("/solve/{id}")
//    public ResponseEntity<RespResult<?>> solve(@PathVariable("id") Long problemId) {
//        Problem problem = assignmentProblemService.getProblemById(problemId).orElseThrow(() -> new IllegalArgumentException("Can't find problem by id [" + problemId + "]"));
//
//        if (problem.getStatus() != ProblemStatus.INITIAL) {
//            throw new IllegalArgumentException("Can't start to solve problem, status is [" + problem.getSolverName() + "]");
//        }
//
//        SolverManager<BaseSolution, Long> solverManager = optaSolverFactory.getSolverManager(problem);
//
//        problem.setStatus(ProblemStatus.SOLVING);
//        assignmentProblemService.saveProblem(problem);
//        solverManager.solveAndListen(problemId
//                , (id) -> problem.convertToSolution()
//                , assignmentProblemService::bestSolutionConsumer
//                , assignmentProblemService::finalBestSolutionHandeAndSaveConsumer
//                , null);
//        return ResponseEntity.ok(RespResult.success());
//    }

    @PostMapping("/blockSolve/{id}")
    public ResponseEntity<RespResult<SolutionVo>> blockSolve(@PathVariable("id") Long problemId) {
        return ResponseEntity.ok(RespResult.success(solverService.blockSolve(problemId)));
    }

    @PostMapping("/blockSolve/detail/{id}")
    public ResponseEntity<RespResult<SolutionDetailVo>> blockSolveDetail(@PathVariable("id") Long problemId) {
        return ResponseEntity.ok(RespResult.success(solverService.blockSolveDetail(problemId)));
    }

    @PostMapping("/blockSolveWithSolver/detail/{id}")
    public ResponseEntity<RespResult<SolutionDetailVo>> blockSolveWithSolver(@PathVariable("id") Long problemId) {
        return ResponseEntity.ok(RespResult.success(solverService.blockSolveWithSolver(problemId)));
    }

    @GetMapping("/findProblemByAccount/{account}")
    public ResponseEntity<RespResult<List<ProblemSimpleVO>>> findProblemByAccount(@PathVariable("account") String account) {
        List<ProblemSimpleVO> list = problemService.findByDesignerAccount(account)
                .stream()
                .map(ProblemSimpleVO::createFromDO)
                .toList();
        return ResponseEntity.ok(RespResult.success(list));
    }

    @GetMapping("/solution/detail/{id}")
    public ResponseEntity<RespResult<SolutionDetailVo>> getSolutionDetail(@PathVariable("id") Long problemId) {
        Problem problem = problemService.findById(problemId);
        return ResponseEntity.ok(RespResult.success(SolutionDetailVo.convertFrom(problem)));
    }

    @GetMapping("/findProblemByTaskId/{id}")
    public ResponseEntity<RespResult<List<ProblemSimpleVO>>> findProblemByTaskId(@PathVariable("id") Long taskId) {
        List<ProblemSimpleVO> list = problemService.findByTaskId(String.valueOf(taskId))
                .stream()
                .map(ProblemSimpleVO::createFromDO)
                .toList();
        return ResponseEntity.ok(RespResult.success(list));
    }

    @GetMapping("/solution/{id}")
    public ResponseEntity<RespResult<SolutionVo>> getSolution(@PathVariable("id") Long problemId) {
        Problem problem = problemService.getProblemById(problemId).orElseThrow(() -> new IllegalArgumentException("Can't find problem by id [" + problemId + "]"));
        return ResponseEntity.ok(RespResult.success(SolutionVo.convertFrom(problem)));
    }
}

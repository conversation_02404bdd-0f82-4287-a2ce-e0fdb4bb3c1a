package com.angelalign.tas.domain.solution.oversea.runtime;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import lombok.Data;
import org.optaplanner.core.api.domain.solution.PlanningEntityCollectionProperty;
import org.optaplanner.core.api.domain.solution.PlanningScore;
import org.optaplanner.core.api.domain.solution.PlanningSolution;
import org.optaplanner.core.api.domain.solution.ProblemFactCollectionProperty;
import org.optaplanner.core.api.domain.valuerange.ValueRangeProvider;
import org.optaplanner.core.api.score.buildin.bendablelong.BendableLongScore;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

@PlanningSolution
@Data
public class OverSeaDesignRuntimeSolution implements BaseSolution {
    @PlanningScore(bendableHardLevelsSize = 10, bendableSoftLevelsSize = 16)
    public BendableLongScore score;
    @ProblemFactCollectionProperty
    @ValueRangeProvider(id = "designerRange")
    public Set<Designer> designerList = new HashSet<>();
    @PlanningEntityCollectionProperty
    public List<Task> taskList = List.of();
    public Problem problem;
    public Boolean accepted;
}

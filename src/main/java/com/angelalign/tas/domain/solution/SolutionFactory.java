package com.angelalign.tas.domain.solution;

import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.solver.version.SolverVersionRouterFactory;
import com.angelalign.tas.util.ApplicationContextHolder;
import lombok.SneakyThrows;


public class SolutionFactory {

    @SneakyThrows
    public static BaseSolution createSolution(Problem problem) {
        Class<? extends BaseSolution> solutionClass =
                ApplicationContextHolder.getBean(SolverVersionRouterFactory.class).getRouter(problem.getSolverName()).routeSolution();
        return solutionClass.getDeclaredConstructor().newInstance();
    }
}

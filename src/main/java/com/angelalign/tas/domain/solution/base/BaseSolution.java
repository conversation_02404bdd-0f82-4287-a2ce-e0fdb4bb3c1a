package com.angelalign.tas.domain.solution.base;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.assignment.Task;
import org.optaplanner.core.api.score.buildin.bendablelong.BendableLongScore;

import java.util.List;
import java.util.Set;

public interface BaseSolution {

    Problem getProblem();

    void setProblem(Problem problem);

    Boolean getAccepted();

    void setAccepted(<PERSON><PERSON><PERSON> accepted);

    List<Task> getTaskList();

    Set<Designer> getDesignerList();

    void setTaskList(List<Task> taskList);

    void setDesignerList(Set<Designer> designerList);

    BendableLongScore getScore();

    void setScore(BendableLongScore score);
}

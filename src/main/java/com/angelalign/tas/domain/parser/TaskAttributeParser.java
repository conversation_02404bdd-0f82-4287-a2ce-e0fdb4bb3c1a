package com.angelalign.tas.domain.parser;

import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.rest.vo.support.MedTagVo;
import com.angelalign.tas.domain.assignment.enums.EntityAttributeKey;
import com.angelalign.tas.util.Jackson;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

public class TaskAttributeParser {
    protected static final Cache<String, List<MedTagVo>> medTagCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, List<String>> taskRequireSkillCodeCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, String> productLineCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();

    protected static final Cache<String, String> taskTeamCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, Boolean> orderTaskRuleAssign = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, Boolean> taskMustAssignPreferredDesignerCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, String> taskPhaseCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, List<String>> designTagCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();

    public static void invalidateAll() {
        medTagCache.invalidateAll();
        taskRequireSkillCodeCache.invalidateAll();
        productLineCache.invalidateAll();
        taskTeamCache.invalidateAll();
        orderTaskRuleAssign.invalidateAll();
        taskMustAssignPreferredDesignerCache.invalidateAll();
        taskPhaseCache.invalidateAll();
        designTagCache.invalidateAll();
    }

    public static String getProductLine(Task task) {
        return productLineCache.get(task.getProblem().getId() + task.getOriginId()
                , key -> task.getAttributes().getOrDefault(EntityAttributeKey.PRODUCT_LINE.getKey(), "none"));
    }

    public static String getPhaseName(Task task) {
        return taskPhaseCache.get(task.getProblem().getId() + task.getOriginId()
                , key -> task.getAttributes().getOrDefault(EntityAttributeKey.PHASE_NAME.getKey(), "none"));
    }

    public static List<String> getCaseDesignType(Task task) {
        return designTagCache.get(task.getProblem().getId() + task.getOriginId()
                , key -> Jackson.covertObjectListFromJsonString(task.getAttributes().getOrDefault(EntityAttributeKey.CASE_DESIGN_TYPE.getKey(), "[]")
                        , String.class));
    }

    /**
     * 获取工单所有的标签
     *
     * @param task 工单
     * @return 工单标签和标签的优先级 集合
     */
    public static List<MedTagVo> getMegTag(Task task) {
        return medTagCache.get(task.getProblem().getId() + task.getOriginId(), (key) -> Stream.of(task.getAttributes().get(EntityAttributeKey.CASE_TAG.getKey())
                        , task.getAttributes().getOrDefault(EntityAttributeKey.ORDER_TAG.getKey(), ""))
                .map(data -> Jackson.covertObjectListFromJsonString(data, MedTagVo.class))
                .flatMap(List::stream)
                .toList());
    }

    /**
     * 获取工单所有需要的技能
     *
     * @param task 工单
     * @return 所需要的技能集合
     */
    public static List<String> getTaskRequireSkillCode(Task task) {
        return taskRequireSkillCodeCache.get(task.getOriginId() + task.getProblem().getId()
                , (key) -> Stream.of(task.getAttributes().getOrDefault(EntityAttributeKey.TASK_REQUIRE_SKILL_CODE.getKey(), "[]"))
                        .map(data -> Jackson.covertObjectListFromJsonString(data, String.class))
                        .flatMap(List::stream)
                        .toList());
    }

    /**
     * 获取工单组
     *
     * @param task 工单
     * @return 工单所在组的名字
     */
    public static String getTaskTeam(Task task) {
        return taskTeamCache.get(task.getOriginId() + task.getProblem().getId()
                , key -> task.getAttributes().getOrDefault(EntityAttributeKey.TASK_TEAM.getKey()
                        , ""));
    }

    /**
     * 获取工单是否需要按照订单病例规则分配
     *
     * @param task 工单
     * @return 工单是否需要按照订单规则分配
     */
    public static Boolean getOrderTaskRuleAssign(Task task) {
        return orderTaskRuleAssign.get(task.getOriginId() + task.getProblem().getId()
                , key -> task.getAttributes().getOrDefault(EntityAttributeKey.ORDER_ASSIGN_RULE_IS_CASE.getKey()
                        , "false").equalsIgnoreCase("true"));
    }

    /**
     * 获取工单是否需要按照订单病例规则分配
     *
     * @param task 工单
     * @return 工单是否需要按照订单规则分配
     */
    public static Boolean taskMustAssignPreferredDesigner(Task task) {
        return taskMustAssignPreferredDesignerCache.get(task.getOriginId() + task.getProblem().getId()
                , key -> task.getAttributes().getOrDefault(EntityAttributeKey.TASK_PREFER_DESIGNER_ACCOUNT_LIST.getKey()
                        , "false").equalsIgnoreCase("true"));
    }
}

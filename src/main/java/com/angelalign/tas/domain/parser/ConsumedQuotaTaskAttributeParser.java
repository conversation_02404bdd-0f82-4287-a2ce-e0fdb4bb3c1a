package com.angelalign.tas.domain.parser;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.DesignerConsumedQuotaTask;
import com.angelalign.tas.rest.vo.support.MedTagVo;
import com.angelalign.tas.domain.assignment.enums.EntityAttributeKey;
import com.angelalign.tas.util.Jackson;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Stream;

public class ConsumedQuotaTaskAttributeParser {
    protected static final Cache<Long, List<MedTagVo>> medTagCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<Long, List<String>> consumedTaskPreferDeisgnerCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, Double> consumedDesignerAppointedTaskQuotaCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<Long, String> productLineCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<Long, List<String>> taskRequireSkillCodeCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, String> taskPhaseCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, List<String>> designTagCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();

    public static void invalidateAll() {
        medTagCache.invalidateAll();
        taskRequireSkillCodeCache.invalidateAll();
        taskPhaseCache.invalidateAll();
        productLineCache.invalidateAll();
        consumedTaskPreferDeisgnerCache.invalidateAll();
        consumedDesignerAppointedTaskQuotaCache.invalidateAll();
        designTagCache.invalidateAll();
    }

    public static Double getDesignerAppointedTaskQuota(Designer designer) {
        return consumedDesignerAppointedTaskQuotaCache.get(designer.getCode() + designer.getProblem().getId()
                , key -> {
                    return designer.getDesignerConsumedQuotaTasks()
                            .stream()
                            .filter(task -> ConsumedQuotaTaskAttributeParser.getPreferUserAccountList(task).stream().anyMatch(data -> data.equals(designer.getCode())))
                            .mapToDouble(DesignerConsumedQuotaTask::getConsumedMinuteQuota)
                            .sum();
                });
    }

    public static String getPhaseName(DesignerConsumedQuotaTask task) {
        return taskPhaseCache.get(task.getTaskId().toString()
                , key -> task.getAttributes().getOrDefault(EntityAttributeKey.PHASE_NAME.getKey(), "none"));
    }

    public static List<String> getDesignTag(DesignerConsumedQuotaTask task) {
        return designTagCache.get(task.getTaskId().toString()
                , key -> Jackson.covertObjectListFromJsonString(task.getAttributes().getOrDefault(EntityAttributeKey.CASE_DESIGN_TYPE.getKey(), "[]")
                        , String.class));
    }

    public static String getProductLine(DesignerConsumedQuotaTask task) {
        return productLineCache.get(task.getTaskId()
                , key -> task.getAttributes().getOrDefault(EntityAttributeKey.PRODUCT_LINE.getKey(), "none"));
    }

    /**
     * 获取工单所有的标签
     *
     * @param task 工单
     * @return 工单标签和标签的优先级 集合
     */
    public static List<MedTagVo> getMegTag(DesignerConsumedQuotaTask task) {
        return medTagCache.get(task.getTaskId()
                , (key) -> Stream.of(task.getAttributes().getOrDefault(EntityAttributeKey.CASE_TAG.getKey(), "")
                                , task.getAttributes().getOrDefault(EntityAttributeKey.ORDER_TAG.getKey(), ""))
                        .map(data -> Jackson.covertObjectListFromJsonString(data, MedTagVo.class))
                        .flatMap(List::stream)
                        .toList()
        );
    }

    public static List<String> getPreferUserAccountList(DesignerConsumedQuotaTask task) {
        return consumedTaskPreferDeisgnerCache.get(task.getTaskId(), key -> Jackson.covertObjectListFromJsonString(task.getAttributes().getOrDefault(EntityAttributeKey.TASK_PREFER_DESIGNER_ACCOUNT_LIST.getKey(), "[]")
                , String.class));
    }

    /**
     * 获取工单所有需要的技能
     *
     * @param task 工单
     * @return 所需要的技能集合
     */
    public static List<String> getTaskRequireSkillCode(DesignerConsumedQuotaTask task) {
        return taskRequireSkillCodeCache.get(task.getTaskId()
                , (key) -> Stream.of(task.getAttributes().getOrDefault(EntityAttributeKey.TASK_REQUIRE_SKILL_CODE.getKey(), "[]"))
                        .map(data -> Jackson.covertObjectListFromJsonString(data, String.class))
                        .flatMap(List::stream)
                        .toList());
    }
}

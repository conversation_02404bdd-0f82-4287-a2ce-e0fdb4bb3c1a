package com.angelalign.tas.domain.parser;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.parser.model.PreferTask;
import com.angelalign.tas.domain.assignment.enums.EntityAttributeKey;
import com.angelalign.tas.score.base.util.DesignerCapacityConvertor;
import com.angelalign.tas.util.Jackson;
import com.fasterxml.jackson.databind.JsonNode;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.StreamSupport;

public class DesignerAttributeParser {
    protected static final Cache<String, List<PreferTask>> preferTaskCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, Boolean> designerAboveDailyQuotaCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, List<String>> preferSkill = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();

    protected static final Cache<String, Boolean> isIntern = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, String> designerTeamCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, Boolean> periodLimitDesignerCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, Double> processConsumedQuotaCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();

    public static void invalidateAll() {
        preferTaskCache.invalidateAll();
        preferSkill.invalidateAll();
        isIntern.invalidateAll();
        designerTeamCache.invalidateAll();
        periodLimitDesignerCache.invalidateAll();
        designerAboveDailyQuotaCache.invalidateAll();
        processConsumedQuotaCache.invalidateAll();
    }

    public static Boolean designerEnableAboveQuota(Designer designer) {
        return designerAboveDailyQuotaCache.get(designer.getCode() + designer.getProblem().getId(), key -> {
            String s = designer.getAttributes().getOrDefault(EntityAttributeKey.DESIGNER_ENABLE_ABOVE_QUOTA.getKey(), Boolean.TRUE.toString());
            return Boolean.valueOf(s);
        });
    }

    /**
     * 判断设计师是否在当前时间段被限制的设计师
     *
     * @param designer 设计师
     * @return 是否被限制分配
     */
    public static Boolean isPeriodLimitDesigner(Designer designer) {
        return periodLimitDesignerCache.get(designer.getCode() + designer.getProblem().getId(), key -> {
            String s = designer.getAttributes().getOrDefault(EntityAttributeKey.PERIOD_LIMIT_DESIGNER.getKey(), Boolean.FALSE.toString());
            return Boolean.valueOf(s);
        });
    }

    /**
     * 获取设计师需要参与均衡计算的已经消耗额度 （为了atp 中班）
     *
     * @param designer 设计师
     * @return 已经消耗的额度
     */
    public static Double getProcessConsumedQuota(Designer designer) {
        return processConsumedQuotaCache.get(designer.getCode() + designer.getProblem().getId(), key -> {
            // 优先使用变量，没有变量就重新计算
            String s = designer.getAttributes().getOrDefault(EntityAttributeKey.DESIGNER_CONSUMED_QUOTA.getKey()
                    , DesignerCapacityConvertor.covertConsumedTaskQuota(designer).toString());
            return Double.valueOf(s);
        });
    }

    /**
     * 判断设计师是否是实习生
     *
     * @param designer 设计师
     * @return 是否是实习生
     */
    public static Boolean isIntern(Designer designer) {
        return isIntern.get(designer.getCode() + designer.getProblem().getId(), key -> {
            String s = designer.getAttributes().getOrDefault(EntityAttributeKey.IS_INTERN.getKey(), Boolean.FALSE.toString());
            return Boolean.valueOf(s);
        });
    }

    /**
     * 获取设计师的偏好工单
     *
     * @param designer 设计师
     * @return 偏好工单表达式和配额的集合
     */
    public static List<PreferTask> getPreferTaskList(Designer designer) {
        return preferTaskCache.get(designer.getCode() + designer.getProblem().getId(), key -> {
            String s = designer.getAttributes().getOrDefault(EntityAttributeKey.PREFER_TASK.getKey(), "[]");
            JsonNode jsonNode = Jackson.jsonNodeOf(s);
            return StreamSupport.stream(jsonNode.spliterator(), false)
                    .map(node -> {
                        String expression = node.path("expression").asText();
                        if (StringUtils.isBlank(expression)) return null;
                        return new PreferTask(node.path("expression").asText(), node.path("quota").asDouble());
                    })
                    .filter(Objects::nonNull)
                    .toList();
        });
    }

    /**
     * 获取设计师的偏好技能
     *
     * @param designer 设计师
     * @return 偏好技能表达式集合
     */
    public static List<String> getPreferSkillExpression(Designer designer) {
        return preferSkill.get(designer.getCode() + designer.getProblem().getId(), key -> {
            String s = designer.getAttributes().getOrDefault(EntityAttributeKey.PREFER_SKILL.getKey(), "[]");
            return Jackson.covertObjectListFromJsonString(s, String.class);
        });
    }

    public static String getDesignerTeam(Designer designer) {
        return designerTeamCache.get(designer.getCode() + designer.getProblem().getId()
                , key -> designer.getAttributes().getOrDefault(EntityAttributeKey.DESIGNER_TEAM.getKey()
                        , ""));
    }
}

package com.angelalign.tas.domain.parser.model;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@Data
@Builder
@NoArgsConstructor
public class PreferTask {
    private String expression;
    private Double quota;

    @Override
    public String toString() {
        return "{" +
               "expression='" + expression + '\'' +
               ", quota=" + quota +
               '}';
    }
}

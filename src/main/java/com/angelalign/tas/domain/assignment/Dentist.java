package com.angelalign.tas.domain.assignment;

import lombok.*;

import javax.persistence.*;
import java.util.Set;

@Entity(name = "ta_dentist")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = {"id","originId","code"})
public class Dentist {
    @Id
    @GeneratedValue
    private Long id;
    @ManyToOne
    @JoinColumn(name = "problem_id")
    private Problem problem;
    private String originId;
    private String code;
    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE},fetch = FetchType.EAGER)
    @JoinTable(
            name = "ta_rel_dentist_designer_preference", // 中间表的名称
            joinColumns = @JoinColumn(name = "dentist_id"),
            inverseJoinColumns = @JoinColumn(name = "designer_id")
    )
    private Set<Designer> preferredDesigners;
    @ManyToMany(cascade = {CascadeType.PERSIST, CascadeType.MERGE},fetch = FetchType.EAGER)
    @JoinTable(
            name = "ta_rel_dentist_designer_cooperated", // 中间表的名称
            joinColumns = @JoinColumn(name = "dentist_id"),
            inverseJoinColumns = @JoinColumn(name = "designer_id")
    )
    private Set<Designer> cooperatedDesigners;
}

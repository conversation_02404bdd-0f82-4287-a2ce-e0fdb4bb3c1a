package com.angelalign.tas.domain.assignment;

import lombok.*;
import org.optaplanner.core.api.domain.entity.PlanningEntity;

import javax.persistence.*;

@PlanningEntity
@Entity(name = "ta_task_type")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(exclude = "problem")
public class TaskType {
    @Id
    @GeneratedValue
    private Long id;
    @ManyToOne
    @JoinColumn(name = "problem_id")
    private Problem problem;
    private String originId;
    private String code;
}

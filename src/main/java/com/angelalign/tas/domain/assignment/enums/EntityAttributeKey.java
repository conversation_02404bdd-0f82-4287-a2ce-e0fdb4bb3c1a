package com.angelalign.tas.domain.assignment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
@Getter
public enum EntityAttributeKey {
    PREFER_TASK("preferTask", AttributeKeyType.JSON, "偏好工单"),
    PREFER_SKILL("preferSkill", AttributeKeyType.LIST, "偏好技能"),
    IS_INTERN("isIntern", AttributeKeyType.BOOLEAN, "是否是实习生"),
    PERIOD_LIMIT_DESIGNER("periodLimitDesigner", AttributeKeyType.BOOLEAN, "当前设计是否在当前时间段被限制分配"),
    ORDER_TAG("orderTag", AttributeKeyType.LIST, "订单标签"),
    DESIGNER_CONSUMED_QUOTA("designerConsumedMinuteQuota", AttributeKeyType.NUMBER, "设计师需要参与计算的已消耗额度"),
    CASE_TAG("caseTag", AttributeKeyType.LIST, "病例标签"),
    TASK_TEAM("teamName", AttributeKeyType.STRING, "工单所属组"),
    DESIGNER_TEAM("teamName", AttributeKeyType.STRING, "设计师所属组"),
    PRODUCT_LINE("productLine", AttributeKeyType.STRING, "工单产品线"),
    PHASE_NAME("phaseName", AttributeKeyType.STRING, "工单阶段"),
    CASE_DESIGN_TYPE("caseDesignType", AttributeKeyType.LIST, "病例设计类型"),
    DESIGNER_ENABLE_ABOVE_QUOTA("designerEnableAboveQuota", AttributeKeyType.BOOLEAN, "设计师的是否可以可以超过日额度（病例指定不用看额度，这个时候也不希望他分到工单）"),
    ORDER_ASSIGN_RULE_IS_CASE("orderAssignRuleIsCase", AttributeKeyType.BOOLEAN, "订单分配规则是否是病例分配"),
    TASK_PREFER_DESIGNER_ACCOUNT_LIST("preferDesignerAccountList", AttributeKeyType.LIST, "工单指定的设计师账户列表"),
    TASK_MUST_ASSIGN_PREFER_DESIGNER("taskMustAssignPreferDesigner",AttributeKeyType.BOOLEAN, "工单是否必须分配给指定的设计师"),
    TASK_REQUIRE_SKILL_CODE("taskRequireSkillCode", AttributeKeyType.LIST, "工单所有需要的技能");


    private final String key;
    private final AttributeKeyType type;
    private final String description;

    public enum AttributeKeyType {
        JSON, STRING, NUMBER, BOOLEAN, LIST
    }
}

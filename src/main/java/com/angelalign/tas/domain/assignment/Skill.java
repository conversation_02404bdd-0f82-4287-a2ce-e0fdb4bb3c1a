package com.angelalign.tas.domain.assignment;

import lombok.*;
import org.optaplanner.core.api.domain.entity.PlanningEntity;

import javax.persistence.*;

@PlanningEntity
@Entity(name = "ta_skill")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(exclude = "problem")
public class Skill {
    @Id
    @GeneratedValue
    private Long id;
    @ManyToOne
    @JoinColumn(name = "problem_id")
    private Problem problem;
    private String originId;
    private String code;

    @Override
    public String toString() {
        return "Skill{" +
               "id=" + id +
               ", originId='" + originId + '\'' +
               ", code='" + code + '\'' +
               '}';
    }
}

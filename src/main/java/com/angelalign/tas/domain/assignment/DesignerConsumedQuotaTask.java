package com.angelalign.tas.domain.assignment;

import com.angelalign.tas.domain.assignment.enums.PhaseType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.persistence.*;
import java.util.HashMap;
import java.util.Map;

@Entity(name = "ta_designer_consumed_quota_task")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DesignerConsumedQuotaTask {
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @Id
    private Long id;
    @Column(name = "task_id")
    private Long taskId;
    @ManyToOne
    @JoinColumn(name = "designer_id")
    private Designer designer;
    @ManyToOne
    private TaskType taskType;
    @Column(name = "consumed_count_quota")
    @Builder.Default
    private Float consumedCountQuota = 0f;
    @Column(name = "consumed_minute_quota")
    private Integer consumedMinuteQuota;
    @Column(name = "dentist_code")
    private String dentistCode;
    @ElementCollection
    @CollectionTable(
            name = "ta_ref_consumed_task_attributes",
            joinColumns = @JoinColumn(name = "consumed_task_id")
    )
    @MapKeyColumn(name = "attribute_key")
    @Column(name = "attribute_value", length = 10000 )
    @Builder.Default
    private Map<String, String> attributes = new HashMap<>();
    @Transient
    @Builder.Default
    private boolean isPreferTask = Boolean.FALSE;
    @Transient
    @Builder.Default
    private boolean isPreferSkill = Boolean.FALSE;
}

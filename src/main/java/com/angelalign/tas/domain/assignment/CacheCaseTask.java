package com.angelalign.tas.domain.assignment;

import com.angelalign.tas.domain.assignment.enums.PhaseType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CacheCaseTask {
    private Long id;
    private String caseCode;
    private PhaseType phaseType;
    private String taskTypeCode;
    private Long taskId;
    private Long assigneeId;
    public static  CacheCaseTask covertFromDO(CaseTask caseCode1){
        return CacheCaseTask.builder()
                .assigneeId(caseCode1.getAssigneeId())
                .taskId(caseCode1.getTaskId())
                .id(caseCode1.getId())
                .taskTypeCode(caseCode1.getTaskTypeCode())
                .phaseType(caseCode1.getPhaseType())
                .caseCode(caseCode1.getCaseCode())
                .build();
    }
}

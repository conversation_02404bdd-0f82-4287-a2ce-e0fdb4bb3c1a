package com.angelalign.tas.domain.assignment;

import com.angelalign.tas.domain.assignment.enums.PhaseType;
import lombok.*;

import javax.persistence.*;

@Entity(name = "ta_case_task")
@Table(name = "ta_case_task",
        indexes = {
                @Index(name = "idx_case_task_casecode", columnList = "caseCode ASC")
        })
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(of = {"id", "caseCode", "phaseType", "taskId","assigneeId"})
public class CaseTask {
    @Id
    @GeneratedValue
    private Long id;
    private String caseCode;
    private PhaseType phaseType;
    private String taskTypeCode;
    private Long taskId;
    private Long assigneeId;
    @ManyToOne
    @JoinColumn(name = "problem_id")
    private Problem problem;
}

package com.angelalign.tas.domain.assignment;

import lombok.*;

import javax.persistence.*;
import java.io.Serial;
import java.io.Serializable;

@Entity(name = "ta_capacity_task_type_quota")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(exclude = "capacity")
public class CapacityTaskTypeQuota implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @Id
    private Long id;
    @Column(name = "quota")
    private Integer quota;
    @ManyToOne(fetch = FetchType.EAGER)
    @JoinColumn(name = "capacity_id")
    private Capacity capacity;
    @Column(name = "task_type_code")
    private String taskTypeCode;
}

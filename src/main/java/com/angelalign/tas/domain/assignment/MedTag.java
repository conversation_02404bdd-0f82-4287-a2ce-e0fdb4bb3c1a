package com.angelalign.tas.domain.assignment;

import lombok.*;
import org.optaplanner.core.api.domain.entity.PlanningEntity;

import javax.persistence.Entity;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.ManyToOne;

@PlanningEntity
@Entity(name = "ta_med_tag")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(exclude = "problem")
public class MedTag {
    @Id
    @GeneratedValue
    private Long id;
    @ManyToOne
    private Problem problem;
    private String originId;
    private String code;
    private Integer levelScore;

    @Override
    public String toString() {
        return "MedTag{" +
               "originId='" + originId + '\'' +
               ", code='" + code + '\'' +
               ", levelScore=" + levelScore +
               '}';
    }
}

package com.angelalign.tas.domain.assignment;

import com.angelalign.tas.score.base.util.DesignerIdentityUtil;
import com.angelalign.tas.domain.assignment.enums.DesignerRankEnum;
import com.angelalign.tas.domain.solution.SolutionFactory;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.google.common.collect.Sets;
import lombok.*;
import net.bytebuddy.utility.RandomString;
import org.apache.commons.lang3.StringUtils;
import org.optaplanner.core.api.score.buildin.bendablelong.BendableLongScore;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.Comparator;
import java.util.Set;
import java.util.stream.Collectors;

@Entity(name = "ta_problem")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class Problem {
    @Id
    @GeneratedValue
    private Long id;
    private String code;
    private String client;
    private String version;
    private String score;
    private String solverName;
    private ProblemStatus status;
    private String callBackUrl;
    private LocalDate created;
    private LocalDate lastUpdated;
    @ToString.Exclude
    @OneToMany(mappedBy = "problem", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private Set<MedTag> medTags;
    @ToString.Exclude
    @OneToMany(mappedBy = "problem", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private Set<Skill> skills;
    @ToString.Exclude
    @OneToMany(mappedBy = "problem", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private Set<TaskType> taskTypes;
    @ToString.Exclude
    @OneToMany(mappedBy = "problem", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private Set<Designer> designers;
    @ToString.Exclude
    @OneToMany(mappedBy = "problem", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private Set<Dentist> dentists;
    @ToString.Exclude
    @OneToMany(mappedBy = "problem", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private Set<Task> tasks;
    @ToString.Exclude
    @OneToMany(mappedBy = "problem", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    private Set<CaseTask> caseTasks;

    public BaseSolution convertToSolution() {
        BaseSolution baseSolution = SolutionFactory.createSolution(this);
        baseSolution.setTaskList(this.tasks.stream()
                .sorted(Comparator.comparing(Task::getPriorityScore).reversed())
                .peek(Task::init)
                .toList());
        baseSolution.setProblem(this);
        baseSolution.setAccepted(true);
//
        if (DesignerIdentityUtil.enableFakerDesigner()) {
            designers.add(buildFakerDesigner());
        }
        baseSolution.setDesignerList(
                this.designers.stream()
                        .peek(Designer::init)
                        .collect(Collectors.toSet())
        );
        if (StringUtils.isNotBlank(score)) {
            baseSolution.setScore(BendableLongScore.parseScore(score));
        }
        return baseSolution;
    }

    public Designer buildFakerDesigner() {
        Designer designer = new Designer();
        designer.setId((long) (-1));
        designer.setCode("faker designer");
        designer.setCapacity(new Capacity());
        designer.setOnDuty(Boolean.TRUE);
        designer.setRank(DesignerRankEnum.FAKER);
        designer.setOriginId(RandomString.make(5));
        designer.setSkills(Sets.newHashSet());
        designer.setTasks(Sets.newHashSet());
        designer.setDesignerConsumedQuotaTasks(Sets.newHashSet());
        designer.setProblem(this);
        return designer;
    }
}

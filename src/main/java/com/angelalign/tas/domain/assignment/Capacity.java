package com.angelalign.tas.domain.assignment;

import com.angelalign.tas.domain.assignment.enums.CaseAllocateTypeEnum;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.*;

import javax.persistence.*;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.Set;

@Entity(name = "ta_capacity")
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(exclude = "designer")
public class Capacity implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "id")
    @Id
    private Long id;
    @OneToOne

    private Designer designer;
    @Column(name = "over_allocation_limit")
    @Builder.Default
    private Integer overAllocationLimit = 0;
    @Column(name = "modify_design_exclusive")
    @Builder.Default
    private Boolean modifyDesignExclusive = Boolean.FALSE;
    @Column(name = "allocate_type")
    @Enumerated(EnumType.STRING)
    private CaseAllocateTypeEnum caseAllocateType;
    @Column(name = "workingday")
    private LocalDate workingday;
    @Column(name = "original_quota")
    private Integer originalQuota;
    @OneToMany(mappedBy = "capacity", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.EAGER)
    @JsonIgnoreProperties(value = {"capacityInfo"}, allowSetters = true)
    @ToString.Exclude
    @Builder.Default
    private Set<CapacityTaskTypeQuota> capacityTaskTypeQuotas = Set.of();

    public Capacity id(Long saveCapacityId) {
        this.setId(saveCapacityId);
        return this;
    }
}

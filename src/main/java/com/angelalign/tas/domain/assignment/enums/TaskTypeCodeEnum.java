package com.angelalign.tas.domain.assignment.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.Optional;

@Getter
@AllArgsConstructor
public enum TaskTypeCodeEnum {
    executeCaseAndTeamConfig("获取病例和设计组配置"),
    checkSGApplicable("判断是否能用SG"),
    executeSGwork("执行SG目标位设计任务"),
    executeSGFullDesignWork("执行SG全设计任务"),
    executeSGSubStepDesignWork("执行SG分步设计任务"),
    executeCaseInfoAnalysis("海外抽取病例信息任务"),
    executeCaseInfoAnalysisDomestic("国内抽取病例信息任务"),
    executeDfficultyAnalysis("海外难度分析任务"),
    executeDfficultyAnalysisDomestic("国内难度分析任务"),
    executeFullDesign("全设计"),
    executeFullDesignReview("全设计回检"),
    executeSendDesign("发送设计任务"),
    processFinish("结束流程"),
    initialOrderVariables("初始化订单要求"),
    executeMpWork("MP自动切牙任务"),
    executeOcclusalCorrection("咬合调整任务"),
    executeManualSegment("人工数字化"),
    executeAutoIrsFuse("自动牙根融合任务"),
    executeManualCheckIrsFuseResult("IRS人工检查融合结果任务"),
    executeIrsFuseReview("IRS融合回检"),
    executeIrsBlueNonConformanceReport("IRS蓝不合格报告"),
    executeIrsGoldNonConformanceReport("IRS金不合格报告"),
    executeMppWork("虚拟牙根MPP软件服务"),
    executeSaveDesignInfoAndCompensation(""),
    executeFullDesignModification("全设计修改"),
    executeOverwriteDesign("覆盖设计"),
    executeDesignPreference("设计偏好工单"),
    executeStructuredPreference("偏好结构化工单"),
    executeFullDesignReplace("方案替换工单"),
    mergeOrderVariables("合并订单变量"),
    executeDesignConversion("方案转换工单"),
    executeDesignOdsQc("ODS质检工单"),
    executeDesignSelection("方案筛选工单"),
    executeDesignSelectionReview("方案筛选回检工单"),
    executeDesign3dDentition("3D排牙工单"),
    executeDesign3dDentitionReview("3D排牙回检工单"),
    executeDesignTargetPosition("目标位工单"),
    executeDesignTargetPositionReview("目标位回检工单"),
    executeDesignSubStep("分步设计工单"),
    executeDesignSubStepReview("分布设计回检工单"),
    executeDesignPreCheck("方案预检工单"),
    executeArrangeSteps("精排分步工单"),
    executeCompensationWork("执行智美或转矩任务"),
    executeDiffTreatmentPlanWork("DTP自动化工单"),
    executeUploadIrsResult("上传irs牙根融合结果"),
    executeQualityInspection("质检工单"),
    executeQualityInspectionModification("质检修改工单"),
    executeAutoIrsFuseB("IRS牙根融合-B"),
    executeCbctAResult("获取IRS牙根融合A的结果"),
    executeMooeliCheck("mooeli检测工单"),
    executeArrangeAnalysis("抽取排牙信息任务"),
    executeArrangeDifficultyAnalysis("排牙难度分析任务"),
    executeQualityRandomInspectionSelectionDesign("方案筛选抽检工单"),
    executeQualityRandomInspectionFullDesign("全设计抽检工单"),
    ;

    private final String description;

    public static String getDescription(String code) {
        try {
            return valueOf(code).description;
        } catch (Exception e) {
            return "";
        }
    }

    //国内设计工单类型
    public static List<String> getCNDesignTaskCode() {
        return List.of(executeDesignSelection.name(),
                executeDesign3dDentition.name(),
                executeDesignTargetPosition.name(),
                executeDesignSubStep.name());
    }

    //国内设计回检工单类型
    public static List<String> getCNDesignReviewTaskCode() {
        return List.of(executeDesignSelectionReview.name(),
                executeDesign3dDentitionReview.name(),
                executeDesignTargetPositionReview.name(),
                executeDesignSubStepReview.name());
    }

    //新设计阶段 最后一步设计的工单类型
    public static List<String> getLastStepDesignTaskCode() {
        return List.of(executeFullDesign.name(), executeDesignSubStep.name(), executeArrangeSteps.name());
    }

    //回检工单类型
    public static List<String> getReviewTaskCode() {
        return List.of(executeFullDesignReview.name(),
                executeDesignSelectionReview.name(),
                executeDesign3dDentitionReview.name(),
                executeDesignTargetPositionReview.name(),
                TaskTypeCodeEnum.executeIrsFuseReview.name(),
                executeDesignSubStepReview.name());
    }

    //可以使用智美转矩的工单类型
    public static List<String> useCompensationTaskCode() {
        return List.of(executeFullDesignModification.name(), executeDesignSubStep.name());
    }

    public static Boolean isDesignTask(String code) {
        return TaskTypeCodeEnum.executeFullDesign.name().equals(code)
               || TaskTypeCodeEnum.executeFullDesignModification.name().equals(code)
               || TaskTypeCodeEnum.executeFullDesignReplace.name().equals(code)
               || TaskTypeCodeEnum.executeDesignSubStep.name().equals(code)
               || TaskTypeCodeEnum.executeArrangeSteps.name().equals(code)
               || TaskTypeCodeEnum.executeOverwriteDesign.name().equals(code);
    }
    public static Boolean isDdmTask(String code) {
        return TaskTypeCodeEnum.executeManualSegment.name().equals(code)
               || TaskTypeCodeEnum.executeIrsFuseReview.name().equals(code)
               || TaskTypeCodeEnum.executeOcclusalCorrection.name().equals(code)
               || TaskTypeCodeEnum.executeManualCheckIrsFuseResult.name().equals(code);
    }


}

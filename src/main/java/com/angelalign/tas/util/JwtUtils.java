package com.angelalign.tas.util;

import org.apache.commons.lang.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class JwtUtils {

    public static boolean isTokenExpired(String token) {
        String[] parts = token.split("\\.");
        if (parts.length != 3) {
            throw new IllegalArgumentException("Invalid JWT token");
        }

        String payload = new String(Base64.getUrlDecoder().decode(parts[1]), StandardCharsets.UTF_8);
        Map<String, Object> claims = parseJson(payload);

        if (claims.containsKey("exp")) {
            long expirationTime = ((Number) claims.get("exp")).longValue() * 1000; // 转换为毫秒
            return new Date().after(new Date(expirationTime));
        }

        throw new IllegalArgumentException("JWT does not contain an expiration time (exp)");
    }

    public static Map<String, Object> parseJson(String json) {
        Map<String, Object> result = new HashMap<>();
        if (StringUtils.isBlank(json)) return result;
        String[] keyValuePairs = json.replace("{", "").replace("}", "").split(",");

        for (String pair : keyValuePairs) {
            String[] entry = pair.split(":");
            if (entry.length == 2) {
                String key = entry[0].replace("\"", "").trim();
                String value = entry[1].replace("\"", "").trim();
                result.put(key, parseValue(value));
            }
        }

        return result;
    }

    private static Object parseValue(String value) {
        if (value.equalsIgnoreCase("true") || value.equalsIgnoreCase("false")) {
            return Boolean.parseBoolean(value);
        } else if (value.matches("-?\\d+")) {
            return Long.parseLong(value);
        } else if (value.matches("-?\\d+\\.\\d+")) {
            return Double.parseDouble(value);
        } else {
            return value;
        }
    }
}

package com.angelalign.tas.util;

import java.math.BigDecimal;
import java.math.RoundingMode;

public class DecimalUtils {
    public static int preciseRound(double num) {
        return (int) preciseRound(num, 0);
    }

    private static final double[] POWERS_OF_10 = {
            1.0, 10.0, 100.0, 1000.0, 10000.0, 100000.0
    };

    public static float preciseRound(double num, int scale) {
        if (scale == 0) {
            return Math.round(num);
        }

        if (scale < 0 || scale >= POWERS_OF_10.length) {
            return new BigDecimal(num).setScale(scale, RoundingMode.HALF_UP).floatValue();
        }

        double multiplier = POWERS_OF_10[scale];
        return (float) (Math.round(num * multiplier) / multiplier);
    }

    public static float upRound(double num) {
        BigDecimal bd = new BigDecimal(num);
        return bd.setScale(0, RoundingMode.UP).intValue();
    }

    /**
     * 放大输入的小数，直到整数部分不为0，返回放大后的整数部分
     *
     * @param num 输入的小数（支持正负）
     * @return 放大后的整数部分（如0.00123 → 1，-0.002 → -2）
     */
    public static int amplifyUntilNonZero(double num) {
        if (num == 0) {
            return 0;
        }
        BigDecimal value = new BigDecimal(Double.toString(num));
        int scale = 0;
        while (true) {
            BigDecimal amplified = value.multiply(BigDecimal.TEN.pow(scale));
            int integerPart = amplified.intValue();
            if (integerPart != 0) {
                return integerPart;
            }
            scale++;
        }
    }
}

package com.angelalign.tas.util;

import com.fasterxml.jackson.core.JsonFactory;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.TreeNode;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.ObjectUtils;

import java.io.File;
import java.io.IOException;
import java.io.Writer;
import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class Jackson {
    @Getter
    private static final ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    static {
        objectMapper.configure(JsonParser.Feature.ALLOW_COMMENTS, true);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
        objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_ARRAY_AS_NULL_OBJECT, true);
        objectMapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
    }

    @Getter
    private static final ObjectWriter writer = objectMapper.writer();
    private static final ObjectWriter prettyWriter = objectMapper.writerWithDefaultPrettyPrinter();

    public static String toJsonPrettyString(Object value) {
        try {
            return prettyWriter.writeValueAsString(value);
        } catch (Exception e) {
            throw new IllegalStateException(e);
        }
    }

    public static String toJsonString(Object value) {
        try {
            if (ObjectUtils.isEmpty(value)) return "";
            return writer.writeValueAsString(value);
        } catch (Exception e) {
            throw new IllegalStateException(e);
        }
    }

    public static JsonNode getOrDefault(JsonNode node, String fieldName, JsonNode defaultValue) {
        JsonNode result = node.get(fieldName);
        return result != null ? result : defaultValue;
    }

    public static String getOrDefault(JsonNode node, String fieldName, String defaultValue) {
        JsonNode result = node.get(fieldName);
        return result != null ? result.asText() : defaultValue;
    }

    /**
     * Returns the deserialized object from the given json string and target
     * class; or null if the given json string is null.
     */
    public static <T> T fromJsonString(String json, Class<T> clazz) {
        if (StringUtils.isBlank(json))
            return null;
        try {
            return objectMapper.readValue(json, clazz);
        } catch (Exception e) {
            throw new RuntimeException("Unable to parse Json String.", e);
        }
    }

    public static <T> List<T> covertObjectListFromJsonString(String content, Class<T> valueType) {
        if (StringUtils.isBlank(content)) return new ArrayList<>();
        TypeReference<List<T>> typeReference = new TypeReference<>() {
            @Override
            public Type getType() {
                return Jackson.getObjectMapper().getTypeFactory().constructCollectionType(List.class, valueType);
            }
        };
        return Jackson.fromJsonString(content, typeReference);
    }

    public static <T> T fromJsonString(String content, TypeReference<T> valueTypeRef) {
        T t;
        try {
            t = objectMapper.readValue(content, valueTypeRef);
        } catch (Exception e) {
            throw new RuntimeException("Unable to parse Json String.", e);
        }
        return t;
    }

    public static <T> T treeToValue(TreeNode node, Class<T> clazz) {
        if (Objects.isNull(node))
            return null;
        try {
            return objectMapper.treeToValue(node, clazz);
        } catch (Exception e) {
            throw new RuntimeException("Unable to parse Json String.", e);
        }
    }

    public static JsonNode jsonNodeOf(String json) {
        if (StringUtils.isBlank(json)) return objectMapper.createObjectNode();
        return fromJsonString(json, JsonNode.class);
    }

    public static <T> T convertValue(Object object, TypeReference<T> valueTypeRef) {
        T t;
        try {
            t = objectMapper.convertValue(object, valueTypeRef);
        } catch (Exception e) {
            throw new RuntimeException("Unable to parse Json String.", e);
        }
        return t;
    }

    public static JsonGenerator jsonGeneratorOf(Writer writer) throws IOException {
        return new JsonFactory().createGenerator(writer);
    }

    public static <T> T loadFrom(File file, Class<T> clazz) throws IOException {
        try {
            return objectMapper.readValue(file, clazz);
        } catch (IOException e) {
            throw e;
        } catch (Exception e) {
            throw new IllegalStateException(e);
        }
    }

    public static Optional<JsonNode> getJsonNodeByName(JsonNode json, String fieldName) {
        return json.has(fieldName) ? Optional.of(json.get(fieldName)) : Optional.empty();
    }
}

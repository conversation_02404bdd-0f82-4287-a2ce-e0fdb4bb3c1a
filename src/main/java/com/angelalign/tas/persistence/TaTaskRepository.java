package com.angelalign.tas.persistence;

import com.angelalign.tas.domain.assignment.Task;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.data.repository.query.Param;

import java.util.List;

public interface TaTaskRepository extends CrudRepository<Task, Long> {

    @Override
    List<Task> findAll();

    @Query(value = "SELECT * from ta_task WHERE origin_id = :originId ORDER BY id DESC",nativeQuery = true)
    List<Task> findByOriginId(@Param("originId") String originId);

}

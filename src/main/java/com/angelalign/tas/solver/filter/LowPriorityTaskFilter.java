package com.angelalign.tas.solver.filter;

import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import org.optaplanner.core.api.score.director.ScoreDirector;
import org.optaplanner.core.impl.heuristic.selector.common.decorator.SelectionFilter;

import java.util.Optional;

public class LowPriorityTaskFilter implements SelectionFilter<BaseSolution,Task> {

    @Override
    public boolean accept(ScoreDirector<BaseSolution> scoreDirector, Task selection) {
        return Optional.ofNullable(selection.getPriorityScore()).orElse(-1) <= 500;
    }
}

package com.angelalign.tas.solver.filter;

import com.angelalign.tas.score.base.util.DesignerIdentityUtil;
import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import org.optaplanner.core.api.score.director.ScoreDirector;
import org.optaplanner.core.impl.heuristic.selector.common.decorator.SelectionFilter;

import java.util.Optional;

public class AvailableDesignerFilter implements SelectionFilter<BaseSolution, Designer> {
    @Override
    public boolean accept(ScoreDirector<BaseSolution> scoreDirector, Designer selection) {
        return Optional.ofNullable(selection.onDuty).orElse(Boolean.FALSE) && DesignerIdentityUtil.isAvailableDesigner(selection);
    }
}

package com.angelalign.tas.solver.listener;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.DesignerConsumedQuotaTask;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.parser.DesignerAttributeParser;
import com.angelalign.tas.score.base.util.DesignerCapacityConvertor;
import com.angelalign.tas.score.base.util.DesignerPreferTaskOrSkillUtil;
import com.angelalign.tas.score.base.util.ProblemUtil;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.domain.variable.VariableListener;
import org.optaplanner.core.api.score.director.ScoreDirector;

import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 偏好技能额度变量监听器
 * 用于增量更新偏好技能相关的计算值，避免重复计算
 *
 * 监听 Task.designer 变量的变化，增量更新：
 * 1. 设计师的偏好技能总额度
 * 2. 全局偏好技能总额度
 * 3. 设计师偏差值
 */
@Slf4j
public class PreferSkillQuotaVariableListener implements VariableListener<Object, Task> {
    
    // 设计师偏好技能总额度缓存（包含历史消耗）
    private final ConcurrentHashMap<String, Double> designerTotalPreferSkillQuotaCache = new ConcurrentHashMap<>();
    
    // 全局偏好技能总额度缓存
    private final ConcurrentHashMap<Long, Double> globalPreferSkillTotalQuotaCache = new ConcurrentHashMap<>();
    
    // 设计师偏差值缓存
    private final ConcurrentHashMap<String, Double> designerDeviationCache = new ConcurrentHashMap<>();
    
    // 设计师历史偏好技能额度缓存（不变的部分）
    private final ConcurrentHashMap<String, Double> designerHistoricalPreferSkillQuotaCache = new ConcurrentHashMap<>();

    @Override
    public void beforeEntityAdded(ScoreDirector<Object> scoreDirector, Task task) {
        // 新增实体时无需处理
    }

    @Override
    public void afterEntityAdded(ScoreDirector<Object> scoreDirector, Task task) {
        // 新增实体后更新缓存
        if (task.getDesigner() != null) {
            updateCacheForDesignerChange(task, null, task.getDesigner());
        }
    }

    @Override
    public void beforeVariableChanged(ScoreDirector<Object> scoreDirector, Task task) {
        // 变量变化前无需处理，在 afterVariableChanged 中处理
    }

    @Override
    public void afterVariableChanged(ScoreDirector<Object> scoreDirector, Task task) {
        // 获取变化前后的设计师
        Designer oldDesigner = (Designer) scoreDirector.lookUpWorkingObject(task.getDesigner());
        Designer newDesigner = task.getDesigner();

        updateCacheForDesignerChange(task, oldDesigner, newDesigner);
    }

    @Override
    public void beforeEntityRemoved(ScoreDirector<Object> scoreDirector, Task task) {
        // 实体移除前更新缓存
        if (task.getDesigner() != null) {
            updateCacheForDesignerChange(task, task.getDesigner(), null);
        }
    }

    @Override
    public void afterEntityRemoved(ScoreDirector<Object> scoreDirector, Task task) {
        // 实体移除后无需处理
    }

    /**
     * 更新设计师变化相关的缓存
     */
    private void updateCacheForDesignerChange(Task task, Designer oldDesigner, Designer newDesigner) {
        try {
            // 更新旧设计师的缓存
            if (oldDesigner != null) {
                updateDesignerCache(oldDesigner, task, false);
            }
            
            // 更新新设计师的缓存
            if (newDesigner != null) {
                updateDesignerCache(newDesigner, task, true);
            }
            
            // 更新全局缓存
            if (oldDesigner != null || newDesigner != null) {
                Long problemId = (oldDesigner != null) ? oldDesigner.getProblem().getId() : newDesigner.getProblem().getId();
                globalPreferSkillTotalQuotaCache.remove(problemId);
            }
            
        } catch (Exception e) {
            log.warn("Error updating prefer skill quota cache for task {}: {}", task.getCode(), e.getMessage());
            // 发生错误时清空相关缓存，确保数据一致性
            clearCacheForProblem(task);
        }
    }

    /**
     * 更新单个设计师的缓存
     */
    private void updateDesignerCache(Designer designer, Task task, boolean isAdding) {
        String designerKey = getDesignerCacheKey(designer);
        
        // 检查任务是否为偏好技能任务
        List<String> preferSkillExpression = DesignerAttributeParser.getPreferSkillExpression(designer);
        if (preferSkillExpression.isEmpty()) {
            return; // 设计师没有偏好技能，无需更新
        }
        
        boolean isPreferSkillTask = DesignerPreferTaskOrSkillUtil.getDesignerPreferSkillTask(preferSkillExpression, Set.of(task)).contains(task);
        if (!isPreferSkillTask) {
            return; // 不是偏好技能任务，无需更新
        }
        
        // 计算任务额度
        double taskQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, Set.of(task));
        
        // 增量更新设计师偏好技能总额度
        designerTotalPreferSkillQuotaCache.compute(designerKey, (key, currentValue) -> {
            if (currentValue == null) {
                // 缓存不存在，重新计算
                return calculateDesignerTotalPreferSkillQuota(designer);
            } else {
                // 增量更新
                return isAdding ? currentValue + taskQuota : currentValue - taskQuota;
            }
        });
        
        // 清除偏差值缓存，因为它依赖于总额度
        designerDeviationCache.remove(designerKey);
    }

    /**
     * 计算设计师总的偏好技能额度（用于缓存初始化）
     */
    private double calculateDesignerTotalPreferSkillQuota(Designer designer) {
        List<String> preferSkillExpression = DesignerAttributeParser.getPreferSkillExpression(designer);
        
        // 本次分配的偏好技能工单
        Set<Task> preferSkillTask = DesignerPreferTaskOrSkillUtil.getDesignerPreferSkillTask(preferSkillExpression, designer.getTasks());
        Double currentPreferSkillQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferSkillTask);
        
        // 历史已消耗的偏好技能额度
        Double historicalQuota = getDesignerHistoricalPreferSkillQuota(designer);
        
        return currentPreferSkillQuota + historicalQuota;
    }

    /**
     * 获取设计师历史偏好技能额度（缓存不变的部分）
     */
    private double getDesignerHistoricalPreferSkillQuota(Designer designer) {
        String designerKey = getDesignerCacheKey(designer);
        return designerHistoricalPreferSkillQuotaCache.computeIfAbsent(designerKey, key -> {
            List<String> preferSkillExpression = DesignerAttributeParser.getPreferSkillExpression(designer);
            List<DesignerConsumedQuotaTask> consumedPreferSkillTask = DesignerPreferTaskOrSkillUtil.getDesignerConsumedPreferSkillTask(preferSkillExpression, designer);
            return DesignerCapacityConvertor.covertConsumedTaskQuota(designer, consumedPreferSkillTask);
        });
    }

    /**
     * 生成设计师缓存键
     */
    private String getDesignerCacheKey(Designer designer) {
        return designer.getProblem().getId() + "_" + designer.getCode();
    }

    /**
     * 清空问题相关的所有缓存
     */
    private void clearCacheForProblem(Task task) {
        if (task.getDesigner() != null) {
            Long problemId = task.getDesigner().getProblem().getId();
            globalPreferSkillTotalQuotaCache.remove(problemId);
            
            // 清空该问题下所有设计师的缓存
            designerTotalPreferSkillQuotaCache.entrySet().removeIf(entry -> 
                entry.getKey().startsWith(problemId + "_"));
            designerDeviationCache.entrySet().removeIf(entry -> 
                entry.getKey().startsWith(problemId + "_"));
        }
    }

    /**
     * 获取缓存的设计师偏好技能总额度
     */
    public Double getCachedDesignerTotalPreferSkillQuota(Designer designer) {
        String designerKey = getDesignerCacheKey(designer);
        return designerTotalPreferSkillQuotaCache.computeIfAbsent(designerKey, 
            key -> calculateDesignerTotalPreferSkillQuota(designer));
    }

    /**
     * 获取缓存的全局偏好技能总额度
     */
    public Double getCachedGlobalPreferSkillTotalQuota(Long problemId) {
        return globalPreferSkillTotalQuotaCache.get(problemId);
    }

    /**
     * 获取缓存的设计师偏差值
     */
    public Double getCachedDesignerDeviation(Designer designer) {
        String designerKey = getDesignerCacheKey(designer);
        return designerDeviationCache.computeIfAbsent(designerKey, key -> {
            // 计算偏差值
            double expectedQuota = ProblemUtil.getDesignerPreferSkillExpectedQuota(designer);
            double consumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);
            
            List<String> preferSkillExpression = DesignerAttributeParser.getPreferSkillExpression(designer);
            Set<Task> preferSkillTask = DesignerPreferTaskOrSkillUtil.getDesignerPreferSkillTask(preferSkillExpression, designer.getTasks());
            Double actualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferSkillTask);
            
            return expectedQuota - consumedQuota - actualQuota;
        });
    }

    /**
     * 清空所有缓存
     */
    public void clearAllCache() {
        designerTotalPreferSkillQuotaCache.clear();
        globalPreferSkillTotalQuotaCache.clear();
        designerDeviationCache.clear();
        designerHistoricalPreferSkillQuotaCache.clear();
    }

    /**
     * 获取缓存统计信息
     */
    public String getCacheStats() {
        return String.format(
            "PreferSkillQuotaVariableListener Cache Stats:\n" +
            "  DesignerTotalQuotaCache: %d entries\n" +
            "  GlobalQuotaCache: %d entries\n" +
            "  DeviationCache: %d entries\n" +
            "  HistoricalQuotaCache: %d entries",
            designerTotalPreferSkillQuotaCache.size(),
            globalPreferSkillTotalQuotaCache.size(),
            designerDeviationCache.size(),
            designerHistoricalPreferSkillQuotaCache.size()
        );
    }
}

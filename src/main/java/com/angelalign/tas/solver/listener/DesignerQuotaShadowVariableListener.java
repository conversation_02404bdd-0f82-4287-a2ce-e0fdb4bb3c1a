package com.angelalign.tas.solver.listener;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.parser.ConsumedQuotaTaskAttributeParser;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.score.base.util.DesignerCapacityConvertor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.optaplanner.core.api.domain.variable.VariableListener;
import org.optaplanner.core.api.score.director.ScoreDirector;

import java.util.Set;

/**
 * Designer额度影子变量监听器
 * 当Designer的tasks集合发生变化时，自动更新assignedQuota和appointedTaskQuota影子变量
 */
@Slf4j
public class DesignerQuotaShadowVariableListener implements VariableListener<BaseSolution, Designer> {

    @Override
    public void beforeEntityAdded(ScoreDirector<BaseSolution> scoreDirector, Designer designer) {
        // 实体添加前，确保影子变量有初始值
        if (designer.assignedQuota == null) {
            designer.assignedQuota = 0.0;
        }
        if (designer.appointedTaskQuota == null) {
            designer.appointedTaskQuota = 0.0;
        }
    }

    @Override
    public void afterEntityAdded(ScoreDirector<BaseSolution> scoreDirector, Designer designer) {
        // 实体添加后，更新影子变量
        updateShadowVariables(scoreDirector, designer);
    }

    @Override
    public void beforeVariableChanged(ScoreDirector<BaseSolution> scoreDirector, Designer designer) {
        // 变量变化前无需特殊处理
    }

    @Override
    public void afterVariableChanged(ScoreDirector<BaseSolution> scoreDirector, Designer designer) {
        // tasks集合变化后，重新计算影子变量
        updateShadowVariables(scoreDirector, designer);
    }

    @Override
    public void beforeEntityRemoved(ScoreDirector<BaseSolution> scoreDirector, Designer designer) {
        // 实体移除前无需特殊处理
    }

    @Override
    public void afterEntityRemoved(ScoreDirector<BaseSolution> scoreDirector, Designer designer) {
        // 实体移除后无需特殊处理
    }

    /**
     * 初始化影子变量
     */
    private void initializeShadowVariables(Designer designer) {
        if (designer.assignedQuota == null) {
            designer.assignedQuota = 0.0;
        }
        if (designer.appointedTaskQuota == null) {
            designer.appointedTaskQuota = 0.0;
        }
    }

    /**
     * 更新影子变量
     */
    private void updateShadowVariables(ScoreDirector<BaseSolution> scoreDirector, Designer designer) {
        if (designer == null) {
            return;
        }

        // 更新assignedQuota影子变量
        Double newAssignedQuota = calculateAssignedQuota(designer);
        // 使用Objects.equals处理null值比较
        if (!java.util.Objects.equals(newAssignedQuota, designer.assignedQuota)) {
            scoreDirector.beforeVariableChanged(designer, "assignedQuota");
            designer.assignedQuota = newAssignedQuota;
            scoreDirector.afterVariableChanged(designer, "assignedQuota");
        }

        // 更新appointedTaskQuota影子变量
        Double newAppointedTaskQuota = calculateAppointedTaskQuota(designer);
        // 使用Objects.equals处理null值比较
        if (!java.util.Objects.equals(newAppointedTaskQuota, designer.appointedTaskQuota)) {
            scoreDirector.beforeVariableChanged(designer, "appointedTaskQuota");
            designer.appointedTaskQuota = newAppointedTaskQuota;
            scoreDirector.afterVariableChanged(designer, "appointedTaskQuota");
        }
    }

    /**
     * 计算分配的总额度（历史消耗 + 当前分配）
     */
    private Double calculateAssignedQuota(Designer designer) {
        // 历史消耗额度
        double consumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);

        // 当前分配的任务额度
        double assignedQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer);

        return consumedQuota + assignedQuota;
    }

    /**
     * 计算指定任务额度（优先分配的任务额度）
     */
    private Double calculateAppointedTaskQuota(Designer designer) {
        // 从当前分配的任务中计算指定任务额度
        double currentAppointedQuota = designer.getTasks()
                .stream()
                .filter(task -> CollectionUtils.isNotEmpty(task.getPreferredDesigner()))
                .filter(task -> task.getPreferredDesigner().stream()
                        .anyMatch(d -> d.getCode().equals(designer.getCode())))
                .mapToDouble(task -> DesignerCapacityConvertor.covertAssignedTaskQuota(designer, Set.of(task)))
                .sum();

        // 从历史消耗任务中计算指定任务额度
        double historicalAppointedQuota = ConsumedQuotaTaskAttributeParser.getDesignerAppointedTaskQuota(designer);

        return currentAppointedQuota + historicalAppointedQuota;
    }
}

package com.angelalign.tas.solver.version;

import com.angelalign.tas.solver.version.base.SolverVersionRouter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Component
public class SolverVersionRouterFactory {

    @Autowired
    private List<SolverVersionRouter> routers;

    private final Map<String, SolverVersionRouter> routerMap = new HashMap<>();

    @PostConstruct
    public void init() {
        for (SolverVersionRouter router : routers) {
            routerMap.put(router.getSolverName(), router);
        }
    }

    /**
     * 获取路由实现
     *
     * @param solverName 求解器名称
     * @return 路由实现
     */
    public SolverVersionRouter getRouter(String solverName) {
        SolverVersionRouter router = routerMap.get(solverName);
        if (router == null) {
            throw new IllegalArgumentException("No router found for solver: " + solverName);
        }
        return router;
    }

    /**
     * 检查求解器是否支持
     *
     * @param solverName 求解器名称
     * @return 是否支持
     */
    public boolean isSolverSupported(String solverName) {
        return routerMap.containsKey(solverName);
    }
} 
package com.angelalign.tas.solver.version.base;

import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;

import java.util.List;


public interface SolverVersionRouter {
    String getSolverName();

    /**
     * 路由解决方案类
     *
     * @return 解决方案实例
     */
    Class<? extends BaseSolution> routeSolution();

    /**
     * 路由约束提供者类
     *
     * @return 约束提供者实例
     */
    Class<? extends BaseConstraintProvider> routeConstraintProvider(String version);

    /**
     * 路由阶段命令列表
     *
     * @param version 版本号
     * @return 阶段命令列表
     */
    List<Class<? extends AbstractCustomPhaseCommand>> routePhaseCommands(String version);

    /**
     * 获取硬约束级别数量
     *
     * @param version 版本号
     * @return 硬约束级别数量
     */
    int getHardLevelSize(String version);

    /**
     * 获取软约束级别数量
     *
     * @param version 版本号
     * @return 软约束级别数量
     */
    int getSoftLevelSize(String version);

    /**
     * 是否支持多阶段
     *
     * @param version 版本号
     * @return 是否支持多阶段
     */
    boolean isMultiPhase(String version);
}
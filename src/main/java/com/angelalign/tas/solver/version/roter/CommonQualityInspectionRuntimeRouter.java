package com.angelalign.tas.solver.version.roter;

import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.domain.solution.oversea.runtime.OverSeaQualityInspectionRuntimeSolution;
import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.oversea.runtime.OverSeaQualityInspectionRuntimeTaskBaseConstraintProvider;
import com.angelalign.tas.solver.phase.NoChangeCustomPhaseCommand;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;
import com.angelalign.tas.solver.version.base.AbstractSolverVersionRouter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class CommonQualityInspectionRuntimeRouter extends AbstractSolverVersionRouter {

    private static final String SOLVER_NAME = "COMMON_QUALITY_INSPECTION_RUNTIME";

    @Override
    public String getSolverName() {
        return SOLVER_NAME;
    }

    @Override
    public Class<? extends BaseSolution> routeSolution() {
        return OverSeaQualityInspectionRuntimeSolution.class;
    }

    @Override
    public Class<? extends BaseConstraintProvider> routeConstraintProvider(String version) {
        // 目前只有一个版本的实现
        return OverSeaQualityInspectionRuntimeTaskBaseConstraintProvider.class;
    }

    @Override
    public List<Class<? extends AbstractCustomPhaseCommand>> routePhaseCommands(String version) {
        return List.of(
                NoChangeCustomPhaseCommand.class
        );
    }

    @Override
    public boolean isMultiPhase(String version) {
        return false;
    }
}

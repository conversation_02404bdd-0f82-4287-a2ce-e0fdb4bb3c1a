package com.angelalign.tas.solver.version.roter;

import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.domain.solution.oversea.schedule.OverSeaDesignScheduleSolution;
import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.oversea.schedule.OverSeaDesignScheduleConstraintProvider_V2;
import com.angelalign.tas.score.oversea.schedule.OverSeaDesignScheduleTaskHardBaseConstraintProvider;
import com.angelalign.tas.solver.phase.FullDesignModifyTaskPhaseCommand;
import com.angelalign.tas.solver.phase.TaskPreferDesignerPhaseCommand;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;
import com.angelalign.tas.solver.version.base.AbstractSolverVersionRouter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class OverseaDesignScheduleRouter extends AbstractSolverVersionRouter {

    private static final String SOLVER_NAME = "OVERSEA_DESIGN_SCHEDULE";

    @Override
    public String getSolverName() {
        return SOLVER_NAME;
    }

    @Override
    public Class<? extends BaseSolution> routeSolution() {
        return OverSeaDesignScheduleSolution.class;
    }

    @Override
    public Class<? extends BaseConstraintProvider> routeConstraintProvider(String version) {
        return switch (version.toUpperCase()) {
            case "V2" -> OverSeaDesignScheduleConstraintProvider_V2.class;
            default -> OverSeaDesignScheduleTaskHardBaseConstraintProvider.class;
        };
    }

    @Override
    public List<Class<? extends AbstractCustomPhaseCommand>> routePhaseCommands(String version) {
        return List.of(
                TaskPreferDesignerPhaseCommand.class
        );
    }

    @Override
    public boolean isMultiPhase(String version) {
        return false;
    }
} 
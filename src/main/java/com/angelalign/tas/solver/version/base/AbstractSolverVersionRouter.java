package com.angelalign.tas.solver.version.base;

import com.angelalign.tas.score.base.BaseConstraintProvider;
import lombok.SneakyThrows;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Field;

public abstract class AbstractSolverVersionRouter implements SolverVersionRouter {

    @Override
    @SneakyThrows
    public int getHardLevelSize(String version) {
        BaseConstraintProvider baseConstraintProvider = routeConstraintProvider(version).getDeclaredConstructor().newInstance();
        Field hardLevel = ReflectionUtils.findField(baseConstraintProvider.getClass()
                , "HARD_LEVEL");
        try {
            return hardLevel.getInt(baseConstraintProvider);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    @SneakyThrows
    public int getSoftLevelSize(String version) {
        BaseConstraintProvider baseConstraintProvider = routeConstraintProvider(version).getDeclaredConstructor().newInstance();
        Field hardLevel = ReflectionUtils.findField(baseConstraintProvider.getClass()
                , "SOFT_LEVEL");
        try {
            return hardLevel.getInt(baseConstraintProvider);
        } catch (IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}

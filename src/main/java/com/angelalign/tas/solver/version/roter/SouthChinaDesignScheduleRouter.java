package com.angelalign.tas.solver.version.roter;

import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.domain.solution.china.ChinaDesignScheduleSolution;
import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.china.south.SouthChinaDesignScheduleBaseConstraintProvider;
import com.angelalign.tas.solver.phase.FullDesignModifyTaskPhaseCommand;
import com.angelalign.tas.solver.phase.TaskPreferDesignerPhaseCommand;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;
import com.angelalign.tas.solver.version.base.AbstractSolverVersionRouter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class SouthChinaDesignScheduleRouter extends AbstractSolverVersionRouter {

    private static final String SOLVER_NAME = "SOUTH_CHINA_DESIGN_SCHEDULE";

    @Override
    public String getSolverName() {
        return SOLVER_NAME;
    }

    @Override
    public Class<? extends BaseSolution> routeSolution() {
        return ChinaDesignScheduleSolution.class;
    }

    @Override
    public Class<? extends BaseConstraintProvider> routeConstraintProvider(String version) {
        // 华南设计调度是一个新策略，目前只有一个实现
        return SouthChinaDesignScheduleBaseConstraintProvider.class;
    }

    @Override
    public List<Class<? extends AbstractCustomPhaseCommand>> routePhaseCommands(String version) {
        return List.of(
                FullDesignModifyTaskPhaseCommand.class,
                TaskPreferDesignerPhaseCommand.class
        );
    }

    @Override
    public boolean isMultiPhase(String version) {
        return false;
    }
}

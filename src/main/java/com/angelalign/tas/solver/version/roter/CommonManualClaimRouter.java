package com.angelalign.tas.solver.version.roter;

import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.domain.solution.oversea.DefaultManualClaimSolution;
import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.oversea.DefaultManualClaimHardBaseConstraintProvider;
import com.angelalign.tas.solver.phase.TaskPreferDesignerPhaseCommand;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;
import com.angelalign.tas.solver.version.base.AbstractSolverVersionRouter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class CommonManualClaimRouter extends AbstractSolverVersionRouter {

    private static final String SOLVER_NAME = "COMMON_MANUAL_CLAIM";

    @Override
    public String getSolverName() {
        return SOLVER_NAME;
    }

    @Override
    public Class<? extends BaseSolution> routeSolution() {
        return DefaultManualClaimSolution.class;
    }

    @Override
    public Class<? extends BaseConstraintProvider> routeConstraintProvider(String version) {
        // 目前只有一个版本的实现
        return DefaultManualClaimHardBaseConstraintProvider.class;
    }

    @Override
    public List<Class<? extends AbstractCustomPhaseCommand>> routePhaseCommands(String version) {
        return List.of(
                TaskPreferDesignerPhaseCommand.class
        );
    }

    @Override
    public boolean isMultiPhase(String version) {
        return false;
    }
}

package com.angelalign.tas.solver.version.roter;

import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.domain.solution.china.ChinaDesignScheduleSolution;
import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.china.*;

import com.angelalign.tas.solver.phase.FullDesignModifyTaskPhaseCommand;
import com.angelalign.tas.solver.phase.TaskPreferDesignerPhaseCommand;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;
import com.angelalign.tas.solver.version.base.AbstractSolverVersionRouter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class ChinaDesignScheduleRouter extends AbstractSolverVersionRouter {

    private static final String SOLVER_NAME = "CHINA_DESIGN_SCHEDULE";

    @Override
    public String getSolverName() {
        return SOLVER_NAME;
    }

    @Override
    public Class<? extends BaseSolution> routeSolution() {
        return ChinaDesignScheduleSolution.class;
    }

    @Override
    public Class<? extends BaseConstraintProvider> routeConstraintProvider(String version) {
        return switch (version.toUpperCase()) {
            case "V2" -> ChinaDesignScheduleConstraintProvider_V2.class;
            case "V3" -> ChinaDesignScheduleConstraintProvider_V3.class;
            case "V4" -> ChinaDesignScheduleConstraintProvider_V4.class;
            case "V5" -> ChinaDesignScheduleConstraintProvider_V5.class;
            case "V6" -> ChinaDesignScheduleConstraintProvider_V6.class;
            default -> ChinaDesignScheduleBaseConstraintProvider.class;
        };
    }

    @Override
    public List<Class<? extends AbstractCustomPhaseCommand>> routePhaseCommands(String version) {
        // 所有版本都使用相同的阶段命令
        return List.of(
                FullDesignModifyTaskPhaseCommand.class,
                TaskPreferDesignerPhaseCommand.class
        );
    }

    @Override
    public boolean isMultiPhase(String version) {
        // 只有V5版本支持多阶段
        return "V5".equalsIgnoreCase(version);
    }
} 
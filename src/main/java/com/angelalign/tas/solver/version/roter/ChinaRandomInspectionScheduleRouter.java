package com.angelalign.tas.solver.version.roter;

import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.domain.solution.china.ChinaRandomInspectionRuntimeSolution;
import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.china.ChinaRandomInspectionBaseConstraintProvider;
import com.angelalign.tas.solver.phase.NoChangeCustomPhaseCommand;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;
import com.angelalign.tas.solver.version.base.AbstractSolverVersionRouter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class ChinaRandomInspectionScheduleRouter extends AbstractSolverVersionRouter {

    private static final String SOLVER_NAME = "CHINA_RANDOM_INSPECTION_SCHEDULE";

    @Override
    public String getSolverName() {
        return SOLVER_NAME;
    }

    @Override
    public Class<? extends BaseSolution> routeSolution() {
        return ChinaRandomInspectionRuntimeSolution.class;
    }

    @Override
    public Class<? extends BaseConstraintProvider> routeConstraintProvider(String version) {
        // 目前只有一个版本的实现
        return ChinaRandomInspectionBaseConstraintProvider.class;
    }

    @Override
    public List<Class<? extends AbstractCustomPhaseCommand>> routePhaseCommands(String version) {
        return List.of(
                NoChangeCustomPhaseCommand.class
        );
    }

    @Override
    public boolean isMultiPhase(String version) {
        return false;
    }
}

package com.angelalign.tas.solver;

import com.angelalign.tas.config.TasConfiguration;
import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.solver.filter.*;
import com.angelalign.tas.solver.phase.FakerDesignerCorrectionPhase;
import com.angelalign.tas.solver.phase.listener.PhaseQueueManager;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;
import com.angelalign.tas.solver.phase.enums.PhaseName;
import com.angelalign.tas.solver.version.base.SolverVersionRouter;
import com.angelalign.tas.util.ApplicationContextHolder;
import com.angelalign.tas.util.DecimalUtils;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.domain.common.DomainAccessType;
import org.optaplanner.core.api.score.stream.ConstraintStreamImplType;
import org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicPhaseConfig;
import org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicType;
import org.optaplanner.core.config.constructionheuristic.decider.forager.ConstructionHeuristicForagerConfig;
import org.optaplanner.core.config.constructionheuristic.decider.forager.ConstructionHeuristicPickEarlyType;
import org.optaplanner.core.config.constructionheuristic.placer.PooledEntityPlacerConfig;
import org.optaplanner.core.config.heuristic.selector.common.SelectionCacheType;
import org.optaplanner.core.config.heuristic.selector.entity.EntitySelectorConfig;
import org.optaplanner.core.config.heuristic.selector.entity.EntitySorterManner;
import org.optaplanner.core.config.heuristic.selector.entity.pillar.PillarSelectorConfig;
import org.optaplanner.core.config.heuristic.selector.move.MoveSelectorConfig;
import org.optaplanner.core.config.heuristic.selector.move.composite.UnionMoveSelectorConfig;
import org.optaplanner.core.config.heuristic.selector.move.generic.ChangeMoveSelectorConfig;
import org.optaplanner.core.config.heuristic.selector.move.generic.PillarSwapMoveSelectorConfig;
import org.optaplanner.core.config.heuristic.selector.move.generic.SubPillarType;
import org.optaplanner.core.config.heuristic.selector.move.generic.SwapMoveSelectorConfig;
import org.optaplanner.core.config.heuristic.selector.value.ValueSelectorConfig;
import org.optaplanner.core.config.heuristic.selector.value.ValueSorterManner;
import org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig;
import org.optaplanner.core.config.localsearch.decider.acceptor.LocalSearchAcceptorConfig;
import org.optaplanner.core.config.localsearch.decider.acceptor.stepcountinghillclimbing.StepCountingHillClimbingType;
import org.optaplanner.core.config.localsearch.decider.forager.FinalistPodiumType;
import org.optaplanner.core.config.localsearch.decider.forager.LocalSearchForagerConfig;
import org.optaplanner.core.config.localsearch.decider.forager.LocalSearchPickEarlyType;
import org.optaplanner.core.config.phase.PhaseConfig;
import org.optaplanner.core.config.phase.custom.CustomPhaseConfig;
import org.optaplanner.core.config.score.director.ScoreDirectorFactoryConfig;
import org.optaplanner.core.config.solver.EnvironmentMode;
import org.optaplanner.core.config.solver.SolverConfig;
import org.optaplanner.core.config.solver.termination.TerminationCompositionStyle;
import org.optaplanner.core.config.solver.termination.TerminationConfig;
import org.optaplanner.core.impl.phase.custom.CustomPhaseCommand;

import java.time.Duration;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
public class OptaSolverConfigProvider {

    public SolverConfig getDefaultSolverConfig(Problem problem
            , SolverVersionRouter router) {
        String version = problem.getVersion();
        TasConfiguration tasConfiguration = ApplicationContextHolder.getBean(TasConfiguration.class);
        SolverConfig solverConfig = new SolverConfig();
        solverConfig.setMoveThreadCount(String.valueOf(Runtime.getRuntime().availableProcessors()));
        solverConfig.setSolutionClass(router.routeSolution());
        // 生成动态字节码 提升性能
        solverConfig.setDomainAccessType(DomainAccessType.GIZMO);
        solverConfig.setEntityClassList(List.of(Designer.class, Task.class));
//        solverConfig.setEnvironmentMode(EnvironmentMode.FULL_ASSERT);
        solverConfig.setEnvironmentMode(EnvironmentMode.NON_REPRODUCIBLE);
        ScoreDirectorFactoryConfig scoreDirectorFactoryConfig = new ScoreDirectorFactoryConfig();
        scoreDirectorFactoryConfig.setConstraintProviderClass(router.routeConstraintProvider(version));

        solverConfig.setScoreDirectorFactoryConfig(scoreDirectorFactoryConfig);

        // 启发构建前阶段
        CustomPhaseConfig preConstructionHeuristicPhaseConfig = getPreConstructionHeuristicPhaseConfig(router.routePhaseCommands(version));
        // 高优先级启发构建阶段
        ConstructionHeuristicPhaseConfig hignPriorityTaskConstructionHeuristicPhaseConfig = getHignPriorityTaskConstructionHeuristicPhaseConfig();
        // 低优先级启发构建阶段
        ConstructionHeuristicPhaseConfig lowPriorityTaskConstructionHeuristicPhaseConfig = getLowPriorityTaskConstructionHeuristicPhaseConfig();
        // 启发构建阶段
//        ConstructionHeuristicPhaseConfig constructionHeuristicPhaseConfig = getConstructionHeuristicPhaseConfig(problem, 0.1f);
        // 虚拟设计师补偿阶段
        CustomPhaseConfig fakerDesignerCorrectionPhase = getFakerDesignerCorrectionPhase();
        // localSearch 阶段
        LocalSearchPhaseConfig highTemperatureSimulatedAnnealingPhaseConfig = getSimulatedAnnealingPhaseConfig(router.getHardLevelSize(version)
                , router.getSoftLevelSize(version)
                , problem
                , 1000
                , 1f);

        LinkedHashMap<PhaseName, PhaseConfig> phaseConfigLinkedHashMap = new LinkedHashMap<>() {{
            // 分配之前的预分配处理
            put(PhaseName.preConstructionHeuristicPhase, preConstructionHeuristicPhaseConfig);
            // 高优先级工单启发构建
            put(PhaseName.highPriorityTaskConstructionHeuristicPhase, hignPriorityTaskConstructionHeuristicPhaseConfig);
            // 低优先级工单的启发构建
            put(PhaseName.lowPriorityTaskConstructionHeuristicPhase, lowPriorityTaskConstructionHeuristicPhaseConfig);
            // 启发构建使用虚拟设计师补偿阶段
            put(PhaseName.fakerDesignerCorrectionPhaseWithConstruction, fakerDesignerCorrectionPhase);

        }};
        // 如果本次开启了多阶段求解器，那么添加一个偏好任务的模拟退火阶段为了取他的比例
        if (router.isMultiPhase(version)) {
            // 使用更高的初始温度和更长的时间，让preferTaskSimulatedAnnealingPhase有足够时间找到可行解
            LocalSearchPhaseConfig preferTaskSimulatedAnnealingPhaseConfig = getSimulatedAnnealingPhaseConfig(router.getHardLevelSize(version)
                    , router.getSoftLevelSize(version)
                    , problem
                    , 1000
                    , 0.5f);
            phaseConfigLinkedHashMap.put(PhaseName.preferTaskSimulatedAnnealingPhase, preferTaskSimulatedAnnealingPhaseConfig);
        }

        // 本地搜索 模拟退火阶段
        phaseConfigLinkedHashMap.put(PhaseName.simulatedAnnealingPhase, highTemperatureSimulatedAnnealingPhaseConfig);
        if (problem.getTasks().size() > 100 && tasConfiguration.getEnableHillClimbing()) {
//            // 模拟爬山
            LocalSearchPhaseConfig hillClimbingPhaseConfig = getHillClimbingPhaseConfig(problem, 0.3f, true);
            phaseConfigLinkedHashMap.put(PhaseName.hillClimbingPhase, hillClimbingPhaseConfig);
            // 最后模拟退火的补偿阶段
            phaseConfigLinkedHashMap.put(PhaseName.fakerDesignerCorrectionPhaseWithLocalSearch, fakerDesignerCorrectionPhase);
        }
        // 最后模拟退火的补偿阶段
        phaseConfigLinkedHashMap.put(PhaseName.fakerDesignerCorrectionPhaseWithLocalSearch, fakerDesignerCorrectionPhase);
        PhaseQueueManager.registerPhaseConfig(phaseConfigLinkedHashMap, solverConfig);
        solverConfig.withConstraintStreamImplType(ConstraintStreamImplType.BAVET);
        return solverConfig;
    }

    /**
     * 爬山算法配置
     *
     * @param problem                      问题实例
     * @param ratio                        时间比例
     * @param acceptedCountLimit           每步接受的候选解数量限制
     * @param enableSwapMove               是否启用交换移动
     * @param stepCountingHillClimbingSize 爬山算法步数大小
     * @return 配置好的爬山算法阶段
     */
    public LocalSearchPhaseConfig getHillClimbingPhaseConfig(Problem problem, float ratio,
                                                             int acceptedCountLimit,
                                                             boolean enableSwapMove,
                                                             int stepCountingHillClimbingSize) {
        LocalSearchPhaseConfig localSearchPhaseConfig = new LocalSearchPhaseConfig();

        // 1. 配置接受器 - 严格的爬山算法配置
        LocalSearchAcceptorConfig acceptorConfig = new LocalSearchAcceptorConfig();

        // 使用纯粹的爬山算法 - 只接受改进的解
        acceptorConfig.setStepCountingHillClimbingType(StepCountingHillClimbingType.IMPROVING_STEP);
        acceptorConfig.setStepCountingHillClimbingSize(stepCountingHillClimbingSize);


        localSearchPhaseConfig.setAcceptorConfig(acceptorConfig);

        // 2. 配置搜索策略
        LocalSearchForagerConfig foragerConfig = new LocalSearchForagerConfig();
        foragerConfig.setAcceptedCountLimit(acceptedCountLimit);
        foragerConfig.setPickEarlyType(LocalSearchPickEarlyType.FIRST_BEST_SCORE_IMPROVING);
        foragerConfig.setFinalistPodiumType(FinalistPodiumType.HIGHEST_SCORE);
        localSearchPhaseConfig.setForagerConfig(foragerConfig);

        // 3. 配置移动选择器
        List<MoveSelectorConfig> moveSelectorList = new ArrayList<>();

        // 3.1 必须包含的ChangeMove
        moveSelectorList.add(new ChangeMoveSelectorConfig()
                .withEntitySelectorConfig(new EntitySelectorConfig()
                        .withEntityClass(Task.class)
                        .withFilterClass(NotPreferDesignerTaskFilter.class)
                        .withCacheType(SelectionCacheType.PHASE))
                .withValueSelectorConfig(new ValueSelectorConfig()
                        .withVariableName("designer")
                        .withFilterClass(OnDutyDesignerFilter.class)
                        .withCacheType(SelectionCacheType.PHASE)));

        // 3.2 可选的SwapMove
        if (enableSwapMove) {
            moveSelectorList.add(new SwapMoveSelectorConfig()
                    .withEntitySelectorConfig(new EntitySelectorConfig()
                            .withEntityClass(Task.class)
                            .withFilterClass(NotPreferDesignerTaskFilter.class)
                            .withCacheType(SelectionCacheType.PHASE))
                    .withVariableNameIncludes("designer"));
        }

        UnionMoveSelectorConfig unionMoveSelectorConfig = new UnionMoveSelectorConfig()
                .withMoveSelectorList(moveSelectorList);
        localSearchPhaseConfig.setMoveSelectorConfig(unionMoveSelectorConfig);

        // 4. 设置终止条件
        TerminationConfig terminationConfig = chooseTerminationCondition(
                problem.getTasks().size(),
                problem.getDesigners().size(),
                ratio);
        localSearchPhaseConfig.setTerminationConfig(terminationConfig);

        return localSearchPhaseConfig;
    }

    /**
     * 爬山算法配置 - 简化版本，使用默认参数
     *
     * @param problem        问题实例
     * @param ratio          时间比例
     * @param enableSwapMove 是否启用交换移动
     * @return 配置好的爬山算法阶段
     */
    public LocalSearchPhaseConfig getHillClimbingPhaseConfig(Problem problem, float ratio,
                                                             boolean enableSwapMove) {
        // 使用默认的步数大小：根据问题规模动态计算
        int defaultStepSize = Math.max(500, Math.min(2000, problem.getTasks().size() * 2));
        return getHillClimbingPhaseConfig(problem, ratio, problem.getTasks().size() / 3, enableSwapMove, defaultStepSize);
    }

    public LocalSearchPhaseConfig getSimulatedAnnealingPhaseConfig_v2(int hardLevelSize
            , int softLevelSize
            , Problem problem
            , int initialTemperature
            , float ratio) {
        // localSearch
        LocalSearchPhaseConfig localSearchPhaseConfig = new LocalSearchPhaseConfig();

        LocalSearchAcceptorConfig localSearchAcceptorConfig = new LocalSearchAcceptorConfig();
        if (problem.getTasks().size() > 10) {
            localSearchAcceptorConfig.setEntityTabuSize(
                    Math.max(3, Math.min(12, DecimalUtils.preciseRound(problem.getTasks().size() * 0.02)))
            );
            localSearchAcceptorConfig.setValueTabuSize(
                    Math.max(Math.min(6, problem.getDesigners().size() / 2), Math.min(6, DecimalUtils.preciseRound(problem.getDesigners().size() * 0.15)))
            );
        }
        localSearchAcceptorConfig.setSimulatedAnnealingStartingTemperature(formatHardSoftTemperature(hardLevelSize, softLevelSize, initialTemperature, 0));

        localSearchPhaseConfig.setAcceptorConfig(localSearchAcceptorConfig);

//        localSearchAcceptorConfig.setEntityTabuSize(DecimalUtils.preciseRound(problem.getTasks().size() * 0.08));
        LocalSearchForagerConfig localSearchForagerConfig = new LocalSearchForagerConfig();
        localSearchForagerConfig.setAcceptedCountLimit(4);
//        localSearchForagerConfig.setAcceptedCountLimit(DecimalUtils.preciseRound(Math.max(3, Math.sqrt((double) (problem.getTasks().size() * problem.getDesigners().size()) / 3))));
        // 定义何时提前终止当前步骤的搜索（即使未遍历所有候选解）‌ FIRST_BEST_SCORE：快速收敛但解质量低 NEVER：解质量高但耗时增加‌
        localSearchForagerConfig.setPickEarlyType(LocalSearchPickEarlyType.FIRST_BEST_SCORE_IMPROVING);
        // 配置局部搜索（Local Search）阶段的候选解选择策略 默认策略
        localSearchForagerConfig.setFinalistPodiumType(FinalistPodiumType.HIGHEST_SCORE);
        localSearchPhaseConfig.setForagerConfig(localSearchForagerConfig);

        PillarSelectorConfig pillarSelectorConfig = new PillarSelectorConfig();

//        pillarSelectorConfig.setMaximumSubPillarSize(Math.max(2
//                , DecimalUtils.preciseRound(problem.getTasks().size() * 0.05)));
//        pillarSelectorConfig.setEntitySelectorConfig(new EntitySelectorConfig()
//                .withEntityClass(Task.class)
//                .withFilterClass(NotPreferDesignerTaskFilter.class)
//                .withCacheType(SelectionCacheType.PHASE));
//         组合自定义移动和默认移动
        UnionMoveSelectorConfig unionMoveSelectorConfig = new UnionMoveSelectorConfig()
                .withMoveSelectorList(List.of(
                        // 1. 单个值修改（ChangeMove）：修改单个任务的设计师
                        new ChangeMoveSelectorConfig()
                                .withEntitySelectorConfig(new EntitySelectorConfig()
                                        .withEntityClass(Task.class)
                                        .withFilterClass(NotPreferDesignerTaskFilter.class)
                                        .withCacheType(SelectionCacheType.PHASE))
                                .withValueSelectorConfig(new ValueSelectorConfig()
                                        .withVariableName("designer")
                                        .withFilterClass(OnDutyDesignerFilter.class)
                                        .withCacheType(SelectionCacheType.PHASE)),

                        // 2. 两两交换（SwapMove）：交换两个任务的设计师
                        new SwapMoveSelectorConfig()
                                .withEntitySelectorConfig(new EntitySelectorConfig()
                                        .withEntityClass(Task.class)
                                        .withFilterClass(NotPreferDesignerTaskFilter.class)
                                        .withCacheType(SelectionCacheType.PHASE))
                                .withVariableNameIncludes("designer")

//                        // 3. 批量交换（PillarSwapMove）：交换一组任务的设计师
                        , new PillarSwapMoveSelectorConfig()
                                .withSubPillarType(SubPillarType.NONE)
//                                .withFilterClass(NotPreferDesignerTaskFilter.class)
//                                .withCacheType(SelectionCacheType.PHASE)
                                .withSecondaryPillarSelectorConfig(pillarSelectorConfig)
                                .withVariableNameIncludes("designer")
//                                .withSubPillarSequenceComparatorClass(Task.class)
                ));

//        localSearchPhaseConfig.setMoveSelectorConfig(unionMoveSelectorConfig);
        // 设置终止条件
        TerminationConfig terminationConfig = chooseTerminationCondition(problem.getTasks().size(), problem.getDesigners().size(), ratio);
        localSearchPhaseConfig.setTerminationConfig(terminationConfig);
        return localSearchPhaseConfig;
    }

    public CustomPhaseConfig getPreConstructionHeuristicPhaseConfig(List<Class<? extends AbstractCustomPhaseCommand>> customPhaseList) {
        CustomPhaseConfig prePhaseConfig = new CustomPhaseConfig();
        prePhaseConfig.withCustomPhaseCommandClassList(
                customPhaseList.stream()
                        .map(clazz -> (Class<? extends CustomPhaseCommand<BaseSolution>>) clazz)
                        .collect(Collectors.toList())
        );
        return prePhaseConfig;
    }

//    public CustomPhaseConfig getAddFakerDesignerPhaseConfig() {
//        CustomPhaseConfig addFakerDesignerPhase = new CustomPhaseConfig();
//        addFakerDesignerPhase.withCustomPhaseCommandClassList(
//                List.of(AddFakerDesignerPhase.class)
//        );
//        return addFakerDesignerPhase;
//    }

    public CustomPhaseConfig getFakerDesignerCorrectionPhase() {
        // 虚拟设计师补偿阶段
        CustomPhaseConfig fakerDesignerCorrectionPhase = new CustomPhaseConfig();
        fakerDesignerCorrectionPhase.withCustomPhaseCommandClassList(
                List.of(FakerDesignerCorrectionPhase.class)
        );
        return fakerDesignerCorrectionPhase;
    }

    public LocalSearchPhaseConfig getTabuSearchPhaseConfig(Problem problem, float ratio) {
        LocalSearchPhaseConfig phaseConfig = new LocalSearchPhaseConfig();

        // 禁忌表配置（核心参数）
        int valueSize = problem.getDesigners().size();
        int entitySize = problem.getTasks().size();
        LocalSearchAcceptorConfig acceptorConfig = new LocalSearchAcceptorConfig();
        acceptorConfig.setEntityTabuSize(Math.max(1, DecimalUtils.preciseRound(entitySize * 0.35)));
        acceptorConfig.setValueTabuSize(Math.max(1, DecimalUtils.preciseRound(valueSize * 0.3)));
//        acceptorConfig.setAvoidRecentChanges(true);
//        acceptorConfig.setEntityTabuSize(7);
//        acceptorConfig.setValueTabuSize(5);
        phaseConfig.setAcceptorConfig(acceptorConfig);

        // 移动选择策略（增强交换移动）
        UnionMoveSelectorConfig moveSelectorConfig = new UnionMoveSelectorConfig()
                .withMoveSelectorList(List.of(
                        new SwapMoveSelectorConfig()
                                .withEntitySelectorConfig(new EntitySelectorConfig()
                                        .withEntityClass(Task.class)
                                        .withCacheType(SelectionCacheType.PHASE))
                                .withVariableNameIncludes("designer"),
                        new PillarSwapMoveSelectorConfig()
//                                .withEntitySelectorConfig(new EntitySelectorConfig()
//                                        .withEntityClass(Task.class)
//                                        .withCacheType(SelectionCacheType.JUST_IN_TIME))
                                .withVariableNameIncludes("designer"),
                        new ChangeMoveSelectorConfig()
                                .withEntitySelectorConfig(new EntitySelectorConfig()
                                        .withEntityClass(Task.class))
                                .withValueSelectorConfig(new ValueSelectorConfig()
                                        .withVariableName("designer"))
                ));
        phaseConfig.setMoveSelectorConfig(moveSelectorConfig);

        // 候选解管理策略
        LocalSearchForagerConfig foragerConfig = new LocalSearchForagerConfig();
        foragerConfig.setAcceptedCountLimit(DecimalUtils.preciseRound(Math.max(5, Math.sqrt((double) (problem.getTasks().size() * problem.getDesigners().size()) / 2))));
        foragerConfig.setPickEarlyType(LocalSearchPickEarlyType.NEVER); // 禁止提前终止
//        foragerConfig.setFinalistPodiumType(FinalistPodiumType.STEP_SCORE_AND_HILL_CLIMBING);
        phaseConfig.setForagerConfig(foragerConfig);
        TerminationConfig terminationConfig = chooseTerminationCondition(problem.getTasks().size(), problem.getDesigners().size(), ratio);
        phaseConfig.setTerminationConfig(terminationConfig);
        return phaseConfig;
    }

    public LocalSearchPhaseConfig getSimulatedAnnealingPhaseConfig(int hardLevelSize
            , int softLevelSize
            , Problem problem
            , float ratio) {
        return getSimulatedAnnealingPhaseConfig(hardLevelSize, softLevelSize, problem, 8000, ratio);
    }

    public LocalSearchPhaseConfig getSimulatedAnnealingPhaseConfig(int hardLevelSize
            , int softLevelSize
            , Problem problem
            , int initialTemperature
            , float ratio) {
        // localSearch
        LocalSearchPhaseConfig localSearchPhaseConfig = new LocalSearchPhaseConfig();

        LocalSearchAcceptorConfig localSearchAcceptorConfig = new LocalSearchAcceptorConfig();
        if (problem.getTasks().size() > 10) {
            localSearchAcceptorConfig.setEntityTabuSize(Math.max(1, DecimalUtils.preciseRound(problem.getTasks().size() * 0.006)));
            localSearchAcceptorConfig.setValueTabuSize(Math.max(1, DecimalUtils.preciseRound(problem.getDesigners().size() * 0.08)));
        }
        localSearchAcceptorConfig.setSimulatedAnnealingStartingTemperature(formatHardSoftTemperature(hardLevelSize, softLevelSize, initialTemperature, 2));

        localSearchPhaseConfig.setAcceptorConfig(localSearchAcceptorConfig);

//        localSearchAcceptorConfig.setEntityTabuSize(DecimalUtils.preciseRound(problem.getTasks().size() * 0.08));
        LocalSearchForagerConfig localSearchForagerConfig = new LocalSearchForagerConfig();
        localSearchForagerConfig.setAcceptedCountLimit(4);
//        localSearchForagerConfig.setAcceptedCountLimit(DecimalUtils.preciseRound(Math.max(3, Math.sqrt((double) (problem.getTasks().size() * problem.getDesigners().size()) / 3))));
        // 定义何时提前终止当前步骤的搜索（即使未遍历所有候选解）‌ FIRST_BEST_SCORE：快速收敛但解质量低 NEVER：解质量高但耗时增加‌
        localSearchForagerConfig.setPickEarlyType(LocalSearchPickEarlyType.FIRST_BEST_SCORE_IMPROVING);
        // 配置局部搜索（Local Search）阶段的候选解选择策略 默认策略
        localSearchForagerConfig.setFinalistPodiumType(FinalistPodiumType.HIGHEST_SCORE);
        localSearchPhaseConfig.setForagerConfig(localSearchForagerConfig);

        PillarSelectorConfig pillarSelectorConfig = new PillarSelectorConfig();

//         组合自定义移动和默认移动
        UnionMoveSelectorConfig unionMoveSelectorConfig = new UnionMoveSelectorConfig()
                .withMoveSelectorList(List.of(
                        // 1. 单个值修改（ChangeMove）：修改单个任务的设计师
                        new ChangeMoveSelectorConfig()
                                .withEntitySelectorConfig(new EntitySelectorConfig()
                                        .withEntityClass(Task.class)
                                        .withFilterClass(NotPreferDesignerTaskFilter.class)
                                        .withCacheType(SelectionCacheType.PHASE))
                                .withValueSelectorConfig(new ValueSelectorConfig()
                                        .withVariableName("designer")
                                        .withFilterClass(OnDutyDesignerFilter.class)
                                        .withCacheType(SelectionCacheType.PHASE)),

                        // 2. 两两交换（SwapMove）：交换两个任务的设计师
                        new SwapMoveSelectorConfig()
                                .withEntitySelectorConfig(new EntitySelectorConfig()
                                        .withEntityClass(Task.class)
                                        .withFilterClass(NotPreferDesignerTaskFilter.class)
                                        .withCacheType(SelectionCacheType.PHASE))
                                .withVariableNameIncludes("designer")

//                        // 3. 批量交换（PillarSwapMove）：交换一组任务的设计师
                        , new PillarSwapMoveSelectorConfig()
                                .withSubPillarType(SubPillarType.NONE)
//                                .withFilterClass(NotPreferDesignerTaskFilter.class)
//                                .withCacheType(SelectionCacheType.PHASE)
                                .withSecondaryPillarSelectorConfig(pillarSelectorConfig)
                                .withVariableNameIncludes("designer")
//                                .withSubPillarSequenceComparatorClass(Task.class)
                ));

//        localSearchPhaseConfig.setMoveSelectorConfig(unionMoveSelectorConfig);
        // 设置终止条件
        TerminationConfig terminationConfig = chooseTerminationCondition(problem.getTasks().size(), problem.getDesigners().size(), ratio);
        localSearchPhaseConfig.setTerminationConfig(terminationConfig);
        return localSearchPhaseConfig;
    }

    public ConstructionHeuristicPhaseConfig getConstructionHeuristicPhaseConfig(Problem problem, float ratio) {
        ConstructionHeuristicPhaseConfig constructionHeuristicPhaseConfig = new ConstructionHeuristicPhaseConfig();
        constructionHeuristicPhaseConfig.setConstructionHeuristicType(ConstructionHeuristicType.CHEAPEST_INSERTION);

        ConstructionHeuristicForagerConfig foragerConfig = new ConstructionHeuristicForagerConfig();
        foragerConfig.setPickEarlyType(ConstructionHeuristicPickEarlyType.FIRST_FEASIBLE_SCORE_OR_NON_DETERIORATING_HARD);
        constructionHeuristicPhaseConfig.setForagerConfig(foragerConfig);

        // 实体排序策略：按难度降序排列
        constructionHeuristicPhaseConfig.withEntitySorterManner(EntitySorterManner.DECREASING_DIFFICULTY);
        constructionHeuristicPhaseConfig.setValueSorterManner(ValueSorterManner.DECREASING_STRENGTH);
        // 创建联合移动选择器（同时包含 ChangeMove 和 SwapMove）
        UnionMoveSelectorConfig unionMoveSelectorConfig = new UnionMoveSelectorConfig()
                .withMoveSelectorList(List.of(
                        new ChangeMoveSelectorConfig()
                                .withCacheType(SelectionCacheType.PHASE)
                                .withEntitySelectorConfig(new EntitySelectorConfig()
                                        .withEntityClass(Task.class)
                                        .withCacheType(SelectionCacheType.PHASE)
                                )
                                .withValueSelectorConfig(new ValueSelectorConfig()
                                        .withVariableName("designer")
                                        .withCacheType(SelectionCacheType.PHASE)
                                ),
                        new SwapMoveSelectorConfig()
                                .withEntitySelectorConfig(new EntitySelectorConfig()
                                        .withEntityClass(Task.class)
                                        .withCacheType(SelectionCacheType.PHASE)
                                )
                                .withVariableNameIncludes("designer")
                ));

        // 将联合移动选择器添加到阶段配置
//        constructionHeuristicPhaseConfig.setMoveSelectorConfigList(List.of(unionMoveSelectorConfig));
//         配置 PooledEntityPlacer
//        PooledEntityPlacerConfig pooledEntityPlacerConfig = new PooledEntityPlacerConfig()
//                .withMoveSelectorConfig(unionMoveSelectorConfig);
//
//        constructionHeuristicPhaseConfig.setEntityPlacerConfig(pooledEntityPlacerConfig);
//        TerminationConfig terminationConfig = chooseTerminationCondition(problem.getTasks().size(), problem.getDesigners().size(), ratio);
//        constructionHeuristicPhaseConfig.setTerminationConfig(terminationConfig);
        return constructionHeuristicPhaseConfig;
    }

    public ConstructionHeuristicPhaseConfig getLowPriorityTaskConstructionHeuristicPhaseConfig() {
        ConstructionHeuristicPhaseConfig lowPriorityTaskConstructionHeuristicPhaseConfig = new ConstructionHeuristicPhaseConfig();
//        lowPriorityTaskConstructionHeuristicPhaseConfig.setConstructionHeuristicType(ConstructionHeuristicType.FIRST_FIT_DECREASING);
//        lowPriorityTaskConstructionHeuristicPhaseConfig.setTerminationConfig(terminationConfig);

        ConstructionHeuristicForagerConfig constructionHeuristicForagerConfig1 = new ConstructionHeuristicForagerConfig();
        // 第一个可行分数或非恶化困难
        constructionHeuristicForagerConfig1.setPickEarlyType(ConstructionHeuristicPickEarlyType.FIRST_FEASIBLE_SCORE_OR_NON_DETERIORATING_HARD);
        lowPriorityTaskConstructionHeuristicPhaseConfig.setForagerConfig(constructionHeuristicForagerConfig1);
        lowPriorityTaskConstructionHeuristicPhaseConfig.withEntitySorterManner(EntitySorterManner.DECREASING_DIFFICULTY);
        ChangeMoveSelectorConfig lowChangeMoveSelectorConfig = new ChangeMoveSelectorConfig()
                .withEntitySelectorConfig(new EntitySelectorConfig()
                        .withCacheType(SelectionCacheType.PHASE)
                        .withFilterClass(LowPriorityTaskFilter.class)
                        .withEntityClass(Task.class))
                .withValueSelectorConfig(new ValueSelectorConfig()
                        .withCacheType(SelectionCacheType.PHASE)
                        .withFilterClass(AvailableDesignerFilter.class)
                        .withVariableName("designer"));

//        lowPriorityTaskConstructionHeuristicPhaseConfig.setMoveSelectorConfigList(List.of(changeMoveSelectorConfig));
        PooledEntityPlacerConfig pooledEntityPlacerConfig1 = new PooledEntityPlacerConfig();
        pooledEntityPlacerConfig1.setMoveSelectorConfig(lowChangeMoveSelectorConfig);
        lowPriorityTaskConstructionHeuristicPhaseConfig.setEntityPlacerConfig(pooledEntityPlacerConfig1);
        return lowPriorityTaskConstructionHeuristicPhaseConfig;
    }

    public ConstructionHeuristicPhaseConfig getHignPriorityTaskConstructionHeuristicPhaseConfig() {
        ConstructionHeuristicPhaseConfig hignPriorityTaskConstructionHeuristicPhaseConfig = new ConstructionHeuristicPhaseConfig();
        /**
         * FIRST_FIT：按顺序处理每个工单，并尝试将其分配给第一个可用的设计师。
         * FIRST_FIT_DECREASING：按工单的优先级或大小从高到低排序后进行分配。
         * WEAKEST_FIT：将每个工单分配给当前负载最轻的设计师。
         * WEAKEST_FIT_DECREASING：按工单的优先级或大小从高到低排序后进行分配。
         * STRONGEST_FIT：将每个工单分配给当前负载最重的设计师。
         * STRONGEST_FIT_DECREASING：结合了首次拟合、递减和最强拟合。 因此，它根据难度降低对规划实体进行排序，对强度降低的规划值进行排序。
         * ALLOCATE_ENTITY_FROM_QUEUE：将所有工单放入一个队列中，依次处理并分配。
         * ALLOCATE_TO_VALUE_FROM_QUEUE：将所有设计师放入一个队列中，依次为其分配最佳工单。
         * CHEAPEST_INSERTION：尝试将每个工单插入到当前解中的某个位置，使总成本最低。
         * ALLOCATE_FROM_POOL：将所有工单和设计师的组合放入一个池中，依次选择最佳组合进行分配
         */
        ConstructionHeuristicForagerConfig constructionHeuristicForagerConfig = new ConstructionHeuristicForagerConfig();
        // 第一个可行分数或非恶化困难
        constructionHeuristicForagerConfig.setPickEarlyType(ConstructionHeuristicPickEarlyType.FIRST_FEASIBLE_SCORE_OR_NON_DETERIORATING_HARD);
//        hignPriorityTaskConstructionHeuristicPhaseConfig.setConstructionHeuristicType(ConstructionHeuristicType.FIRST_FIT_DECREASING);
//        hignPriorityTaskConstructionHeuristicPhaseConfig.setTerminationConfig(terminationConfig);
        hignPriorityTaskConstructionHeuristicPhaseConfig.setForagerConfig(constructionHeuristicForagerConfig);
        hignPriorityTaskConstructionHeuristicPhaseConfig.withEntitySorterManner(EntitySorterManner.DECREASING_DIFFICULTY);

        ChangeMoveSelectorConfig changeMoveSelectorConfig = new ChangeMoveSelectorConfig()
                .withEntitySelectorConfig(new EntitySelectorConfig()
                        .withCacheType(SelectionCacheType.PHASE)
                        .withFilterClass(HighPriorityTaskFilter.class)
                        .withEntityClass(Task.class))
                .withValueSelectorConfig(new ValueSelectorConfig()
                        .withCacheType(SelectionCacheType.PHASE)
                        .withFilterClass(AvailableDesignerFilter.class)
                        .withVariableName("designer"));

//        hignPriorityTaskConstructionHeuristicPhaseConfig.setMoveSelectorConfigList(List.of(changeMoveSelectorConfig));
        PooledEntityPlacerConfig pooledEntityPlacerConfig = new PooledEntityPlacerConfig();
        pooledEntityPlacerConfig.setMoveSelectorConfig(changeMoveSelectorConfig);
        hignPriorityTaskConstructionHeuristicPhaseConfig.setEntityPlacerConfig(pooledEntityPlacerConfig);
        return hignPriorityTaskConstructionHeuristicPhaseConfig;
    }

    /**
     * 计算工单和设计师分配的可能数量的位数
     *
     * @param taskCount     工单数量
     * @param designerCount 设计师数量
     * @return 可能分配方案数量的位数
     */
    public static int calculateAssignmentPossibilitiesDigits(int taskCount, int designerCount) {
        if (taskCount < 0 || designerCount < 0) {
            throw new IllegalArgumentException("工单数量和设计师数量不能为负数");
        }
        if (designerCount == 0) {
            return 1; // 如果没有设计师，只有一种分法（不分配）
        }
        if (taskCount == 0) {
            return 1; // 如果没有工单，只有一种分法（全部分配 0 个工单）
        }
        // 计算位数的公式：floor(taskCount * log10(designerCount)) + 1
        double logValue = taskCount * Math.log10(designerCount);
        return (int) Math.floor(logValue) + 1;
    }

    /**
     * 根据可能的分配方案数量选择合适的终止条件
     *
     * @param taskCount     工单数
     * @param designerCount 设计师数
     * @return 终止条件配置 factor
     */
    public TerminationConfig chooseTerminationCondition(int taskCount, int designerCount, double ratio) {
        int digits = calculateAssignmentPossibilitiesDigits(taskCount, designerCount);
        TerminationConfig terminationConfig = new TerminationConfig();
        terminationConfig.setTerminationCompositionStyle(TerminationCompositionStyle.OR);

        TasConfiguration tasConfiguration = ApplicationContextHolder.getBean(TasConfiguration.class);
        long min = (long) Math.min(Duration.ofMinutes(tasConfiguration.getTerminationMinuteLimit()).toMillis()
                , Duration.ofSeconds(tasConfiguration.getTerminationSecondBase()).toMillis()
                  + (long) digits * tasConfiguration.getTerminationDigitalRation() * Duration.ofSeconds(1).toMillis());
        if (taskCount >= 300) {
            ratio = (1 + (double) taskCount / 1000) * ratio;
        }
        terminationConfig.setMillisecondsSpentLimit((long) DecimalUtils.preciseRound((double) min * ratio));
        log.info("task and designer possible digits {} chose millisecond termination,second {}", digits, Duration.ofMillis(DecimalUtils.preciseRound((double) min * ratio)).toSeconds());
        terminationConfig.setUnimprovedSpentLimit(Duration.ofSeconds(tasConfiguration.getUnimprovedSpentSecondLimit()));
        return terminationConfig;
    }

    private String formatHardSoftTemperature(int hard, int soft, int softInitialTemperature, int hardInitialTemperature) {
        // 返回构建好的字符串
        return "[" +
               buildPart(hard, hardInitialTemperature) +
               "]hard/" +

               // 构建soft部分
               "[" +
               buildPart(soft, softInitialTemperature) +
               "]soft";
    }

    /**
     * 通用方法：构建部分字符串
     *
     * @param count 数量（hard或soft的值）
     * @param value 填充的值（如2或999）
     * @return 构建好的部分字符串
     */
    private String buildPart(int count, int value) {
        StringBuilder part = new StringBuilder();
        for (int i = 0; i < count; i++) {
            part.append(value);
            // 最后一个数字后不添加分隔符
            if (i < count - 1) {
                part.append("/");
            }
        }
        return part.toString();
    }
}

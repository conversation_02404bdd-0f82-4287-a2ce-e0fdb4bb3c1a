package com.angelalign.tas.solver;

import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.china.*;
import com.angelalign.tas.score.china.south.SouthChinaDesignScheduleBaseConstraintProvider;
import com.angelalign.tas.score.oversea.DefaultManualClaimHardBaseConstraintProvider;
import com.angelalign.tas.score.oversea.runtime.OverSeaQualityInspectionRuntimeTaskBaseConstraintProvider;
import com.angelalign.tas.score.oversea.runtime.OverSeaDdmRuntimeTaskBaseConstraintProvider;
import com.angelalign.tas.score.oversea.runtime.OverSeaDesignRuntimeBaseConstraintProvider;
import com.angelalign.tas.score.oversea.schedule.OverSeaDdmScheduleTaskHardBaseConstraintProvider;
import com.angelalign.tas.score.oversea.schedule.OverSeaDesignScheduleConstraintProvider_V2;
import com.angelalign.tas.score.oversea.schedule.OverSeaDesignScheduleTaskHardBaseConstraintProvider;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.domain.solution.china.ChinaDesignScheduleSolution;
import com.angelalign.tas.domain.solution.china.ChinaRandomInspectionRuntimeSolution;
import com.angelalign.tas.domain.solution.oversea.runtime.OverSeaQualityInspectionRuntimeSolution;
import com.angelalign.tas.domain.solution.oversea.schedule.OverSeaDdmScheduleSolution;
import com.angelalign.tas.domain.solution.oversea.schedule.OverSeaDesignScheduleSolution;
import com.angelalign.tas.domain.solution.oversea.DefaultManualClaimSolution;
import com.angelalign.tas.domain.solution.oversea.runtime.OverSeaDdmRuntimeSolution;
import com.angelalign.tas.domain.solution.oversea.runtime.OverSeaDesignRuntimeSolution;
import com.angelalign.tas.solver.phase.NoChangeCustomPhaseCommand;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;
import com.angelalign.tas.solver.phase.FullDesignModifyTaskPhaseCommand;
import com.angelalign.tas.solver.phase.TaskPreferDesignerPhaseCommand;
import jodd.util.StringUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum SolverTypeEnum {
    // 海外设计组实时分配
    OVERSEA_DESIGN_RUNTIME(OverSeaDesignRuntimeSolution.class
            , OverSeaDesignRuntimeBaseConstraintProvider.class
            , List.of(
            FullDesignModifyTaskPhaseCommand.class
            , TaskPreferDesignerPhaseCommand.class)
            , OverSeaDesignRuntimeBaseConstraintProvider.HARD_LEVEL
            , OverSeaDesignRuntimeBaseConstraintProvider.SOFT_LEVEL),

    // 海外设计定时分配
    OVERSEA_DESIGN_SCHEDULE(OverSeaDesignScheduleSolution.class
            , OverSeaDesignScheduleTaskHardBaseConstraintProvider.class
            , List.of(TaskPreferDesignerPhaseCommand.class)
            , OverSeaDesignScheduleTaskHardBaseConstraintProvider.HARD_LEVEL
            , OverSeaDesignScheduleTaskHardBaseConstraintProvider.SOFT_LEVEL),

    // 海外DDM定时分配
    OVERSEA_DDM_SCHEDULE(OverSeaDdmScheduleSolution.class
            , OverSeaDdmScheduleTaskHardBaseConstraintProvider.class
            , List.of(TaskPreferDesignerPhaseCommand.class)
            , OverSeaDdmScheduleTaskHardBaseConstraintProvider.HARD_LEVEL
            , OverSeaDdmScheduleTaskHardBaseConstraintProvider.SOFT_LEVEL),
    // 海外设计定时分配
    OVERSEA_DESIGN_SCHEDULE_V2(OverSeaDesignScheduleSolution.class
            , OverSeaDesignScheduleConstraintProvider_V2.class
            , List.of(TaskPreferDesignerPhaseCommand.class)
            , OverSeaDesignScheduleConstraintProvider_V2.HARD_LEVEL
            , OverSeaDesignScheduleConstraintProvider_V2.SOFT_LEVEL),
    // 海外DDM实时分配
    OVERSEA_DDM_RUNTIME(OverSeaDdmRuntimeSolution.class
            , OverSeaDdmRuntimeTaskBaseConstraintProvider.class
            , List.of(TaskPreferDesignerPhaseCommand.class)
            , OverSeaDdmRuntimeTaskBaseConstraintProvider.HARD_LEVEL
            , OverSeaDdmRuntimeTaskBaseConstraintProvider.SOFT_LEVEL),

    // 通用质检组的实时分配
    COMMON_QUALITY_INSPECTION_RUNTIME(OverSeaQualityInspectionRuntimeSolution.class
            , OverSeaQualityInspectionRuntimeTaskBaseConstraintProvider.class
            , List.of(NoChangeCustomPhaseCommand.class)
            , OverSeaQualityInspectionRuntimeTaskBaseConstraintProvider.HARD_LEVEL
            , OverSeaQualityInspectionRuntimeTaskBaseConstraintProvider.SOFT_LEVEL),

    // 海外主动领取
    COMMON_MANUAL_CLAIM(DefaultManualClaimSolution.class
            , DefaultManualClaimHardBaseConstraintProvider.class
            , List.of(TaskPreferDesignerPhaseCommand.class)
            , DefaultManualClaimHardBaseConstraintProvider.HARD_LEVEL
            , DefaultManualClaimHardBaseConstraintProvider.SOFT_LEVEL),

    // 国内的定时分配
    CHINA_DESIGN_SCHEDULE(ChinaDesignScheduleSolution.class
            , ChinaDesignScheduleBaseConstraintProvider.class
            , List.of(FullDesignModifyTaskPhaseCommand.class, TaskPreferDesignerPhaseCommand.class)
            , ChinaDesignScheduleBaseConstraintProvider.HARD_LEVEL
            , ChinaDesignScheduleBaseConstraintProvider.SOFT_LEVEL),
    CHINA_DESIGN_SCHEDULE_V2(ChinaDesignScheduleSolution.class
            , ChinaDesignScheduleConstraintProvider_V2.class
            , List.of(FullDesignModifyTaskPhaseCommand.class, TaskPreferDesignerPhaseCommand.class)
            , ChinaDesignScheduleConstraintProvider_V2.HARD_LEVEL
            , ChinaDesignScheduleConstraintProvider_V2.SOFT_LEVEL),
    CHINA_DESIGN_SCHEDULE_V3(ChinaDesignScheduleSolution.class
            , ChinaDesignScheduleConstraintProvider_V3.class
            , List.of(FullDesignModifyTaskPhaseCommand.class, TaskPreferDesignerPhaseCommand.class)
            , ChinaDesignScheduleConstraintProvider_V3.HARD_LEVEL
            , ChinaDesignScheduleConstraintProvider_V3.SOFT_LEVEL),
    CHINA_DESIGN_SCHEDULE_V4(ChinaDesignScheduleSolution.class
            , ChinaDesignScheduleConstraintProvider_V4.class
            , List.of(FullDesignModifyTaskPhaseCommand.class, TaskPreferDesignerPhaseCommand.class)
            , ChinaDesignScheduleConstraintProvider_V4.HARD_LEVEL
            , ChinaDesignScheduleConstraintProvider_V4.SOFT_LEVEL),
    CHINA_DESIGN_SCHEDULE_V5(ChinaDesignScheduleSolution.class
            , ChinaDesignScheduleConstraintProvider_V5.class
            , List.of(FullDesignModifyTaskPhaseCommand.class, TaskPreferDesignerPhaseCommand.class)
            , ChinaDesignScheduleConstraintProvider_V5.HARD_LEVEL
            , ChinaDesignScheduleConstraintProvider_V5.SOFT_LEVEL,
            Boolean.TRUE),
    CHINA_DESIGN_SCHEDULE_V6(ChinaDesignScheduleSolution.class
            , ChinaDesignScheduleConstraintProvider_V6.class
            , List.of(FullDesignModifyTaskPhaseCommand.class, TaskPreferDesignerPhaseCommand.class)
            , ChinaDesignScheduleConstraintProvider_V6.HARD_LEVEL
            , ChinaDesignScheduleConstraintProvider_V6.SOFT_LEVEL),
    SOUTH_CHINA_DESIGN_SCHEDULE(ChinaDesignScheduleSolution.class
            , SouthChinaDesignScheduleBaseConstraintProvider.class
            , List.of(FullDesignModifyTaskPhaseCommand.class, TaskPreferDesignerPhaseCommand.class)
            , SouthChinaDesignScheduleBaseConstraintProvider.HARD_LEVEL
            , SouthChinaDesignScheduleBaseConstraintProvider.SOFT_LEVEL),
    // 国内质检工单的分配
    CHINA_RANDOM_INSPECTION_SCHEDULE(ChinaRandomInspectionRuntimeSolution.class
            , ChinaRandomInspectionBaseConstraintProvider.class
            , List.of(NoChangeCustomPhaseCommand.class)
            , ChinaRandomInspectionBaseConstraintProvider.HARD_LEVEL
            , ChinaRandomInspectionBaseConstraintProvider.SOFT_LEVEL);

    private final Class<? extends BaseSolution> solutionClass;
    private final Class<? extends BaseConstraintProvider> providerClass;
    private final List<Class<? extends AbstractCustomPhaseCommand>> customPhaseClass;
    private final int hardLevelSize;
    private final int softLevelSize;
    private final Boolean multiPhase;

    SolverTypeEnum(Class<? extends BaseSolution> solutionClass
            , Class<? extends BaseConstraintProvider> providerClass
            , List<Class<? extends AbstractCustomPhaseCommand>> customPhaseClass, int hardLevelSize, int softLevelSize) {
        this.softLevelSize = softLevelSize;
        this.hardLevelSize = hardLevelSize;
        this.providerClass = providerClass;
        this.solutionClass = solutionClass;
        this.customPhaseClass = customPhaseClass;
        this.multiPhase = Boolean.FALSE;
    }

    public static SolverTypeEnum findSolverByName(String solverName) {
        return findSolverByNameAndVersion(solverName, "");
    }

    public static SolverTypeEnum findSolverByNameAndVersion(String solverName, String version) {
        return Stream.of(SolverTypeEnum.values())
                .filter(solverTypeEnum -> solverTypeEnum.name()
                        .equalsIgnoreCase(StringUtil.isBlank(version) ? solverName : solverName + "_" + version))
                .findFirst()
                .orElseGet(() -> Stream.of(SolverTypeEnum.values())
                        .filter(solverTypeEnum -> solverTypeEnum.name()
                                .equalsIgnoreCase(solverName))
                        .findFirst()
                        .orElseThrow(() -> new IllegalArgumentException("Can't find solver [" + solverName + "]")));
    }
}

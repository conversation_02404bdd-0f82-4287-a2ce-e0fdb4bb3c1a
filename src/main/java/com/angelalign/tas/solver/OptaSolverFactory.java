package com.angelalign.tas.solver;

import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.solver.phase.listener.PhaseAwareSolverEventListener;
import com.angelalign.tas.solver.version.SolverVersionRouterFactory;
import com.angelalign.tas.solver.version.base.SolverVersionRouter;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.score.buildin.bendablelong.BendableLongScore;
import org.optaplanner.core.api.solver.Solver;
import org.optaplanner.core.api.solver.SolverFactory;
import org.optaplanner.core.api.solver.SolverManager;
import org.optaplanner.core.config.solver.SolverConfig;
import org.optaplanner.core.impl.solver.AbstractSolver;
import org.optaplanner.core.impl.solver.DefaultSolverFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ReflectionUtils;

import java.lang.reflect.Method;

@Component
@Slf4j
public class OptaSolverFactory {
    @Autowired
    private SolverVersionRouterFactory solverVersionRouterFactory;

    private final OptaSolverConfigProvider optaSolverConfigProvider = new OptaSolverConfigProvider();

    public SolverManager<BaseSolution, Long> createSolverManager(Problem problem) {
        log.info("problem solverName {} version {}", problem.getSolverName(), problem.getVersion());
        SolverVersionRouter router = solverVersionRouterFactory.getRouter(problem.getSolverName());
        return createSolverManager(problem, router);
    }

    private SolverManager<BaseSolution, Long> createSolverManager(Problem problem, SolverVersionRouter router) {

        SolverConfig solverConfig = optaSolverConfigProvider.getDefaultSolverConfig(problem
                , router);

        // 创建SolverManager
        return SolverManager.create(solverConfig);
    }

    public SolverFactory<BaseSolution> createSolverFactory(Problem problem) {
        log.info("problem solverName {} version {}", problem.getSolverName(), problem.getVersion());
        SolverVersionRouter router = solverVersionRouterFactory.getRouter(problem.getSolverName());
        SolverConfig solverConfig = optaSolverConfigProvider.getDefaultSolverConfig(problem, router);

        // 创建SolverManager并注册事件监听器
        return SolverFactory.create(solverConfig);
    }

    public Solver<BaseSolution> createSolver(Problem problem) {
        SolverFactory<BaseSolution> solverFactory = createSolverFactory(problem);
        AbstractSolver<BaseSolution> solver = (AbstractSolver<BaseSolution>) solverFactory.buildSolver();
        // 添加阶段切换监听器
        solver.addPhaseLifecycleListener(new PhaseAwareSolverEventListener());
        return solver;
    }

    /**
     * 对解决方案进行评分
     */
    public BendableLongScore scoreSolution(Problem problem, BaseSolution baseSolution) {
        DefaultSolverFactory<BaseSolution> solverFactory = (DefaultSolverFactory<BaseSolution>) createSolverFactory(problem);

        try {
            // 调用 buildScoreDirectorFactory 方法
            Method buildScoreDirectorFactory = ReflectionUtils.findMethod(solverFactory.getClass(), "buildScoreDirectorFactory");
            ReflectionUtils.makeAccessible(buildScoreDirectorFactory);
            Object scoreDirectorFactory = ReflectionUtils.invokeMethod(buildScoreDirectorFactory, solverFactory);

            // 2.InnerScoreDirectorFactory 的 buildScoreDirector 方法
            Method buildScoreDirector = ReflectionUtils.findMethod(scoreDirectorFactory.getClass(), "buildScoreDirector");
            ReflectionUtils.makeAccessible(buildScoreDirector);
            Object scoreDirector = ReflectionUtils.invokeMethod(buildScoreDirector, scoreDirectorFactory);

            // 3. setWorkingSolution
            Method setWorkingSolution = ReflectionUtils.findMethod(scoreDirector.getClass(), "setWorkingSolution", Object.class);
            ReflectionUtils.invokeMethod(setWorkingSolution, scoreDirector, baseSolution);

            // 4. calculateScore 方法
            Method calculateScore = ReflectionUtils.findMethod(scoreDirector.getClass(), "calculateScore");
            ReflectionUtils.makeAccessible(calculateScore);
            BendableLongScore score = (BendableLongScore) ReflectionUtils.invokeMethod(calculateScore, scoreDirector);

            // 5. 关闭 ScoreDirector
            Method close = ReflectionUtils.findMethod(scoreDirector.getClass(), "close");
            if (close != null) {
                ReflectionUtils.invokeMethod(close, scoreDirector);
            }
            return score;

        } catch (Exception e) {
            log.error("Failed to score solution using reflection", e);
            throw new RuntimeException("Failed to calculate solution score using reflection", e);
        }
    }
}

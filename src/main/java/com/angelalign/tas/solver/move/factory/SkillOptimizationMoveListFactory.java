package com.angelalign.tas.solver.move.factory;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.solver.move.SkillOptimizationMove;
import org.optaplanner.core.impl.heuristic.move.Move;
import org.optaplanner.core.impl.heuristic.selector.move.factory.MoveListFactory;

import java.util.List;
import java.util.stream.Collectors;

public class SkillOptimizationMoveListFactory implements MoveListFactory<BaseSolution> {

    @Override
    public List<Move<BaseSolution>> createMoveList(BaseSolution solution) {
        return solution.getTaskList().stream()
            .flatMap(task -> solution.getDesignerList().stream()
                .filter(d -> d != task.getDesigner())
                .filter(d -> d.getAvailableQuota() >= task.getBaseDurationInMinutes())
                .filter(d -> calculateSkillMatch(task, d) > calculateSkillMatch(task, task.getDesigner()))
                .map(d -> new SkillOptimizationMove(task, d))
            ).collect(Collectors.toList());
    }

    private int calculateSkillMatch(Task task, Designer designer) {
        return (int) task.getRequiredSkill().stream()
            .filter(designer.getSkills()::contains)
            .count();
    }
}
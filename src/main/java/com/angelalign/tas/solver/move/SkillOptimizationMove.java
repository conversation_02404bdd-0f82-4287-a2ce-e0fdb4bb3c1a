package com.angelalign.tas.solver.move;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Skill;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.score.director.ScoreDirector;
import org.optaplanner.core.impl.heuristic.move.AbstractMove;

import java.util.Set;

@Slf4j
public class SkillOptimizationMove extends AbstractMove<BaseSolution> {
    private final Task task;
    private final Designer newDesigner;

    public SkillOptimizationMove(Task task, Designer newDesigner) {
        this.task = task;
        this.newDesigner = newDesigner;
    }

    @Override
    protected AbstractMove<BaseSolution> createUndoMove(ScoreDirector<BaseSolution> scoreDirector) {
        return new SkillOptimizationMove(task, task.getDesigner());
    }

    @Override
    public AbstractMove<BaseSolution> rebase(ScoreDirector<BaseSolution> destinationScoreDirector) {
        return new SkillOptimizationMove(destinationScoreDirector.lookUpWorkingObject(task)
                , destinationScoreDirector.lookUpWorkingObject(newDesigner));
    }

    @Override
    protected void doMoveOnGenuineVariables(ScoreDirector<BaseSolution> scoreDirector) {
        scoreDirector.beforeVariableChanged(task, "designer");
        task.setDesigner(newDesigner);
        log.debug("SkillOptimizationMove picked move task {} -> {}", task.getDesigner().getCode(), newDesigner.getCode());
        scoreDirector.afterVariableChanged(task, "designer");

        // 更新技能匹配缓存
        scoreDirector.triggerVariableListeners();
    }

    @Override
    public boolean isMoveDoable(ScoreDirector<BaseSolution> scoreDirector) {
        return isMoreSuitable()
               && newDesigner.getSkills().containsAll(task.getRequiredSkill())
               && newDesigner.getAvailableQuota() >= task.getBaseDurationInMinutes();
    }

    private boolean isMoreSuitable() {
        int currentMatch = calculateSkillMatch(task.getRequiredSkill(), task.getDesigner().getSkills());
        int newMatch = calculateSkillMatch(task.getRequiredSkill(), newDesigner.getSkills());
        return newMatch > currentMatch;
    }

    private int calculateSkillMatch(Set<Skill> required, Set<Skill> actual) {
        return (int) required.stream().filter(actual::contains).count();
    }
}
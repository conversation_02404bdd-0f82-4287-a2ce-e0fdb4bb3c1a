package com.angelalign.tas.solver.move;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.score.director.ScoreDirector;
import org.optaplanner.core.impl.heuristic.move.AbstractMove;

import java.util.Objects;

@Slf4j
public class PriorityTaskReassignmentMove extends AbstractMove<BaseSolution> {

    private final Task task;
    private final Designer fromDesigner;
    private final Designer toDesigner;

    public PriorityTaskReassignmentMove(Task task, Designer fromDesigner, Designer toDesigner) {
        this.task = task;
        this.fromDesigner = fromDesigner;
        this.toDesigner = toDesigner;
    }

    @Override
    protected AbstractMove<BaseSolution> createUndoMove(ScoreDirector<BaseSolution> scoreDirector) {
        return new PriorityTaskReassignmentMove(task, toDesigner, fromDesigner);
    }
    @Override
    public AbstractMove<BaseSolution> rebase(ScoreDirector<BaseSolution> destinationScoreDirector) {
        return new PriorityTaskReassignmentMove(destinationScoreDirector.lookUpWorkingObject(task)
                , destinationScoreDirector.lookUpWorkingObject(toDesigner)
                , destinationScoreDirector.lookUpWorkingObject(fromDesigner));
    }
    @Override
    protected void doMoveOnGenuineVariables(ScoreDirector<BaseSolution> scoreDirector) {
        scoreDirector.beforeVariableChanged(task, "designer");
        log.debug("PriorityTaskReassignmentMove picked move task {} -> {}", task.getDesigner().getCode(), toDesigner.getCode());
        task.setDesigner(toDesigner);
        scoreDirector.afterVariableChanged(task, "designer");
    }

    @Override
    public boolean isMoveDoable(ScoreDirector<BaseSolution> scoreDirector) {
        return !Objects.equals(fromDesigner, toDesigner)
               && toDesigner.getSkills().containsAll(task.getRequiredSkill())
               && task.getPriorityScore() > 100
               && toDesigner.getAvailableQuota() >= task.getBaseDurationInMinutes();
    }
}

package com.angelalign.tas.solver.move.factory;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.solver.move.PriorityTaskReassignmentMove;
import org.optaplanner.core.impl.heuristic.move.Move;
import org.optaplanner.core.impl.heuristic.selector.move.factory.MoveListFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

public class PriorityTaskMoveListFactory implements MoveListFactory<BaseSolution> {

    @Override
    public List<Move<BaseSolution>> createMoveList(BaseSolution solution) {
        List<Move<BaseSolution>> moves = new ArrayList<>();
        Set<Designer> designers = solution.getDesignerList();
        solution.getTaskList().stream()
                .filter(task -> task.getPriorityScore() > 100)
                .forEach(task -> {
                    Designer currentDesigner = task.getDesigner();
                    designers.stream()
                            .filter(d -> d != currentDesigner)
                            .filter(d -> d.getSkills().containsAll(task.getRequiredSkill()))
                            // todo task.getBaseDurationInMinutes() 假设设计师是工时了
                            .filter(d -> d.getAvailableQuota() >= task.getBaseDurationInMinutes())
                            .forEach(d -> moves.add(new PriorityTaskReassignmentMove(task, currentDesigner, d)));
                });
        return moves;
    }
}
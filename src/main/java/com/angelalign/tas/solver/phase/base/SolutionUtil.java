package com.angelalign.tas.solver.phase.base;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import org.optaplanner.core.api.score.director.ScoreDirector;
import org.optaplanner.core.impl.score.director.InnerScoreDirector;

import java.util.Arrays;

public class SolutionUtil {
    /**
     * 判断当前分配的结果是否可以满足所有硬约束
     *
     * @param solution solution
     * @return 是否认可
     */
    public static boolean scoreAccept(BaseSolution solution) {
        return solution.getScore().initScore() >= 0 && Arrays.stream(solution.getScore().hardScores()).allMatch(s -> s >= 0);
    }

    /**
     * 改变工单的设计并且尝试打分
     *
     * @param scoreDirector scoreDirector
     * @param task          工单
     * @param designer      要改变的设计师
     */
    public static void changeDesignerAndReScore(ScoreDirector<BaseSolution> scoreDirector, Task task, Designer designer) {
        scoreDirector.beforeVariableChanged(task, "designer");
        task.setDesigner(designer);
        scoreDirector.afterVariableChanged(task, "designer");
        scoreDirector.triggerVariableListeners();
        // 触发评分
        ((InnerScoreDirector<BaseSolution, ?>) scoreDirector).calculateScore();
    }

    /**
     * 检测分数是否有提高，如果有提高返回提高的索引
     *
     * @param originScore 原始的分数
     * @param newScore    新的分数
     * @return 分数提高的索引，如果没有提高返回-1
     */
    public static int scoreNoImprove(long[] originScore, long[] newScore) {
        for (int i = 0; i < originScore.length; i++) {
            if (originScore[i] < newScore[i]) return i;
        }
        return -1;
    }
}

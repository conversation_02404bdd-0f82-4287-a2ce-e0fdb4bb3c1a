package com.angelalign.tas.solver.phase.listener;

import com.angelalign.tas.solver.phase.enums.PhaseName;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.config.phase.PhaseConfig;
import org.optaplanner.core.config.solver.SolverConfig;

import java.util.LinkedHashMap;
import java.util.concurrent.ConcurrentLinkedQueue;

@Slf4j
public class PhaseQueueManager {
    private static final ConcurrentLinkedQueue<PhaseName> phaseQueue = new ConcurrentLinkedQueue<>();

    @Getter
    private static volatile PhaseName currentPhase = PhaseName.init;
    private static volatile boolean isInitialized = false;

    public static void registerPhaseConfig(LinkedHashMap<PhaseName, PhaseConfig> phaseConfig, SolverConfig solverConfig) {
        solverConfig.setPhaseConfigList(phaseConfig.values().stream().toList());

        synchronized (PhaseQueueManager.class) {
            phaseQueue.clear();
            phaseQueue.addAll(phaseConfig.keySet());
            isInitialized = true;
        }
    }

    /**
     * 更新当前阶段
     */
    public static synchronized void updateCurrentPhase() {
        if (!isInitialized) {
            currentPhase = PhaseName.init;
            return;
        }

        PhaseName nextPhase = phaseQueue.poll();
        if (nextPhase != null) {
            currentPhase = nextPhase;
            log.info("Phase updated to: {}", currentPhase);
        } else {
            log.warn("No more phases in queue");
        }
    }

    public static synchronized void invalidateAll() {
        currentPhase = PhaseName.init;
        phaseQueue.clear();
        isInitialized = false;
    }
}
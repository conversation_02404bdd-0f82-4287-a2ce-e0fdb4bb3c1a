package com.angelalign.tas.solver.phase.listener;

import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.score.base.util.PreferTaskExpectedRatioUtil;
import com.angelalign.tas.solver.phase.enums.PhaseName;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.impl.phase.event.PhaseLifecycleListener;
import org.optaplanner.core.impl.phase.scope.AbstractPhaseScope;
import org.optaplanner.core.impl.phase.scope.AbstractStepScope;
import org.optaplanner.core.impl.solver.scope.SolverScope;

@Slf4j
public class PhaseAwareSolverEventListener implements PhaseLifecycleListener<BaseSolution> {

    @Override
    public void phaseStarted(AbstractPhaseScope<BaseSolution> phaseScope) {
        String phaseName = phaseScope.getClass().getName();
        log.info("phase start: {}", phaseName);
        // 更新当前阶段的名字
        PhaseQueueManager.updateCurrentPhase();
    }

    @Override
    public void stepStarted(AbstractStepScope<BaseSolution> stepScope) {

    }

    @Override
    public void stepEnded(AbstractStepScope<BaseSolution> stepScope) {

    }

    @Override
    public void phaseEnded(AbstractPhaseScope<BaseSolution> phaseScope) {
        String phaseName = phaseScope.getClass().getName();
        log.info("phase end : {}", phaseName);
        // 更新一下本次阶段的值
        if (PhaseName.preferTaskSimulatedAnnealingPhase.equals(PhaseQueueManager.getCurrentPhase())) {
            phaseScope.getSolverScope().getBestSolution()
                    .getDesignerList()
                    .forEach(designer -> {
                        PreferTaskExpectedRatioUtil.calculatePreferTaskQuotaRatio(designer);
                        PreferTaskExpectedRatioUtil.calculatePreferSkillQuotaRatio(designer);
                    });
        }
    }

    @Override
    public void solvingStarted(SolverScope<BaseSolution> solverScope) {
        log.info("solvingStarted: {}", solverScope.getClass().getName());
    }

    @Override
    public void solvingEnded(SolverScope<BaseSolution> solverScope) {
        log.info("solvingEnded: {}", solverScope.getClass().getName());
    }
}

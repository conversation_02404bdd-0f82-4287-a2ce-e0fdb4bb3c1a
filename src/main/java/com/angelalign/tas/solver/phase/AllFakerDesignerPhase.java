package com.angelalign.tas.solver.phase;

import com.angelalign.tas.score.base.util.DesignerIdentityUtil;
import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.assignment.enums.DesignerRankEnum;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;

public class AllFakerDesignerPhase extends AbstractCustomPhaseCommand {
    @Override
    protected Designer choosePossibleDesigner(Task task, BaseSolution workingSolution) {
        if (!DesignerIdentityUtil.enableFakerDesigner()) {
            return null;
        }
        return workingSolution.getDesignerList().stream()
                .filter(designer -> DesignerRankEnum.FAKER.equals(designer.getRank()))
                .findFirst()
                .orElseThrow();
    }
}

package com.angelalign.tas.solver.phase.base;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.score.director.ScoreDirector;
import org.optaplanner.core.impl.phase.custom.CustomPhaseCommand;

/**
 * 参考文档
 * <a href="https://docs.optaplanner.org/9.44.0.Final/optaplanner-docs/html_single/index.html#customSolverPhase">...</a>
 */
@Slf4j
public abstract class AbstractCustomPhaseCommand implements CustomPhaseCommand<BaseSolution> {
    @Override
    public void changeWorkingSolution(ScoreDirector<BaseSolution> scoreDirector) {
        log.info("pre construction heuristic {} phase start", this.getClass().getSimpleName());
        BaseSolution workingSolution = scoreDirector.getWorkingSolution();
        for (Task task : workingSolution.getTaskList()) {
            if (task.getDesigner() != null) continue;
            scoreDirector.beforeVariableChanged(task, "designer");
            task.setDesigner(choosePossibleDesigner(task, workingSolution));
            scoreDirector.afterVariableChanged(task, "designer");
            scoreDirector.triggerVariableListeners();
        }

        log.info("pre construction heuristic {} phase done", this.getClass().getSimpleName());
    }

    protected abstract Designer choosePossibleDesigner(Task task, BaseSolution workingSolution);
}

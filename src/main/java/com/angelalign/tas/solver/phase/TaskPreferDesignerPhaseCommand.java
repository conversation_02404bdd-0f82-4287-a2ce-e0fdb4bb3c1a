package com.angelalign.tas.solver.phase;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;
import com.angelalign.tas.util.DecimalUtils;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Optional;
import java.util.Set;

public class TaskPreferDesignerPhaseCommand extends AbstractCustomPhaseCommand {
    @Override
    protected Designer choosePossibleDesigner(Task task, BaseSolution workingSolution) {
        if (CollectionUtils.isEmpty(task.getPreferredDesigner())) return null;
        Set<Designer> preferredDesigner = task.getPreferredDesigner();
        Optional<Designer> min = preferredDesigner.stream()
                // 取当前指定的工单额度最小的
                .min((o1, o2) -> DecimalUtils.preciseRound(o1.getAppointedTaskQuota() - o2.getAppointedTaskQuota()));
        return min.orElse(null);
    }
}

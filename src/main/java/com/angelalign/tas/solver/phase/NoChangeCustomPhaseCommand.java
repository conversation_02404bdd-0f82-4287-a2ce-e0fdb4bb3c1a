package com.angelalign.tas.solver.phase;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;

public class NoChangeCustomPhaseCommand extends AbstractCustomPhaseCommand {
    @Override
    protected Designer choosePossibleDesigner(Task task, BaseSolution workingSolution) {
        return null;
    }
}

package com.angelalign.tas.solver.phase;

import com.angelalign.tas.domain.assignment.CacheCaseTask;
import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.assignment.enums.TaskTypeCodeEnum;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.score.base.util.CaseTaskUtil;
import com.angelalign.tas.solver.phase.base.AbstractCustomPhaseCommand;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
public class FullDesignModifyTaskPhaseCommand extends AbstractCustomPhaseCommand {

    // 创建缓存实例，设置缓存策略
    private final Cache<BaseSolution, Map<String, Designer>> designerMapCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.SECONDS)
            .maximumSize(5000)
            .build();

    private final Cache<BaseSolution, Set<String>> originIdSetCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.SECONDS)
            .maximumSize(5000)
            .build();

    /**
     * 选择可能的设计师进行分配
     *
     * @param task 工单
     * @return 可能的设计师
     */
    @Override
    protected Designer choosePossibleDesigner(Task task, BaseSolution workingSolution) {
        // 全设计修改 分给之前做过这个病例的人
        if (!TaskTypeCodeEnum.executeFullDesignModification.name().equals(task.getTaskType().getCode())) return null;

        // 从缓存中获取designerMap，如果没有则计算并放入缓存
        Map<String, Designer> designerMap = designerMapCache.get(workingSolution, solution -> {
            Map<String, Designer> map = new HashMap<>();
            for (Designer designer : solution.getDesignerList()) {
                map.put(designer.getOriginId(), designer);
            }
            return map;
        });

        // 从缓存中获取originIdSet，如果没有则计算并放入缓存
        Set<String> originIdSet = originIdSetCache.get(workingSolution, solution -> {
            Set<String> set = new HashSet<>();
            for (Designer designer : solution.getDesignerList()) {
                set.add(designer.getOriginId());
            }
            return set;
        });

        List<CacheCaseTask> caseTaskList = CaseTaskUtil.findAndSortByCaseCode(task.getCaseCode(), task.getProblem());
        for (CacheCaseTask cacheCaseTask : caseTaskList) {
            if (cacheCaseTask.getAssigneeId() == null) continue;
            if (!TaskTypeCodeEnum.isDesignTask(cacheCaseTask.getTaskTypeCode())) continue;
            if (!cacheCaseTask.getPhaseType().equals(task.getPhaseType())) continue;
            String assigneeIdStr = String.valueOf(cacheCaseTask.getAssigneeId());
            if (!originIdSet.contains(assigneeIdStr)) continue;
            Designer designer = designerMap.get(assigneeIdStr);
            if (designer == null || !designer.getOnDuty()) continue;
            return designer;
        }
        return null;
    }
}

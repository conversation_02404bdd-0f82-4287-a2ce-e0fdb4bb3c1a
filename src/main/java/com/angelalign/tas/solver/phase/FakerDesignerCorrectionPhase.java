package com.angelalign.tas.solver.phase;

import com.angelalign.tas.score.base.util.DesignerIdentityUtil;
import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.assignment.enums.DesignerRankEnum;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.solver.phase.base.SolutionUtil;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.score.director.ScoreDirector;
import org.optaplanner.core.impl.phase.custom.CustomPhaseCommand;
import org.optaplanner.core.impl.score.director.InnerScoreDirector;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;

@Slf4j
public class FakerDesignerCorrectionPhase implements CustomPhaseCommand<BaseSolution> {
    @Override
    public void changeWorkingSolution(ScoreDirector<BaseSolution> scoreDirector) {
        // 不开启虚拟设计师模式，就不进行计算了
        if (!DesignerIdentityUtil.enableFakerDesigner()) {
            return;
        }
        log.info("fakerDesignerCorrectionPhase start");
        BaseSolution solution = scoreDirector.getWorkingSolution();
        // 分数是接受的，算出了解，无需补偿
        if (SolutionUtil.scoreAccept(solution)) {
            log.info(solution.getScore().toString());
            log.info("fakerDesignerCorrectionPhase end");
            return;
        }

        Designer fakerDesigner = solution.getDesignerList().stream()
                .filter(designer -> DesignerRankEnum.FAKER.equals(designer.getRank()))
                .findFirst()
                .orElseThrow();

        List<Task> taskList = solution.getTaskList().stream()
                .sorted(Comparator.comparing(Task::getPriorityScore))
                .toList();
        for (Task task : taskList) {
            // 更改工单负责人那是虚拟设计师来提高分数
            chooseFakerDesignerIfNecessary(scoreDirector, task, fakerDesigner);
            // 如果分数都满足硬约束，就不需要重新计算了
            if (SolutionUtil.scoreAccept(solution)) {
                log.info(solution.getScore().toString());
                log.info("fakerDesignerCorrectionPhase end");
                return;
            }
        }
        if (!SolutionUtil.scoreAccept(solution)) {
            changeWorkingSolution(scoreDirector);
        }
    }

    /**
     * 如果必要，选择faker设计师并重新评分
     * 当工单分配给faker设计师的硬约束评分没有提高时，恢复原设计师
     *
     * @param scoreDirector 分数导演，用于管理评分和解决方案的变更
     * @param task          需要分配设计师的任务
     * @param fakerDesigner 拟使用的faker设计师
     */
    private void chooseFakerDesignerIfNecessary(ScoreDirector<BaseSolution> scoreDirector, Task task, Designer fakerDesigner) {
        // 工单原本的设计师
        Designer originDesigner = task.getDesigner();
        BaseSolution workingSolution = scoreDirector.getWorkingSolution();
        long[] originScore = workingSolution.getScore().hardScores();
        int originInitScore = workingSolution.getScore().initScore();

        // 更改成为faker 的设计师
        SolutionUtil.changeDesignerAndReScore(scoreDirector, task, fakerDesigner);
        long[] newScore = workingSolution.getScore().hardScores();
        int newInitScore = workingSolution.getScore().initScore();
        int hardScoreImproveIndex = SolutionUtil.scoreNoImprove(originScore, newScore);

        // 如果硬约束没有提高，就改回去
        if (SolutionUtil.scoreNoImprove(new long[]{originInitScore}, new long[]{newInitScore}) < 0
            && hardScoreImproveIndex < 0) {
            SolutionUtil.changeDesignerAndReScore(scoreDirector, task, originDesigner);
            return;
        }
        log.info("correction phase: task {} -> faker designer", task.getCode());
        // 记录工单没有分配的索引
        task.setUnassignedReason(hardScoreImproveIndex);
    }


}

package com.angelalign.tas.analysis.rest.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 工单类型分布方差详情
 * 包含每种工单类型的分布统计信息
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskTypeCountVariance {
    /**
     * 工单类型名称
     */
    private String taskTypeCode;

    /**
     * 该工单类型在所有设计师中的分布方差
     */
    private Double variance;

    /**
     * 标准差
     */
    private Double standardDeviation;

    /**
     * 平均分配数量
     */
    private Double mean;

    /**
     * 参与分配的设计师总数
     */
    private Integer designerCount;

    /**
     * 该类型工单的总数量
     */
    private Long totalCount;
}

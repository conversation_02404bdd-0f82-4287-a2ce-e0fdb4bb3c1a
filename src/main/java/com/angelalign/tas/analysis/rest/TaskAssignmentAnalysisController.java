package com.angelalign.tas.analysis.rest;

import com.angelalign.tas.analysis.rest.vo.TaskAssignmentComparisonResult;
import com.angelalign.tas.analysis.rest.vo.TaskAssignmentMetrics;
import com.angelalign.tas.analysis.service.TaskAssignmentAnalysisService;
import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.rest.vo.RespResult;
import com.angelalign.tas.service.ProblemService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/task-assignment/analysis")
@Slf4j
@CrossOrigin
class TaskAssignmentAnalysisController {
    @Autowired
    private TaskAssignmentAnalysisService taskAssignmentAnalysisService;
    @Autowired
    private ProblemService problemService;

    @GetMapping("/compare/{problemId}")
    public ResponseEntity<RespResult<TaskAssignmentComparisonResult>> compare(@PathVariable("problemId") Long problemId) {
        Problem problem = problemService.getProblemById(problemId).orElseThrow();
        BaseSolution solution = problem.convertToSolution();

        TaskAssignmentComparisonResult taskAssignmentMetrics = taskAssignmentAnalysisService.compareAllocations(solution);
        return ResponseEntity.ok(RespResult.success(taskAssignmentMetrics));
    }

    @GetMapping("/{problemId}")
    public ResponseEntity<RespResult<TaskAssignmentMetrics>> calculateAnalysis(@PathVariable("problemId") Long problemId) {
        Problem problem = problemService.getProblemById(problemId).orElseThrow();
        BaseSolution solution = problem.convertToSolution();

        TaskAssignmentMetrics taskAssignmentMetrics = taskAssignmentAnalysisService.analyzeAllocation(solution);

        return ResponseEntity.ok(RespResult.success(taskAssignmentMetrics));
    }
}

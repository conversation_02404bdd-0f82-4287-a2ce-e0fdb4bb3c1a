package com.angelalign.tas.analysis.rest.vo;

import lombok.Builder;
import lombok.Data;

/**
 * 分配改进情况
 */
@Data
@Builder
public class TaskAssignmentImprovements {
    private Double workloadBalanceImprovement;      // 工作量平衡度改进
    private Double efficiencyImprovement;           // 效率改进
    private Double fairnessImprovement;             // 公平性改进
    private Double preferredSkillImprovement;       // 偏好技能改进
    private Double sameRankVarianceImprovement;     // 同职级方差改进（正值表示方差减小）
    private Double quotaVarianceImprovement;        // 额度方差改进（正值表示方差减小）
    private double improvementScore;
    private Boolean hasOverallImprovement;
}
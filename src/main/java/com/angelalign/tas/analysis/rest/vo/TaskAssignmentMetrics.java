package com.angelalign.tas.analysis.rest.vo;

import com.angelalign.tas.rest.vo.SolutionDetailVo;
import lombok.Data;
import lombok.Builder;
import lombok.AllArgsConstructor;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 任务分配指标
 * 用于分析一次分配结果的各项指标和方差数据
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TaskAssignmentMetrics {
    /**
     * 分配详情
     */
    private SolutionDetailVo solutionDetailVo;
    /**
     * 分数
     */
    private String score;

    // ========== 基础指标 ==========
    /**
     * 平均任务额度
     */
    private Double designerAverageAssignedTaskQuota;

    /**
     * 总任务额度
     */
    private Double totalTaskQuota;

    // ========== 职级相关方差 ==========
    /**
     * 相同职级工时方差
     * 衡量同一职级设计师之间工时分配的均匀程度
     */
    private Double sameRankWorkHoursVariance;


    // ========== 任务类型方差 ==========
    /**
     * 工单类型分布方差详情
     * 每种工单类型在所有设计师中的分布方差
     */
    private List<TaskTypeCountVariance> taskTypeDistributionVariance;


    // ========== 额度相关方差 ==========
    /**
     * 相同额度设计师工时方差
     * 衡量具有相同额度限制的设计师之间工时分配的均匀程度
     */
    private Double sameQuotaDesignerWorkHoursVariance;


    // ========== 偏好技能指标 ==========
    /**
     * 偏好技能达成比例
     * 分配给设计师偏好技能任务的比例
     */
    private Double preferredSkillAchievementRatio;

    /**
     * 偏好技能覆盖率
     * 有偏好技能任务分配的设计师占总设计师的比例
     */
    private Double designerPreferredSkillCoverageRatio;

    /**
     * 偏好技能匹配度
     * 偏好技能任务与设计师技能的匹配程度
     */
    private Double preferredSkillMatchingDegree;

    // ========== 综合平衡指标 ==========
    /**
     * 整体工作量平衡度
     * 综合评估所有设计师工作量分配的平衡程度
     */
    private Double overallWorkloadBalance;

    /**
     * 分配效率指数
     * 综合评估分配结果的效率
     */
    private Double allocationEfficiencyIndex;

    /**
     * 公平性指数
     * 评估分配结果的公平程度
     */
    private Double fairnessIndex;
    private Double quotaUtilizationVariance;
    }

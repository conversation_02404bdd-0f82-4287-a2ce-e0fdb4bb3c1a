package com.angelalign.tas.analysis.service;

import com.angelalign.tas.analysis.rest.vo.TaskAssignmentMetrics;
import com.angelalign.tas.analysis.rest.vo.TaskTypeCountVariance;
import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.DesignerConsumedQuotaTask;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.parser.DesignerAttributeParser;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.rest.vo.SolutionDetailVo;
import com.angelalign.tas.score.base.util.DesignerCapacityConvertor;
import com.angelalign.tas.score.base.util.DesignerPreferTaskOrSkillUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务分配指标计算器
 * 用于计算分配结果的各项指标和方差数据
 */
@Slf4j
@Service
public class TaskAssignmentMetricsCalculator {

    /**
     * 计算分配指标
     */
    public TaskAssignmentMetrics calculateMetrics(BaseSolution solution) {
        Set<Designer> designers = solution.getDesignerList().stream()
                .peek(Designer::init).collect(Collectors.toSet());

        return TaskAssignmentMetrics.builder()
                // 分数
                .score(solution.getScore().toString())
                // 分配详情
                .solutionDetailVo(SolutionDetailVo.convertFromWithNotVerifyScore(solution.getProblem(), solution))
                // 计算相同职级工时方差
                .sameRankWorkHoursVariance(calculateSameRankWorkHoursVariance(designers))
                // 计算工单类型方差
                .taskTypeDistributionVariance(calculateTaskTypeDistributionVariance(new HashSet<>(designers)))
                //  相同额度设计师工时方差
                .sameQuotaDesignerWorkHoursVariance(calculateSameQuotaDesignerWorkHoursVariance(designers))
                // 计算额度利用率方差
                .quotaUtilizationVariance(calculateQuotaUtilizationVariance(designers))
                // 工单偏好技能达成比例
                .preferredSkillAchievementRatio(calculatePreferredSkillAchievementRatio(designers))
                // 计算设计师偏好技能覆盖率
                .designerPreferredSkillCoverageRatio(calculateDesignerPreferredSkillCoverageRatio(designers))
                // 计算偏好技能匹配度
//                .preferredSkillMatchingDegree(calculatePreferredSkillMatchingDegree(designers))
                // 计算整体工作量平衡度
                .overallWorkloadBalance(calculateOverallWorkloadBalance(designers))
                // 计算分配效率指数
                .allocationEfficiencyIndex(calculateAllocationEfficiencyIndex(designers))
                // 计算公平性指数
                .fairnessIndex(calculateFairnessIndex(designers))
                .build();
    }

    /**
     * 计算平均任务额度
     */
    private Double calculateAverageTaskQuota(List<Task> tasks) {
        if (tasks.isEmpty()) return 0.0;
        return tasks.stream()
                .mapToDouble(Task::getBaseDurationInCount)
                .average()
                .orElse(0.0);
    }

    /**
     * 计算总任务额度
     */
    private Double calculateTotalTaskQuota(List<Task> tasks) {
        return tasks.stream()
                .mapToDouble(Task::getBaseDurationInCount)
                .sum();
    }

    /**
     * 计算相同职级工时方差
     */
    private Double calculateSameRankWorkHoursVariance(Set<Designer> designers) {
        Map<Integer, List<Double>> rankWorkHours = designers.stream()
                .collect(Collectors.groupingBy(
                        Designer::getDesignerLevelSequence,
                        Collectors.mapping(
                                Designer::getTotalConsumedQuota,
                                Collectors.toList()
                        )
                ));

        double totalVariance = 0.0;
        int rankCount = 0;

        for (List<Double> workHours : rankWorkHours.values()) {
            if (workHours.size() > 1) {
                totalVariance += calculateVariance(workHours);
                rankCount++;
            }
        }

        return rankCount > 0 ? totalVariance / rankCount : 0.0;
    }

    /**
     * 计算工单类型方差
     */
    private List<TaskTypeCountVariance> calculateTaskTypeDistributionVariance(Set<Designer> designers) {
        // 收集所有工单类型
        Set<String> allTaskTypes = new HashSet<>();

        // 每个设计师的工单类型分布 Map<设计师ID, Map<工单类型, 数量>>
        Map<String, Map<String, Long>> designerTaskTypeDistribution = new HashMap<>();

        for (Designer designer : designers) {
            // 该设计师分到的工单类型和数量
            Map<String, Long> designerTaskTypeCountMap = DesignerCapacityConvertor.getDesignerTaskTypeCountMap(designer);
            designerTaskTypeDistribution.put(designer.getCode(), designerTaskTypeCountMap);

            // 收集所有出现的工单类型
            allTaskTypes.addAll(designerTaskTypeCountMap.keySet());
        }

        // 计算每种工单类型在所有设计师中的分布方差
        List<TaskTypeCountVariance> result = new ArrayList<>();

        for (String taskType : allTaskTypes) {
            // 收集该工单类型在所有设计师中的分配数量
            List<Double> taskTypeCounts = new ArrayList<>();

            for (Designer designer : designers) {
                Map<String, Long> designerTaskTypes = designerTaskTypeDistribution.get(designer.getCode());
                // 如果该设计师没有这种类型的工单，数量为0
                Long count = designerTaskTypes.getOrDefault(taskType, 0L);
                taskTypeCounts.add(count.doubleValue());
            }

            // 计算该工单类型的方差
            Double variance = calculateVariance(taskTypeCounts);

            // 计算平均值和标准差
            Double mean = taskTypeCounts.stream()
                    .mapToDouble(Double::doubleValue)
                    .average()
                    .orElse(0.0);
            Double standardDeviation = Math.sqrt(variance);

            // 计算总数量
            Long totalCount = taskTypeCounts.stream().mapToLong(Double::longValue).sum();

            // 创建结果对象
            TaskTypeCountVariance taskTypeVariance =
                    TaskTypeCountVariance.builder()
                            .taskTypeCode(taskType)
                            .variance(variance)
                            .standardDeviation(standardDeviation)
                            .mean(mean)
                            .designerCount(designers.size())
                            .totalCount(totalCount)
                            .build();

            result.add(taskTypeVariance);
        }

        // 按方差降序排列，方差大的排在前面（说明分布不均匀）
        result.sort((a, b) -> Double.compare(b.getVariance(), a.getVariance()));

        return result;
    }

    /**
     * 计算相同额度设计师工时方差
     */
    private Double calculateSameQuotaDesignerWorkHoursVariance(Set<Designer> designers) {
        Map<Double, List<Double>> quotaGroups = designers.stream()
                .collect(Collectors.groupingBy(
                        Designer::getOverAllocationLimit,
                        Collectors.mapping(
                                Designer::getTotalConsumedQuota,
                                Collectors.toList()
                        )
                ));

        double totalVariance = 0.0;
        int groupCount = 0;

        for (List<Double> workHours : quotaGroups.values()) {
            if (workHours.size() > 1) {
                totalVariance += calculateVariance(workHours);
                groupCount++;
            }
        }

        return groupCount > 0 ? totalVariance / groupCount : 0.0;
    }

    /**
     * 计算额度方差
     */
    private Double calculateQuotaDistributionVariance(Set<Designer> designers) {
        List<Double> quotas = designers.stream()
                .map(Designer::getOverAllocationLimit)
                .collect(Collectors.toList());

        return calculateVariance(quotas);
    }

    /**
     * 计算额度利用率方差
     */
    private Double calculateQuotaUtilizationVariance(Set<Designer> designers) {
        List<Double> utilizationRates = designers.stream()
                .map(designer -> {
                    double quota = designer.getOverAllocationLimit();
                    double consumed = designer.getTotalConsumedQuota();
                    return quota > 0 ? consumed / quota : 0.0;
                })
                .collect(Collectors.toList());

        return calculateVariance(utilizationRates);
    }

    /**
     * 计算偏好技能达成比例
     */
    private Double calculatePreferredSkillAchievementRatio(Set<Designer> designers) {
        double ratio = 0.0;

        for (Designer designer : designers) {
            Set<Task> allTasks = designer.getTasks();
            Set<Task> preferredTasks = DesignerPreferTaskOrSkillUtil.getPreferSkillTask(designer);

            List<String> preferSkillExpression = DesignerAttributeParser.getPreferSkillExpression(designer);
            List<DesignerConsumedQuotaTask> designerConsumedPreferSkillTask = DesignerPreferTaskOrSkillUtil.getDesignerConsumedPreferSkillTask(preferSkillExpression, designer);

            ratio += (double) (preferredTasks.size() + designerConsumedPreferSkillTask.size()) / Math.max(allTasks.size(), 1.0);
        }

        return ratio;
    }

    /**
     * 计算设计师偏好技能覆盖率
     */
    private Double calculateDesignerPreferredSkillCoverageRatio(Set<Designer> designers) {
        return designers.stream()
                .mapToDouble(designer -> {
                    Set<Task> preferredTasks = DesignerPreferTaskOrSkillUtil.getPreferSkillTask(designer);

                    List<String> preferSkillExpression = DesignerAttributeParser.getPreferSkillExpression(designer);
                    List<DesignerConsumedQuotaTask> designerConsumedPreferSkillTask = DesignerPreferTaskOrSkillUtil.getDesignerConsumedPreferSkillTask(preferSkillExpression, designer);
                    double totalQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferredTasks) + DesignerCapacityConvertor.covertConsumedTaskQuota(designer, designerConsumedPreferSkillTask);
                    return totalQuota / Math.max(designer.getOverAllocationLimit(), 1.0);
                }).average().orElse(0.0);
    }

    /**
     * 计算偏好技能匹配度
     */
    private Double calculatePreferredSkillMatchingDegree(Set<Designer> designers) {
        // 这里可以根据具体的技能匹配算法来实现
        // 暂时返回一个基于偏好任务比例的简单计算
        return calculatePreferredSkillAchievementRatio(designers);
    }

    /**
     * 计算整体工作量平衡度
     */
    private Double calculateOverallWorkloadBalance(Set<Designer> designers) {
        // 总的工作量
        List<Double> workloads = designers.stream()
                .map(Designer::getTotalConsumedQuota)
                .collect(Collectors.toList());
        // 方差
        double variance = calculateVariance(workloads);
        // 平均值
        double mean = workloads.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);

        // 使用变异系数来衡量平衡度，值越小越平衡
        return mean > 0 ? 1.0 / (1.0 + Math.sqrt(variance) / mean) : 0.0;
    }

    /**
     * 计算分配效率指数
     */
    private Double calculateAllocationEfficiencyIndex(Set<Designer> designers) {
        return designers.stream()
                .mapToDouble(designer -> {
                    double quota = designer.getOverAllocationLimit();
                    double consumed = designer.getTotalConsumedQuota();
                    return quota > 0 ? Math.min(consumed / quota, 1.0) : 0.0;
                })
                .average()
                .orElse(0.0);
    }

    /**
     * 计算公平性指数
     */
    private Double calculateFairnessIndex(Set<Designer> designers) {
        List<Double> utilizationRates = designers.stream()
                .map(designer -> {
                    double quota = designer.getOverAllocationLimit();
                    double consumed = designer.getTotalConsumedQuota();
                    return quota > 0 ? consumed / quota : 0.0;
                })
                .collect(Collectors.toList());

        // 使用基尼系数的简化版本
        double mean = utilizationRates.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        double variance = calculateVariance(utilizationRates);

        return mean > 0 ? 1.0 - (variance / (mean * mean)) : 0.0;
    }

    /**
     * 计算方差
     */
    private Double calculateVariance(List<Double> values) {
        if (values.size() <= 1) return 0.0;

        double mean = values.stream().mapToDouble(Double::doubleValue).average().orElse(0.0);
        return values.stream()
                .mapToDouble(value -> Math.pow(value - mean, 2))
                .average()
                .orElse(0.0);
    }
}

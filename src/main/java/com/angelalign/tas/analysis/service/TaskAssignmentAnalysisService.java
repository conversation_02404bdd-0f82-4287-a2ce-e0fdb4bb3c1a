package com.angelalign.tas.analysis.service;

import com.angelalign.tas.analysis.rest.vo.TaskAssignmentComparisonResult;
import com.angelalign.tas.analysis.rest.vo.TaskAssignmentImprovements;
import com.angelalign.tas.analysis.rest.vo.TaskAssignmentMetrics;
import com.angelalign.tas.common.gms.MtsServerProxy;
import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import com.angelalign.tas.score.base.util.DesignerIdentityUtil;
import com.angelalign.tas.solver.OptaSolverFactory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.optaplanner.core.api.score.buildin.bendablelong.BendableLongScore;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class TaskAssignmentAnalysisService {

    private final TaskAssignmentMetricsCalculator metricsCalculator;
    private final MtsServerProxy mtsServerProxy;
    private final OptaSolverFactory optaSolverFactory;

    /**
     * 分析分配结果
     */
    public TaskAssignmentMetrics analyzeAllocation(BaseSolution solution) {
        return metricsCalculator.calculateMetrics(solution);
    }

    /**
     * 当前分配结果和mts分配结果的对比
     *
     * @param solution 当前的分配结果
     * @return 对比结果对象
     */
    public TaskAssignmentComparisonResult compareAllocations(BaseSolution solution) {

        // tas 的结果先保存下来,计算分配的指标
        TaskAssignmentMetrics originalMetrics = metricsCalculator.calculateMetrics(solution);

        // 从gms拿到人工调整的结果
        List<AssignedTaskVo> mtsSolution = mtsServerProxy.getMtsSolution(solution.getProblem().getCallBackUrl()
                , solution.getTaskList().stream().map(Task::getOriginId).map(Long::valueOf).toList());

        // 拿到的结果调整成为一个solution
        BaseSolution adjusted = adjustSolutionByMtsResult(solution.getProblem(), solution, mtsSolution);

        // 再重新计算一次指标
        TaskAssignmentMetrics adjustedMetrics = metricsCalculator.calculateMetrics(adjusted);

        return TaskAssignmentComparisonResult.builder()
                .originalMetrics(originalMetrics)
                .adjustedMetrics(adjustedMetrics)
                .improvements(calculateImprovements(originalMetrics, adjustedMetrics))
                .build();
    }

    private BaseSolution adjustSolutionByMtsResult(Problem problem, BaseSolution originalSolution, List<AssignedTaskVo> mtsSolution) {
        // 创建新的Solution对象，避免修改原始数据
        BaseSolution adjustedSolution = problem.convertToSolution();
        Designer fakerDesigner = originalSolution.getDesignerList()
                .stream()
                .filter(DesignerIdentityUtil::isFakerDesigner)
                .findFirst()
                .orElseThrow();

        // 深拷贝任务列表并清空设计师分配
        List<Task> taskList = originalSolution.getTaskList()
                .stream()
                .map(task -> {
                    Task newTask = new Task();
                    newTask.setId(task.getId());
                    newTask.setOriginId(task.getOriginId());
                    newTask.setCode(task.getCode());
                    newTask.setCaseCode(task.getCaseCode());
                    newTask.setPhaseType(task.getPhaseType());
                    newTask.setTaskType(task.getTaskType());
                    newTask.setDentist(task.getDentist());
                    newTask.setBaseDurationInMinutes(task.getBaseDurationInMinutes());
                    newTask.setBaseDurationInCount(task.getBaseDurationInCount());
                    newTask.setPriorityScore(task.getPriorityScore());
                    newTask.setRequiredSkill(task.getRequiredSkill());
                    newTask.setPreferredDesigner(task.getPreferredDesigner());
                    newTask.setAttributes(task.getAttributes());
                    newTask.setOriginalTag(task.getOriginalTag());
                    newTask.setProblem(task.getProblem());
                    newTask.setDesigner(null);
                    return newTask;
                })
                .toList();

        // 深拷贝设计师列表并清空任务分配
        Set<Designer> designerList = originalSolution.getDesignerList()
                .stream()
                .map(designer -> {
                    Designer newDesigner = new Designer();
                    newDesigner.setId(designer.getId());
                    newDesigner.setOriginId(designer.getOriginId());
                    newDesigner.setCode(designer.getCode());
                    newDesigner.setRank(designer.getRank());
                    newDesigner.setCapacity(designer.getCapacity());
                    newDesigner.setSkills(designer.getSkills());
                    newDesigner.setAttributes(designer.getAttributes());
                    newDesigner.setProblem(designer.getProblem());
                    newDesigner.setHistoryConsumedQuota(designer.getHistoryConsumedQuota());
                    newDesigner.setAssignedQuota(designer.getAssignedQuota());
                    newDesigner.setDailyQuota(designer.getDailyQuota());
                    newDesigner.setOverAllocationLimit(designer.getOverAllocationLimit());
                    newDesigner.setAppointedTaskQuota(designer.getAppointedTaskQuota());
                    newDesigner.setTasks(new java.util.HashSet<>());
                    return newDesigner;
                })
                .collect(Collectors.toSet());

        // 根据MTS结果重新分配
        mtsSolution.stream()
                .filter(assignedTaskVo -> StringUtils.isNotBlank(assignedTaskVo.getDesignerId()))
                .forEach(assignedTaskVo -> {
                    Task task = taskList.stream()
                            .filter(t -> t.getOriginId().equals(assignedTaskVo.getTaskId()))
                            .findFirst()
                            .orElseThrow(() -> new IllegalArgumentException("Can't find task [" + assignedTaskVo.getTaskId() + "]"));
                    Designer designer = designerList.stream()
                            .filter(d -> d.getOriginId().equals(assignedTaskVo.getDesignerId()))
                            .findFirst()
                            .orElse(fakerDesigner);
                    task.setDesigner(designer);
                    designer.getTasks().add(task);
                });

        adjustedSolution.setTaskList(taskList);
        adjustedSolution.setDesignerList(designerList);

        // 触发optaplanner 重新计算一次打分
        BendableLongScore bendableLongScore = optaSolverFactory.scoreSolution(adjustedSolution.getProblem(), adjustedSolution);
        adjustedSolution.setScore(bendableLongScore);

        return adjustedSolution;
    }

    /**
     * 计算 工单分配结果的提升情况
     *
     * @param original 原始的
     * @param adjusted 调整以后的
     * @return 分数提升情况的结果
     */
    private TaskAssignmentImprovements calculateImprovements(TaskAssignmentMetrics original, TaskAssignmentMetrics adjusted) {
        // 分数改进度
        double improvementScore = calculateImprovementScore(original, adjusted);
        return TaskAssignmentImprovements.builder()
                .workloadBalanceImprovement(adjusted.getOverallWorkloadBalance() - original.getOverallWorkloadBalance())
                .efficiencyImprovement(adjusted.getAllocationEfficiencyIndex() - original.getAllocationEfficiencyIndex())
                .fairnessImprovement(adjusted.getFairnessIndex() - original.getFairnessIndex())
                .preferredSkillImprovement(adjusted.getPreferredSkillAchievementRatio() - original.getPreferredSkillAchievementRatio())
                .sameRankVarianceImprovement(original.getSameRankWorkHoursVariance() - adjusted.getSameRankWorkHoursVariance())
                .quotaVarianceImprovement(original.getSameQuotaDesignerWorkHoursVariance() - adjusted.getSameQuotaDesignerWorkHoursVariance())
                .improvementScore(improvementScore)
                .hasOverallImprovement(improvementScore > 0)
                .build();
    }


    private double calculateImprovementScore(TaskAssignmentMetrics original, TaskAssignmentMetrics adjusted) {
        String originalScore = original.getScore();
        String adjustedScore = adjusted.getScore();

        if (originalScore == null || adjustedScore == null) {
            log.info("分数为空，无法计算改进分数");
            return 0.0;
        }

        try {
            BendableLongScore originalBendableLongScore = BendableLongScore.parseScore(originalScore);
            BendableLongScore adjustedBendableLongScore = BendableLongScore.parseScore(adjustedScore);

            // 软约束改进分数
            double softScoreImprovement = calculateConstraintScoreImprovement(
                    originalBendableLongScore.softScores(), adjustedBendableLongScore.softScores());

            // 硬约束改进分数
            double hardScoreImprovement = calculateConstraintScoreImprovement(
                    originalBendableLongScore.hardScores(), adjustedBendableLongScore.hardScores());

            return softScoreImprovement * 0.2 + hardScoreImprovement * 0.8;

        } catch (Exception e) {
            log.error("计算改进分数时出错: {}", e.getMessage(), e);
            return 0.0;
        }
    }

    /**
     * 计算分数的提升情况，优先级越高，占得权重越大，硬约束的权重比软约束的权重大的很多
     *
     * @param originalScores 原始的分数数组
     * @param adjustedScores 调整以后的分数数组
     * @return 改进分数百分比
     */
    private double calculateConstraintScoreImprovement(long[] originalScores, long[] adjustedScores) {

        if (originalScores.length == 0) {
            return 0.0;
        }

        double improvement = 0.0;
        int softLevelsSize = originalScores.length;

        for (int i = 0; i < softLevelsSize; i++) {
            long originalScore = originalScores[i];
            long adjustedScore = adjustedScores[i];

            // 软约束级别越低（索引越小），权重越高
            // 使用指数权重：第0级权重最高，后续级别权重递减
            double levelWeight = Math.pow(0.7, i);

            // 计算相对改进
            double relativeImprovement = (double) (adjustedScore - originalScore) / Math.max(Math.abs(originalScore), 1);

            improvement += relativeImprovement * levelWeight;
        }

        return improvement;
    }

    /**
     * 获取改进分数的详细说明
     */
    public String getImprovementScoreExplanation(TaskAssignmentMetrics original, TaskAssignmentMetrics adjusted) {
        if (original.getScore() == null || adjusted.getScore() == null) {
            return "分数信息不完整，无法生成详细说明";
        }

        try {
            BendableLongScore originalScore = BendableLongScore.parseScore(original.getScore());
            BendableLongScore adjustedScore = BendableLongScore.parseScore(adjusted.getScore());

            StringBuilder explanation = new StringBuilder();
            explanation.append("=== 分数改进详细分析 ===\n");

            // 硬约束分析
            explanation.append("硬约束对比:\n");
            long[] originalHard = originalScore.hardScores();
            long[] adjustedHard = adjustedScore.hardScores();
            for (int i = 0; i < originalHard.length; i++) {
                explanation.append(String.format("  级别%d: %d → %d (权重: %.1f)\n",
                        i, originalHard[i], adjustedHard[i], Math.pow(2, originalHard.length - i)));
            }

            // 软约束分析
            explanation.append("软约束对比:\n");
            long[] originalSoft = originalScore.softScores();
            long[] adjustedSoft = adjustedScore.softScores();
            for (int i = 0; i < originalSoft.length; i++) {
                explanation.append(String.format("  级别%d: %d → %d (权重: %.3f)\n",
                        i, originalSoft[i], adjustedSoft[i], Math.pow(0.7, i)));
            }

            double totalImprovement = calculateImprovementScore(original, adjusted);
            explanation.append(String.format("总体改进分数: %.4f\n", totalImprovement));

            return explanation.toString();

        } catch (Exception e) {
            return "生成改进分数说明时出错: " + e.getMessage();
        }
    }
}

package com.angelalign.tas.queue;

import com.angelalign.tas.persistence.TaProblemRepository;
import com.angelalign.tas.service.SolverService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;

import javax.persistence.EntityManager;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
public class ProblemSolveJob {
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private EntityManager entityManager;
    @Autowired
    private TaProblemRepository taProblemRepository;
    @Autowired
    @Lazy
    private ProblemSolveJob problemSolveJob;
    @Autowired
    private SolverService solverService;
    @Autowired
    private TransactionTemplate transactionTemplate;

    @Scheduled(fixedDelay = 100)
    @Transactional(propagation = Propagation.NOT_SUPPORTED)
    public void execute() {
        while (true) {
            Long id;
            RLock lock = redissonClient.getLock("tas:lock:ProblemSolveJob");
            try {
                lock.lock(10, TimeUnit.SECONDS);
                id = transactionTemplate.execute(status -> problemSolveJob.acquireProblemIdWithLock());
            } finally {
                lock.unlock();
            }

            if (id == null || id == 0L) return;

            try {
                log.info("acquire a problem id {}", id);
                log.info("tas start solve {}", id);
                long start = System.currentTimeMillis();
                solverService.blockSolve(id);
                log.info("tas end solve {} during {}", id, System.currentTimeMillis() - start);
            } finally {
                transactionTemplate.executeWithoutResult(status -> {
                    taProblemRepository.updateProblemStatusSolved(id);
                });
            }
        }
    }

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Long acquireProblemIdWithLock() {
        Long lastProblem = taProblemRepository.findLastProblem();
        if (lastProblem != null && lastProblem > 0L) {
            log.info("acquireProblemIdWithLock: {}", lastProblem);
            taProblemRepository.updateProblemStatusSolving(lastProblem);
            entityManager.flush();
        }
        return lastProblem;
    }
}
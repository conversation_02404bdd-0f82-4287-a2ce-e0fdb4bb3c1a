//package com.angelalign.tas.queue;
//
//import com.angelalign.tas.persistence.TaProblemRepository;
//import com.angelalign.tas.service.SolveProblemService;
//import lombok.extern.slf4j.Slf4j;
//import org.redisson.api.RBlockingDeque;
//import org.redisson.api.RLock;
//import org.redisson.api.RSet;
//import org.redisson.api.RedissonClient;
//import org.springframework.beans.factory.DisposableBean;
//import org.springframework.beans.factory.InitializingBean;
//import org.springframework.beans.factory.annotation.Autowired;
//
//import java.util.List;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//import java.util.concurrent.TimeUnit;
//
//// 不启用
////@Component
//@Slf4j
//public class ProblemQueueService implements InitializingBean, DisposableBean {
//    @Autowired
//    private RedissonClient redissonClient;
//    @Autowired
//    private TaProblemRepository taProblemRepository;
//    @Autowired
//    private SolveProblemService solveProblemService;
//    private RBlockingDeque<String> deque;
//    private RSet<String> processingSet;
//    private ExecutorService executorService;
//
//    public void addProblemToQueue(Long id) {
//        deque.addLast(String.valueOf(id));
//    }
//
//    @Override
//    public void afterPropertiesSet() throws Exception {
//        deque = redissonClient.getBlockingDeque("tas:queue:problem");
//        processingSet = redissonClient.getSet("tas:set:problem");
//        deque.clear();
//        List<Long> unSolvedProblem = taProblemRepository.findUnSolvedProblem();
//        List<String> list = unSolvedProblem.stream()
//                .map(String::valueOf)
//                .filter(data -> !processingSet.contains(data))
//                .toList();
//        deque.addAll(list);
//        executorService = Executors.newSingleThreadExecutor();
//        executorService.submit(() -> {
//            while (!Thread.currentThread().isInterrupted()) {
//                try {
//                    String id = deque.takeFirst();
//                    log.info("problem queue takeFirst: {}", id);
//                    RLock lock = redissonClient.getLock("tas:lock:problem:" + id);
//                    try {
//                        lock.lock(10, TimeUnit.SECONDS);
//                        processingSet.add(id);
//                    } finally {
//                        if (lock.isHeldByCurrentThread()) {
//                            lock.unlock();
//                        }
//                    }
//                    try {
//                        log.info("problem queue solve: {}", id);
//                        // 去执行
//                        solveProblemService.blockSolve(Long.valueOf(id));
//                    } finally {
//                        // 正在处理的集合删掉这个问题
//                        processingSet.remove(id);
//                        log.info("problem queue solve end: {}", id);
//                    }
//                } catch (Exception e) {
//                    log.error(e.getMessage(), e);
//                }
//            }
//        });
//    }
//
//    @Override
//    public void destroy() throws Exception {
//        if (executorService != null) {
//            executorService.shutdown();
//            if (!executorService.awaitTermination(10, TimeUnit.SECONDS)) {
//                executorService.shutdownNow();
//            }
//        }
//    }
//}
package com.angelalign.tas.service;

import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.solution.base.BaseSolution;
import com.angelalign.tas.rest.vo.SolutionDetailVo;
import com.angelalign.tas.rest.vo.SolutionVo;
import com.angelalign.tas.score.base.cache.CacheManagementService;
import com.angelalign.tas.solver.OptaSolverFactory;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.solver.Solver;
import org.optaplanner.core.api.solver.SolverJob;
import org.optaplanner.core.api.solver.SolverManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.function.BiFunction;

@Service
@Slf4j
public class SolverService {
    @Autowired
    private ProblemService problemService;
    @Autowired
    private OptaSolverFactory optaSolverFactory;
    @Autowired
    private CacheManagementService cacheManagementService;

    public SolutionVo blockSolve(Long problemId) {
        return solveInternalWithSolver(problemId, SolutionVo::convertFrom);
    }

    public SolutionDetailVo blockSolveDetail(Long problemId) {
        return solveInternalWithSolver(problemId, SolutionDetailVo::convertFrom);
    }
    public SolutionDetailVo blockSolveWithSolver(Long problemId) {
        return solveInternalWithSolver(problemId, SolutionDetailVo::convertFrom);
    }
    @SneakyThrows
    @Deprecated
    private <T> T solveInternal(Long problemId, BiFunction<Problem, BaseSolution, T> converter) {
        Problem problem = problemService.getProblemById(problemId)
                .orElseThrow(() -> new IllegalArgumentException("Can't find problem by id [" + problemId + "]"));

        SolverManager<BaseSolution, Long> solverManager = optaSolverFactory.createSolverManager(problem);

        BaseSolution initialSolution = problem.convertToSolution();

        SolverJob<BaseSolution, Long> solve = solverManager.solve(
                problemId,
                (id) -> initialSolution,
                problemService::finalBestSolutionHandeAndSaveConsumer,
                null
        );

        BaseSolution finalBestSolution = solve.getFinalBestSolution();

        close(solverManager);

        return converter.apply(problem, finalBestSolution);
    }

    @SneakyThrows
    private <T> T solveInternalWithSolver(Long problemId, BiFunction<Problem, BaseSolution, T> converter) {
        Problem problem = problemService.getProblemById(problemId)
                .orElseThrow(() -> new IllegalArgumentException("Can't find problem by id [" + problemId + "]"));
        // 创建求解器
        Solver<BaseSolution> solver = optaSolverFactory.createSolver(problem);

        BaseSolution finalBestSolution = solver.solve(problem.convertToSolution());
        // 结果回写到数据库
        problemService.finalBestSolutionHandeAndSaveConsumer(finalBestSolution);

        cacheManagementService.cleanupCacheAfterSolving();

        return converter.apply(problem, finalBestSolution);
    }

    private void close(SolverManager<BaseSolution, Long> solverManager) {
        // 清理缓存
        cacheManagementService.cleanupCacheAfterSolving();
        solverManager.close();
    }
}

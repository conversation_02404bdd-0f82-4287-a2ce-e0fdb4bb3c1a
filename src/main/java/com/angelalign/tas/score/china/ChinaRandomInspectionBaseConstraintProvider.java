package com.angelalign.tas.score.china;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.parser.DesignerAttributeParser;
import com.angelalign.tas.domain.parser.TaskAttributeParser;
import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.base.constraint.ConstraintDefinition;
import com.angelalign.tas.score.base.util.DesignerIdentityUtil;
import org.optaplanner.core.api.score.buildin.bendablelong.BendableLongScore;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintFactory;

import java.util.List;
import java.util.Objects;

public class ChinaRandomInspectionBaseConstraintProvider extends BaseConstraintProvider {
    public static final int HARD_LEVEL = 10;
    public static final int SOFT_LEVEL = 4 + HARD_LEVEL;

    @Override
    public List<Constraint> getHardConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 设计师必须有可用的产能才进行计算
                designerMustHasAvailableCapacityPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(0)
                                .description("设计师必须有可用的产能才进行计算")
                                .build()
                )
                // 技能匹配
                , noMissingSkillsPenalizeConstraint(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1)
                                .description("技能匹配")
                                .build()
                )
                // 设计师的优先工单比例不能超过配额
                , designerPreferTaskLimitedPenalizeConstraint(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(2)
                                .description("设计师的优先工单比例不能超过配额")
                                .build()
                )
                // 抽检工单不可以分给它同组的设计师
                , taskTeamCannotSameDesignerTeamPenalizeConstraint(constraintFactory, 3, HARD_LEVEL, SOFT_LEVEL)
                // 不在岗设计师惩罚约束
                , unDutyDesignerPenalizeConstraint(constraintFactory, 4, HARD_LEVEL, SOFT_LEVEL)
        );
    }


    @Override
    public List<Constraint> getSoftConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 虚拟的的设计师需要扣分
                super.fakerDesignerPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1 + HARD_LEVEL)
                                .description("设计师的优先工单比例不能超过配额")
                                .build()
                )
                // 填满优先工单
                , designerPreferTaskByCapacityRewardConstraint_v4(constraintFactory, 1 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
                , designerCapacityOutOfWorkPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(2 + HARD_LEVEL)
                                .description("如果用户设置了超额分配，且当前已经超额，会算出超额的百分比进行加分，超的越多，优先级越低")
                                .build()
                )
                // 不同员工的工时尽量均衡
                , minimizeWorkTimeVariancePenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(3 + HARD_LEVEL)
                                .description("不同员工的工时尽量均衡")
                                .build()
                )
        );
    }

    /**
     * 抽检工单和设计师不能在一个组
     *
     * @return Constraint
     */
    private Constraint taskTeamCannotSameDesignerTeamPenalizeConstraint(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Task.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .penalize(BendableLongScore.ofHard(hardLevelSize, softLevelSize, level, 1)
                        , task -> {
                            Designer designer = task.getDesigner();
                            String taskTeam = TaskAttributeParser.getTaskTeam(task);
                            String designerTeam = DesignerAttributeParser.getDesignerTeam(designer);
                            return Objects.equals(taskTeam, designerTeam) ? 1 : 0;
                        })
                .asConstraint("taskTeamCannotSameDesignerTeamPenalizeConstraint");
    }
}

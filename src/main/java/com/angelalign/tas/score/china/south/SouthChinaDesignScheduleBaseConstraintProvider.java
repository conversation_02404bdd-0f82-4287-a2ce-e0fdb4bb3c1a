package com.angelalign.tas.score.china.south;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Skill;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.base.constraint.ConstraintDefinition;
import org.apache.commons.collections4.CollectionUtils;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintFactory;

import java.util.List;
import java.util.Set;
import java.util.function.Predicate;

public class SouthChinaDesignScheduleBaseConstraintProvider extends BaseConstraintProvider {
    public static final int HARD_LEVEL = 10;
    public static final int SOFT_LEVEL = 10 + HARD_LEVEL;

    @Override
    public List<Constraint> getHardConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 设计师必须有可用的产能才进行计算
                designerMustHasAvailableCapacityPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(0)
                                .privilegePredicateList(
                                        List.of(
                                                // 病例指定分配的设计师不看产能
                                                preferredDesignerPrivilege()
                                        ))
                                .description("设计师必须有可用的产能才进行计算")
                                .build())
                // 技能匹配
                , noMissingSkillsPenalizeConstraint(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1)
                                .privilegePredicateList(
                                        List.of(
                                                // 病例指定分配设计师如果只差难度标签 特权
                                                caseAssignmentAndDifficultyTagOrRedYellowLightPrivilege()
                                        ))
                                .description("技能匹配")
                                .build()
                )
                // 设计师的优先工单比例不能超过配额
                , designerPreferTaskLimitedPenalizeConstraint(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(2)
                                .description("设计师的优先工单比例不能超过配额")
                                .build()
                )
                // 如设计师“是否全设计修改只分配自己的”=是，这些设计师不可分配其他人的全设计修改单
                , fullDesignModifyExclusivePenalizeConstraint(constraintFactory, 3, HARD_LEVEL, SOFT_LEVEL)
                // 工单必须分配给他偏向的设计师
                , taskMustAssignPreferredDesignerPenalizeConstraint_ForChina(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(4)
                                .description("单必须分配给他偏向的设计师")
                                .build()
                )
                // 惩罚不在岗的设计师
                , unDutyDesignerPenalizeConstraint(constraintFactory, 5, HARD_LEVEL, SOFT_LEVEL)
        );
    }

    @Override
    public List<Constraint> getSoftConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 虚拟的的设计师需要扣分
                super.fakerDesignerPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(HARD_LEVEL)
                                .description("虚拟的的设计师需要扣分")
                                .build()
                )
                // 工单分给它倾向的设计师加分约束
                , taskPreferredDesignerRewardConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1 + HARD_LEVEL)
                                .description("工单分给它倾向的设计师加分约束")
                                .build()
                )

                // 优先填满设计师的优先工单
                , designerPreferTaskByCapacityRewardConstraint_v4(constraintFactory, 2 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
                // 优先填满设计师的偏好技能
                , designerPreferSkillByCapacityRewardConstraint_v4(constraintFactory, 3 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
                // 如果用户设置了超额分配，且当前已经超额，会算出超额的百分比进行加分，超的越多，优先级越低
                , designerCapacityOutOfWorkPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(4 + HARD_LEVEL)
                                .description("如果用户设置了超额分配，且当前已经超额，会算出超额的百分比进行加分，超的越多，优先级越低")
                                .build()
                )
                // 不同员工的工作量按照日额度均衡
                , minimizeWorkTimeByCapacityVariancePenalizeConstraint_v3(constraintFactory, 5 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
                // 设计师优先分某些特定的标签
                , designerPreferMedTagRewardConstraint_V2(constraintFactory, 6 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL, List.of("CTG_0009_OP01", "CTG_0009_OP02"))
                // 工单更倾向于低职级的设计师
                , taskPreferLowLevelPenalizeConstraint(constraintFactory, 7 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
                // 工单类型按照设计师的日额度比例均衡
                , minimizeTaskTypeCountByCapacityRatioPenalizeConstraint_v4(constraintFactory, 8 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
        );
    }

    /**
     * 普通设计师无需查看红黄灯的技能
     * 病例指定分配设计师 无需校验难度标签和长尾标签
     */
    private Predicate<Task> caseAssignmentAndDifficultyTagOrRedYellowLightPrivilege() {
        return task -> {
            // 获取缺失技能code列表
            List<Skill> missingSkills = task.getMissingSkill();
            List<String> missingSkillCodes = missingSkills.stream()
                    .map(Skill::getCode)
                    .toList();

            // 条件1：是否所有缺失技能都属于红黄灯标签
            boolean isRedYellowLightMatched = ConstraintDefinition.redYellowLightTag.containsAll(missingSkillCodes);
            if (isRedYellowLightMatched) return true;

            // 条件2：是否有指定设计师，并且当前设计师在指定列表中
            Set<Designer> preferredDesigners = task.getPreferredDesigner();
            if (CollectionUtils.isEmpty(preferredDesigners)) return false;

            Designer designer = task.getDesigner();
            if (designer == null) return false;
            String currentDesignerCode = designer.getCode();

            boolean isAssignedDesigner = preferredDesigners.stream()
                    .anyMatch(d -> currentDesignerCode.equals(d.getCode()));
            if (!isAssignedDesigner) return false;

            // 条件3：是否所有缺失技能都是难度标签
            return ConstraintDefinition.difficultyTagList.containsAll(missingSkillCodes);
        };
    }
}

package com.angelalign.tas.score.oversea.schedule;

import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.base.constraint.ConstraintDefinition;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintFactory;

import java.util.List;

import static com.angelalign.tas.score.base.constraint.PreferSkillConstraint.designerPreferSkillByCapacityRewardConstraint_WithOutTaskType;
import static com.angelalign.tas.score.base.constraint.PreferTaskConstraint.designerPreferTaskByCapacityRewardConstraint_WithOutTaskType;

@Slf4j
public class OverSeaDdmScheduleTaskHardBaseConstraintProvider extends BaseConstraintProvider {
    public static final int HARD_LEVEL = 10;
    public static final int SOFT_LEVEL = 10 + HARD_LEVEL;

    @Override
    public List<Constraint> getHardConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 设计师必须有可用的产能才进行计算
                designerMustHasAvailableCapacityPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(0)
                                .description("设计师必须有可用的产能才进行计算")
                                .build()
                )
                // 技能匹配
                , noMissingSkillsPenalizeConstraint(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1)
                                .description("技能匹配")
                                .build()
                )
                // 设计师的优先工单比例不能超过配额
                , designerPreferTaskLimitedPenalizeConstraint(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(2)
                                .description("设计师的优先工单比例不能超过配额")
                                .build()
                )
                // 限制分配的设计师只能分配给它指定的工单
                , periodLimitDesignerOnlyAssignPreferTaskPenalizeConstraint(constraintFactory, 3, HARD_LEVEL, SOFT_LEVEL)
                // 不在岗的设计师惩罚约束
                , unDutyDesignerPenalizeConstraint(constraintFactory, 4, HARD_LEVEL, SOFT_LEVEL)
        );
    }

    @Override
    public List<Constraint> getSoftConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 虚拟的的设计师需要扣分
                super.fakerDesignerPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(HARD_LEVEL)
                                .description("虚拟的的设计师需要扣分")
                                .build()
                )
                // 如果用户设置了超额分配，且当前已经超额，会算出超额的百分比进行加分，超的越多，优先级越低
                , designerCapacityOutOfWorkPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1 + HARD_LEVEL)
                                .description("如果用户设置了超额分配，且当前已经超额，会算出超额的百分比进行加分，超的越多，优先级越低")
                                .build()
                )
                // 工单分给它倾向的设计师加分约束
                , taskPreferredDesignerRewardConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(2 + HARD_LEVEL)
                                .description("工单分给它倾向的设计师加分约束")
                                .build()
                )
                // 优先填满设计师的优先工单
                , designerPreferTaskByCapacityRewardConstraint_WithOutTaskType(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(3 + HARD_LEVEL)
                                .description("优先填满设计师的优先工单")
                                .build()
                )
                // 优先填满设计师的偏好技能
                , designerPreferSkillByCapacityRewardConstraint_WithOutTaskType(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(4 + HARD_LEVEL)
                                .description("优先填满设计师的偏好技能")
                                .build()
                ), minimizeWorkTimeVariancePenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(5 + HARD_LEVEL)
                                .description("不同员工的工时均衡")
                                .build()
                )
                // cbct 标签均衡
                , originTagBalancedRatioPenalizedConstraint(List.of("IRS_BLUE", "IRS_GOLD"), constraintFactory, 6 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
                // T+1 均衡
                , originTagBalancedRatioPenalizedConstraint(List.of("T_PLUS_ONE"), constraintFactory, 7 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
                // 加急标签均衡
                , originTagBalancedRatioPenalizedConstraint(List.of("EXPEDITED"), constraintFactory, 8 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
        );
    }
}

package com.angelalign.tas.score.oversea.schedule;

import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.base.constraint.ConstraintDefinition;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintFactory;

import java.util.List;

@Slf4j
public final class OverSeaDesignScheduleTaskHardBaseConstraintProvider extends BaseConstraintProvider {
    public static final int HARD_LEVEL = 10;
    public static final int SOFT_LEVEL = 10 + HARD_LEVEL;

    @Override
    public List<Constraint> getHardConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 设计师必须有可用的产能才进行计算
                designerMustHasAvailableCapacityPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(0)
                                .privilegePredicateList(
                                        List.of(
                                                // 病例指定分配的设计师不看产能
                                                preferredDesignerPrivilege()
                                        ))
                                .description("设计师必须有可用的产能才进行计算")
                                .build()
                )
                // 技能匹配
                , noMissingSkillsPenalizeConstraint(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1)
                                .description("技能匹配")
                                .build()
                )

                // 如设计师“是否全设计修改只分配自己的”=是，这些设计师不可分配其他人的全设计修改单
                , fullDesignModifyExclusivePenalizeConstraint(constraintFactory, 2, HARD_LEVEL, SOFT_LEVEL)
                // 设计师的优先工单比例不能超过配额
                , designerPreferTaskLimitedPenalizeConstraint(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(3)
                                .description("设计师的优先工单比例不能超过配额")
                                .build()
                )
                // 工单必须分配给他偏向的设计师
                // 工单必须分配给他偏向的设计师
                , taskMustAssignPreferredDesignerPenalizeConstraint_ForChina(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(4)
                                .description("单必须分配给他偏向的设计师")
                                .build()
                )
                // 不在岗的设计师惩罚约束
                , unDutyDesignerPenalizeConstraint(constraintFactory, 5, HARD_LEVEL, SOFT_LEVEL)
        );
    }

    @Override
    public List<Constraint> getSoftConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 虚拟的设计师需要扣分
                super.fakerDesignerPenalizeConstraint_v3(constraintFactory, HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
                // 优先分配给它倾向的设计师
                , taskPreferredDesignerRewardConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1 + HARD_LEVEL)
                                .description("优先分配给它倾向的设计师")
                                .build()
                )
                // 如果用户设置了超额分配，且当前已经超额，会算出超额的百分比进行加分，超的越多，优先级越低
                , designerCapacityOutOfWorkPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(2 + HARD_LEVEL)
                                .description("如果用户设置了超额分配，且当前已经超额，会算出超额的百分比进行加分，超的越多，优先级越低")
                                .build()
                )
                // 工单标签优先级加分约束，优先级越高，越容易分到
                , medTadLevelScoreRewardConstraint(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(3 + HARD_LEVEL)
                                .description("工单标签优先级加分约束，优先级越高，越容易分到")
                                .build()
                )
                // 优先填满设计师的优先工单
                , designerPreferTaskByCapacityRewardConstraint_v4(constraintFactory, 4 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
                // 优先填满设计师的偏好技能
                , designerPreferSkillByCapacityRewardConstraint_v4(constraintFactory, 5 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
                // 不同员工的工作量按照日额度均衡
                , minimizeWorkTimeByCapacityVariancePenalizeConstraint_v3(constraintFactory, 6 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
                // 对与某个类型任务，在符合技能要求的员工中，尽量平均
                , minimizeTaskTypeCountBalancePenalizeConstraint_v3(constraintFactory, 7 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
                // cbct的标签工单数量实现按照产能的比例均衡
                , originTagBalancedRatioPenalizedConstraint(List.of("IRS_BLUE", "IRS_GOLD"), constraintFactory, 8 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
        );
    }
}
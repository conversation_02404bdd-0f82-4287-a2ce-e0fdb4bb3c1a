package com.angelalign.tas.score.oversea;

import com.angelalign.tas.domain.assignment.CacheCaseTask;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.assignment.enums.DesignerRankEnum;
import com.angelalign.tas.domain.assignment.enums.TaskTypeCodeEnum;
import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.base.constraint.ConstraintDefinition;
import com.angelalign.tas.score.base.util.CaseTaskUtil;
import com.angelalign.tas.score.base.util.DesignerIdentityUtil;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.score.buildin.bendablelong.BendableLongScore;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintFactory;

import java.util.List;
import java.util.Set;

@Slf4j
public class DefaultManualClaimHardBaseConstraintProvider extends BaseConstraintProvider {
    public static final int HARD_LEVEL = 10;
    public static final int SOFT_LEVEL = 4 + HARD_LEVEL;

    @Override
    public List<Constraint> getHardConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 硬约束 惩罚被分配的designer数量大于1
                designerCountCannotOverOneHardPenalizeConstraint(constraintFactory, 0)
                // 技能匹配
                , noMissingSkillsPenalizeConstraint(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1)
                                .description("技能匹配")
                                .build()
                )
                // 不能领取工单有指定的设计
                , super.taskMustAssignPreferredDesignerPenalizeConstraint(constraintFactory, 2, HARD_LEVEL, SOFT_LEVEL, List.of(), Set.of())
                // 如果是全设计修改，不能领取其他人的
                , fullDesignModifyAssigneDesifnerPenalizeConstraint(constraintFactory, 3, HARD_LEVEL, SOFT_LEVEL)
                // 不在岗的设计师惩罚约束
                , unDutyDesignerPenalizeConstraint(constraintFactory, 4, HARD_LEVEL, SOFT_LEVEL)
        );
    }

    @Override
    public List<Constraint> getSoftConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 虚拟的的设计师需要扣分
                super.fakerDesignerPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1 + HARD_LEVEL)
                                .description("虚拟的的设计师需要扣分")
                                .build()
                )
                , taskPreferredDesignerRewardConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(2 + HARD_LEVEL)
                                .description("工单分给它倾向的设计师加分约束")
                                .build()
                )
                // 领取工单优先级更高的工单
                , super.taskPriorityScoreRewardConstraint(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(3 + HARD_LEVEL)
                                .description("工单分给它倾向的设计师加分约束")
                                .build()
                )
        );
    }


    /**
     * 如果是全设计修改工单，必须分给设计完成的设计师
     *
     * @return Constraint
     */
    private Constraint fullDesignModifyAssigneDesifnerPenalizeConstraint(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Task.class)
                .filter(task -> task.getDesigner() != null)
                .filter(task -> !DesignerRankEnum.FAKER.equals(task.getDesigner().getRank()))
                .filter(task -> TaskTypeCodeEnum.executeFullDesignModification.name().equals(task.getTaskType().getCode()))
                .penalize(BendableLongScore.ofHard(HARD_LEVEL, SOFT_LEVEL, level, 1L)
                        , task -> {
                            String originId = task.getDesigner().getOriginId();
                            // 当前病例的所有工单
                            List<CacheCaseTask> byCaseCode = CaseTaskUtil.findAndSortByCaseCode(task.getCaseCode(), task.getProblem());
                            String lastDesignTaskDesigner = byCaseCode.stream()
                                    .filter(cacheCaseTask -> cacheCaseTask.getAssigneeId() != null)
                                    .filter(caseTask -> caseTask.getPhaseType().equals(task.getPhaseType()))
                                    .findFirst()
                                    .map(CacheCaseTask::getAssigneeId)
                                    .map(String::valueOf)
                                    .orElse("");
                            return originId.equals(lastDesignTaskDesigner) ? 0 : 1;
                        })
                .asConstraint("fullDesignModifyAssigneDesifnerPenalizeConstraint");
    }

    /**
     * 硬约束 惩罚被分配的designer数量大于1
     *
     * @param factory factory
     * @param level   硬约束
     * @return Constraint
     */
    private Constraint designerCountCannotOverOneHardPenalizeConstraint(ConstraintFactory factory, int level) {
        return factory.forEach(Task.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .penalize(BendableLongScore.ofHard(HARD_LEVEL, SOFT_LEVEL, level, 1L)
                        , task -> Math.max(0, task.getDesigner().getTasks().size() - 1))
                .asConstraint("penalizeUnassignedTasks");
    }
}

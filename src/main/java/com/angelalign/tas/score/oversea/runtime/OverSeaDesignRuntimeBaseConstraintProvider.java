package com.angelalign.tas.score.oversea.runtime;

import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.base.constraint.ConstraintDefinition;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintFactory;

import java.util.List;
import java.util.Set;

@Slf4j
@Deprecated
public class OverSeaDesignRuntimeBaseConstraintProvider extends BaseConstraintProvider {
    public static final int HARD_LEVEL = 10;
    public static final int SOFT_LEVEL = 6 + HARD_LEVEL;

    @Override
    public List<Constraint> getHardConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 惩罚所有的工单
                penalizeAllTaskConstraint(constraintFactory, 0, HARD_LEVEL, SOFT_LEVEL
                        , List.of(
                                // 全设计修改分给上一个人
                                fullDesignModifyAssignLastDesignerPrivilege()
                                // 病例分配的设计师
                                , preferredDesignerPrivilege()
                        ), Set.of())
                // 全设计修改 如果这个设计师做过这个病例之前的工单，必须分给他
                , designModificationMustSameDesignerPenalizeConstraint(constraintFactory, 1, HARD_LEVEL, SOFT_LEVEL)
                // 不在岗的设计师惩罚约束
                , unDutyDesignerPenalizeConstraint(constraintFactory, 2, HARD_LEVEL, SOFT_LEVEL)
        );
    }

    @Override
    public List<Constraint> getSoftConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 虚拟的的设计师需要扣分
                super.fakerDesignerPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1 + HARD_LEVEL)
                                .description("虚拟的的设计师需要扣分")
                                .build()
                )
                // 按照优先级奖励对应的人
                , super.rewardPurposeTaskConstraint(constraintFactory, 2 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL, List.of(
                        // 全设计修改分给上一个人
                        fullDesignModifyAssignLastDesignerPrivilege()
                        // 病例分配的设计师
                        , preferredDesignerPrivilege()
                ))
                // 设计师需要有可用的产能
                , super.designerHasAvailableCapacityPenalizeSoftConstraint(constraintFactory, 3 + HARD_LEVEL, HARD_LEVEL, SOFT_LEVEL)
                // 如果用户设置了超额分配，且当前已经超额，会算出超额的百分比进行加分，超的越多，优先级越低
                , designerCapacityOutOfWorkPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(4 + HARD_LEVEL)
                                .description("如果用户设置了超额分配，且当前已经超额，会算出超额的百分比进行加分，超的越多，优先级越低")
                                .build()
                ));
    }
}
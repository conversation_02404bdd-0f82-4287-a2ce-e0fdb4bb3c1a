package com.angelalign.tas.score.oversea.runtime;

import com.angelalign.tas.domain.assignment.CacheCaseTask;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.assignment.enums.TaskTypeCodeEnum;
import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.base.constraint.ConstraintDefinition;
import com.angelalign.tas.score.base.util.CaseTaskUtil;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintFactory;

import java.util.List;
import java.util.Set;
import java.util.function.Predicate;

@Slf4j
@Deprecated
public class OverSeaDdmRuntimeTaskBaseConstraintProvider extends BaseConstraintProvider {
    public static int HARD_LEVEL = 10;
    public static int SOFT_LEVEL = 3 + HARD_LEVEL;

    @Override
    public List<Constraint> getHardConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 设计师必须有可用的产能才进行计算
                designerMustHasAvailableCapacityPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(0)
                                .description("设计师必须有可用的产能才进行计算")
                                .build()
                )
                // 技能匹配
                , noMissingSkillsPenalizeConstraint(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1)
                                .description("技能匹配")
                                .build()
                )   // 惩罚所有的工单
                , penalizeAllTaskConstraint(constraintFactory, 2, HARD_LEVEL, SOFT_LEVEL
                        , List.of(
                                // 数字化分给一个人
                                ddmTaskAssignSameDesigner()
                        ), Set.of())
                // 不在岗的设计师惩罚约束
                , unDutyDesignerPenalizeConstraint(constraintFactory, 3, HARD_LEVEL, SOFT_LEVEL)
        );
    }

    @Override
    public List<Constraint> getSoftConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                super.fakerDesignerPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(HARD_LEVEL)
                                .description("虚拟的的设计师需要扣分")
                                .build()
                )
                // 惩罚数字化工单和现在工单的距离
                , penalizedDdmTaskConstraint(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1 + HARD_LEVEL)
                                .description("惩罚数字化工单和现在工单的距离")
                                .build()
                )
        );
    }


    private Predicate<Task> ddmTaskAssignSameDesigner() {
        return task -> {
            String originId = task.getDesigner().getOriginId();
            // 当前病例的所有工单
            List<CacheCaseTask> byCaseCode = CaseTaskUtil.findAndSortByCaseCode(task.getCaseCode(), task.getProblem());
            return byCaseCode.stream()
                    .filter(cacheCaseTask -> cacheCaseTask.getAssigneeId() != null)
                    .filter(cacheCaseTask -> cacheCaseTask.getPhaseType().equals(task.getPhaseType()))
                    .filter(caseTask -> TaskTypeCodeEnum.isDdmTask(caseTask.getTaskTypeCode()))
                    .anyMatch(cacheCaseTask -> cacheCaseTask.getAssigneeId().toString().equals(originId));
        };
    }
}

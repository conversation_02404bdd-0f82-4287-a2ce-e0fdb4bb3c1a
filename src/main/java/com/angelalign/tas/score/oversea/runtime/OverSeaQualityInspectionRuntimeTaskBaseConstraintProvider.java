package com.angelalign.tas.score.oversea.runtime;

import com.angelalign.tas.score.base.BaseConstraintProvider;
import com.angelalign.tas.score.base.constraint.ConstraintDefinition;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintFactory;

import java.util.List;

@Slf4j
public class OverSeaQualityInspectionRuntimeTaskBaseConstraintProvider extends BaseConstraintProvider {
    public static int HARD_LEVEL = 10;
    public static int SOFT_LEVEL = 6 + HARD_LEVEL;

    @Override
    public List<Constraint> getHardConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 设计师必须有可用的产能才进行计算
                designerMustHasAvailableCapacityPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(0)
                                .description("设计师必须有可用的产能才进行计算")
                                .build()
                )
                // 技能匹配
                , noMissingSkillsPenalizeConstraint(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1)
                                .description("技能匹配")
                                .build()
                )
        );
    }

    @Override
    public List<Constraint> getSoftConstraints(ConstraintFactory constraintFactory) {
        return List.of(
                // 虚拟的设计师需要扣分
                super.fakerDesignerPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(1 + HARD_LEVEL)
                                .description("虚拟的的设计师需要扣分")
                                .build()
                )
                // 如果用户设置了超额分配，且当前已经超额，会算出超额的百分比进行加分，超的越多，优先级越低
                , designerCapacityOutOfWorkPenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(2 + HARD_LEVEL)
                                .description("如果用户设置了超额分配，且当前已经超额，会算出超额的百分比进行加分，超的越多，优先级越低")
                                .build()
                )
                // 不同员工的工作量均衡
                , super.minimizeWorkTimeVariancePenalizeConstraint_v2(
                        ConstraintDefinition.builder()
                                .constraintFactory(constraintFactory)
                                .hardLevelSize(HARD_LEVEL)
                                .softLevelSize(SOFT_LEVEL)
                                .constraintLevel(3 + HARD_LEVEL)
                                .description("不同员工的工作量均衡")
                                .build()
                )
        );
    }
}

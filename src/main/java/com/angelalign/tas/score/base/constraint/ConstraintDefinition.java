package com.angelalign.tas.score.base.constraint;

import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.solver.phase.enums.PhaseName;
import lombok.*;
import org.optaplanner.core.api.score.stream.ConstraintFactory;

import java.util.List;
import java.util.Set;
import java.util.function.Predicate;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ConstraintDefinition {
    private ConstraintFactory constraintFactory;
    private int constraintLevel;
    private int hardLevelSize;
    private int softLevelSize;
    @Builder.Default
    private List<Predicate<Task>> privilegePredicateList = List.of();
    @Builder.Default
    private Set<PhaseName> inactivePhases = Set.of();
    private String description;
    @Getter
    public static final Set<String> redYellowLightTag = Set.of(
            // 红黄灯
            "CTG_0009_OP01",
            "blueLight",
            "CTG_0009_OP02");
    @Getter
    public static final Set<String> difficultyTagList = Set.of(
            // 排牙A
            "ORDER_TAG-ARRANGE_A",
            // 排牙B
            "ORDER_TAG-ARRANGE_B",
            // 排牙C
            "ORDER_TAG-ARRANGE_C",
            // 排牙D
            "ORDER_TAG-ARRANGE_D",
            // 新A级
            "ORDER_TAG-NEW_A_LEVEL"
            // 新B级
            , "ORDER_TAG-NEW_B_LEVEL"
            // 新C级
            , "ORDER_TAG-NEW_C_LEVEL"
            // 新D级
            , "ORDER_TAG-NEW_D_LEVEL"
            // 新E级
            , "ORDER_TAG-NEW_E_LEVEL"
            // 新F级
            , "ORDER_TAG-NEW_F_LEVEL"
            // 中A级
            , "ORDER_TAG-MIDA"
            // 中B级
            , "ORDER_TAG-MIDB"
            // 中C级
            , "ORDER_TAG-MIDC"
            // 中D级
            , "ORDER_TAG-MIDD"
            // 中E级
            , "ORDER_TAG-MIDE"
            // 中F级
            , "ORDER_TAG-MIDF"
    );
}

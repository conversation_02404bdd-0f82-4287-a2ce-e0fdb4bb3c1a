package com.angelalign.tas.score.base.util;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import java.util.Set;
import java.util.concurrent.TimeUnit;

public class PreferTaskExpectedRatioUtil {

    protected static final Cache<String, Double> preferTaskExpectedRatioCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(5000).build();
    protected static final Cache<String, Double> preferSkillExpectedRatioCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(5000).build();

    public static void invalidateAll() {
        preferTaskExpectedRatioCache.invalidateAll();
        preferSkillExpectedRatioCache.invalidateAll();
    }

    public static double getDesignerExpectedPreferTaskRatio(Designer designer) {
        return preferTaskExpectedRatioCache.get(designer.getCode() + designer.getProblem().getId(), key -> 0.0);
    }

    public static double getDesignerExpectedPreferSkillRatio(Designer designer) {
        return preferSkillExpectedRatioCache.get(designer.getCode() + designer.getProblem().getId(), key -> 0.0);
    }

    public static void calculatePreferSkillQuotaRatio(Designer designer) {
        Set<Task> preferTask = DesignerPreferTaskOrSkillUtil.getPreferTask(designer);
        // 当前设计师本次分配的偏好技能工单额度
        Double designerPreferTaskActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferTask);
        Set<Task> preferSkillTask = DesignerPreferTaskOrSkillUtil.getPreferSkillTask(designer);
        // 当前设计师本次分配的偏好技能工单额度
        Double designerPreferSkillActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferSkillTask);
        // 当前设计师历史分配的工单（不可动）
        Double designerConsumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);
        double totalQuota = designer.getAppointedTaskQuota()
                            + designerPreferSkillActualQuota
                            + designerPreferTaskActualQuota
                            + designerConsumedQuota;
        double ratio = totalQuota / Math.max(designer.getCapacityDailyTotalQuota(), 1.0);
        preferSkillExpectedRatioCache.get(designer.getCode() + designer.getProblem().getId(), key -> ratio);
    }

    public static void calculatePreferTaskQuotaRatio(Designer designer) {
        Set<Task> preferSkillTask = DesignerPreferTaskOrSkillUtil.getPreferSkillTask(designer);
        // 当前设计师本次分配的偏好技能工单额度
        Double designerPreferSkillActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferSkillTask);
        // 当前设计师历史分配的工单（不可动）
        Double designerConsumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);
        double totalQuota = designer.getAppointedTaskQuota()
                            + designerPreferSkillActualQuota
                            + designerConsumedQuota;
        double ratio = totalQuota / Math.max(designer.getCapacityDailyTotalQuota(), 1.0);
        preferTaskExpectedRatioCache.get(designer.getCode() + designer.getProblem().getId(), key -> ratio);
    }
}

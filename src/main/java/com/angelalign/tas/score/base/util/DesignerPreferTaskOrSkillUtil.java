package com.angelalign.tas.score.base.util;

import com.angelalign.tas.domain.assignment.TaskType;
import com.angelalign.tas.domain.parser.DesignerAttributeParser;
import com.angelalign.tas.domain.parser.model.PreferTask;
import com.angelalign.tas.score.base.util.expression.ExpressionEvaluator;
import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.DesignerConsumedQuotaTask;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.parser.ConsumedQuotaTaskAttributeParser;
import com.angelalign.tas.domain.parser.TaskAttributeParser;
import com.angelalign.tas.rest.vo.support.MedTagVo;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.angelalign.tas.domain.parser.DesignerAttributeParser.getPreferSkillExpression;

public class DesignerPreferTaskOrSkillUtil {
    protected static final Cache<String, List<DesignerConsumedQuotaTask>> designerConsumedPreferTaskExpressionCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(1000).build();

    protected static final Cache<String, List<DesignerConsumedQuotaTask>> designerConsumedPreferSkillExpressionCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(1000).build();
    protected static final Cache<String, List<Designer>> taskPreferDesignerCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(1000).build();
    private static final Cache<String, List<String>> batchTaskAttributesCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(1000).build();
    private static final Cache<String, Boolean> taskExpressionCache = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(10000).build();


    public static void invalidateAll() {
        designerConsumedPreferTaskExpressionCache.invalidateAll();
        designerConsumedPreferSkillExpressionCache.invalidateAll();
        taskPreferDesignerCache.invalidateAll();
        batchTaskAttributesCache.invalidateAll();
        taskExpressionCache.invalidateAll();
    }

    public static List<Designer> getPreferSkillDesigner(Task task) {
        return taskPreferDesignerCache.get("skill" + task.getCode(), k -> {
            Set<Designer> allDesigner = task.getProblem().getDesigners();
            return allDesigner.stream()
                    .filter(designer -> DesignerPreferTaskOrSkillUtil.isPreferSkill(task, getPreferSkillExpression(designer)))
                    .toList();
        });
    }

    public static List<Designer> getPreferTaskDesigner(Task task) {
        return taskPreferDesignerCache.get("task" + task.getCode(), k -> {
            Set<Designer> allDesigner = task.getProblem().getDesigners();
            return allDesigner.stream()
                    .filter(designer -> DesignerPreferTaskOrSkillUtil.isPreferTask(task, DesignerAttributeParser.getPreferTaskList(designer)))
                    .toList();
        });
    }

    public static Set<Task> getPreferTask(Designer designer) {
        return designer.getTasks()
                .stream()
                .filter(task -> {
                    return isPreferTask(task, DesignerAttributeParser.getPreferTaskList(designer));
                })
                .collect(Collectors.toSet());
    }

    public static Set<Task> getPreferSkillTask(Designer designer) {
        return designer.getTasks()
                .stream()
                .filter(task -> isPreferSkill(task, getPreferSkillExpression(designer)))
                .collect(Collectors.toSet());
    }

    /**
     * 获取设计师已经占用额度的工单与优先工单匹配的数量
     *
     * @param preferTaskList 优先工单表达式
     * @param designer       设计师
     * @return 匹配数量
     */
    public static List<DesignerConsumedQuotaTask> getDesignerConsumedPreferTask(List<PreferTask> preferTaskList, Designer designer) {
        return designerConsumedPreferTaskExpressionCache.get(designer.getCode() + preferTaskList + designer.getProblem().getId()
                , key -> designer.getDesignerConsumedQuotaTasks()
                        .stream()
                        .filter(designerConsumedQuotaTask -> isConsumedPreferTask(designerConsumedQuotaTask, preferTaskList))
                        .toList());
    }

    public static List<DesignerConsumedQuotaTask> getDesignerConsumedPreferTask(Designer designer) {
        return designerConsumedPreferTaskExpressionCache.get(designer.getCode() + "getDesignerConsumedPreferTask" + designer.getProblem().getId()
                , key -> designer.getDesignerConsumedQuotaTasks()
                        .stream()
                        .filter(designerConsumedQuotaTask -> isConsumedPreferTask(designerConsumedQuotaTask, DesignerAttributeParser.getPreferTaskList(designer)))
                        .toList());
    }

    public static boolean isConsumedPreferTask(DesignerConsumedQuotaTask designerConsumedQuotaTask, List<PreferTask> preferTaskList) {
        return preferTaskList.stream().anyMatch(preferTask -> {
            List<String> taskMedTag = ConsumedQuotaTaskAttributeParser.getMegTag(designerConsumedQuotaTask)
                    .stream()
                    .map(MedTagVo::getCode)
                    .collect(Collectors.toList());
            String productLine = ConsumedQuotaTaskAttributeParser.getProductLine(designerConsumedQuotaTask);
            taskMedTag.add(productLine);

            String phaseName = ConsumedQuotaTaskAttributeParser.getPhaseName(designerConsumedQuotaTask);
            List<String> designTag = ConsumedQuotaTaskAttributeParser.getDesignTag(designerConsumedQuotaTask);
            taskMedTag.addAll(designTag);
            taskMedTag.add(phaseName);

            ConsumedQuotaTaskAttributeParser.getTaskRequireSkillCode(designerConsumedQuotaTask)
                    .stream()
                    .filter(skill -> skill.startsWith(designerConsumedQuotaTask.getTaskType().getCode()))
                    .findFirst()
                    .ifPresent(taskMedTag::add);
            boolean evaluate = ExpressionEvaluator.evaluate(preferTask.getExpression(), taskMedTag);
            designerConsumedQuotaTask.setPreferTask(evaluate);
            return evaluate;
        });
    }

    /**
     * 获取设计师已经占用额度的工单与优先技能匹配的数量
     *
     * @param expressionList 优先工单表达式
     * @param designer       设计师
     * @return 匹配数量
     */
    public static List<DesignerConsumedQuotaTask> getDesignerConsumedPreferSkillTask
    (List<String> expressionList, Designer designer) {
        return designerConsumedPreferSkillExpressionCache.get(designer.getCode() + expressionList + designer.getProblem().getId()
                , key -> designer.getDesignerConsumedQuotaTasks()
                        .stream()
                        .filter(designerConsumedQuotaTask -> isConsumedPreferSkill(designerConsumedQuotaTask, expressionList)
                        )
                        .toList());
    }

    /**
     * 获取设计师已经占用额度的工单与优先技能匹配的数量
     *
     * @param designer 设计师
     * @return 匹配数量
     */
    public static List<DesignerConsumedQuotaTask> getDesignerConsumedPreferSkillTask(Designer designer) {
        return designerConsumedPreferSkillExpressionCache.get(designer.getCode() + designer.getProblem().getId()
                , key -> designer.getDesignerConsumedQuotaTasks()
                        .stream()
                        .filter(designerConsumedQuotaTask -> isConsumedPreferSkill(designerConsumedQuotaTask, getPreferSkillExpression(designer))
                        )
                        .toList());
    }

    private static boolean isConsumedPreferSkill(DesignerConsumedQuotaTask designerConsumedQuotaTask, List<String> expressionList) {
        return expressionList.stream().anyMatch(expression -> {
            List<String> taskRequireSkillCode = ConsumedQuotaTaskAttributeParser.getTaskRequireSkillCode(designerConsumedQuotaTask);
            return ExpressionEvaluator.evaluate(expression, taskRequireSkillCode);
        });
    }

    /**
     * 获取当前已经分配的工单 符合优先工单的工单
     *
     * @param expressionList 偏好表达式列表
     * @param tasks          所有已经分配的工单
     * @return 工单集合
     */
    public static Set<Task> getDesignerPreferTask(List<PreferTask> expressionList, Set<Task> tasks) {
        return tasks.stream()
                .filter(task -> isPreferTask(task, expressionList))
                .collect(Collectors.toSet());
    }

    private static List<String> getTaskMatchAttributes(Task task) {
        return batchTaskAttributesCache.get(task.getOriginId() + task.getProblem().getId(), key -> {
            List<String> attributes = new ArrayList<>();

            // 添加MedTag codes
            TaskAttributeParser.getMegTag(task)
                    .stream()
                    .map(MedTagVo::getCode)
                    .forEach(attributes::add);

            String phaseName = TaskAttributeParser.getPhaseName(task);
            List<String> caseDesignType = TaskAttributeParser.getCaseDesignType(task);
            attributes.addAll(caseDesignType);
            attributes.add(phaseName);

            // 添加产品线
            attributes.add(TaskAttributeParser.getProductLine(task));

            // 添加技能代码
            // todo 临时办法，没好办法解决工单类型+PHASE_INSENSITIVE 问题
            TaskAttributeParser.getTaskRequireSkillCode(task)
                    .stream()
                    .filter(skill -> skill.startsWith(task.getTaskType().getCode()))
                    .findFirst()
                    .ifPresent(attributes::add);

            // 去重并返回不可变列表
            return attributes.stream().distinct().toList();
        });
    }

    private static boolean isPreferTask(Task task, List<PreferTask> expressionList) {
        return expressionList.stream()
                .anyMatch(preferTask -> taskExpressionCache.get("isPreferTask" + preferTask.getExpression() + task.getOriginId() + task.getProblem().getId()
                        , key -> {
                            List<String> taskAttributes = getTaskMatchAttributes(task);
                            if (taskAttributes == null) {
                                // 降级到实时计算
                                taskAttributes = getTaskMatchAttributes(task);
                            }

                            return ExpressionEvaluator.evaluate(preferTask.getExpression(), taskAttributes);
                        }));
    }

    /**
     * 判断工单是否是偏好技能工单
     *
     * @param task     工单
     * @param designer 设计师
     * @return 是否
     */
    public static boolean isDesignerPreferSkillTask(Task task, Designer designer) {
        if (designer == null) {
            return false;
        }
        return isPreferSkill(task, getPreferSkillExpression(designer));
    }

    /**
     * 判断工单是否是偏好工单
     *
     * @param task     工单
     * @param designer 设计师
     * @return 是否
     */
    public static boolean isDesignerPreferTask(Task task, Designer designer) {
        if (designer == null) {
            return false;
        }
        return isPreferTask(task, DesignerAttributeParser.getPreferTaskList(designer));
    }

    public static boolean isDesignerConsumedPreferTask(DesignerConsumedQuotaTask designerConsumedQuotaTask) {
        if (designerConsumedQuotaTask.getDesigner() == null) {
            return false;
        }
        return isConsumedPreferTask(designerConsumedQuotaTask, DesignerAttributeParser.getPreferTaskList(designerConsumedQuotaTask.getDesigner()));
    }

    public static boolean isDesignerConsumedPreferSkillTask(DesignerConsumedQuotaTask designerConsumedQuotaTask) {
        if (designerConsumedQuotaTask.getDesigner() == null) {
            return false;
        }
        return isConsumedPreferSkill(designerConsumedQuotaTask, getPreferSkillExpression(designerConsumedQuotaTask.getDesigner()));
    }

    /**
     * 获取当前已经分配的工单 符合偏好技能的工单
     *
     * @param expressionList 偏好表达式
     * @param tasks          所有符合已经分配的工单
     * @return 工单的集合
     */
    public static Set<Task> getDesignerPreferSkillTask(List<String> expressionList, Set<Task> tasks) {
        return tasks.stream()
                .filter(task -> isPreferSkill(task, expressionList))
                .collect(Collectors.toSet());
    }

    private static boolean isPreferSkill(Task task, List<String> expressionList) {
        return expressionList.stream().anyMatch(expression -> {
            return taskExpressionCache.get("isPreferSkill" + expression + task.getCode() + task.getOriginId() + task.getProblem().getId()
                    , key -> {
                        List<String> taskRequireSkillCode = TaskAttributeParser.getTaskRequireSkillCode(task);
                        return ExpressionEvaluator.evaluate(expression, taskRequireSkillCode);
                    });
        });
    }

    //    private static boolean isPreferSkill(Task task, List<String> expressionList) {
//        return expressionList.stream().anyMatch(expression -> taskExpressionCache.get(getCacheKey(task, expression), key -> {
//          List<String> taskRequireSkillCode = TaskAttributeParser.getTaskRequireSkillCode(task);
//          return ExpressionEvaluator.evaluate(expression, taskRequireSkillCode);
//      }));
//    }
    private static Long getCacheKey(Task task, String expression) {
        return (long) Objects.hash(
                expression,
                task.getOriginId(),
                task.getProblem().getId()
        );
    }

    public static Map<String, Long> getDesignerPreferSkillTaskTypeCountMap(Designer designer) {
        // 本次分配的工单类型和数量
        Map<String, Long> taskTypeCountMap = designer.getTasks().stream()
                .filter(task -> isPreferSkill(task, getPreferSkillExpression(designer)))
                .map(Task::getTaskType)
                .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));
        Map<String, Long> consumedTaskTypeCountMap = getDesignerConsumedPreferSkillTask(designer)
                .stream()
                .map(DesignerConsumedQuotaTask::getTaskType)
                .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));
        // 合并一下
        return Stream.concat(
                taskTypeCountMap.entrySet().stream(),
                consumedTaskTypeCountMap.entrySet().stream()
        ).collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                Long::sum
        ));
    }

    public static Map<String, Long> getDesignerPreferTaskTaskTypeCountMap(Designer designer) {
        // 本次分配的工单类型和数量
        Map<String, Long> taskTypeCountMap = designer.getTasks().stream()
                .filter(task -> isPreferTask(task, DesignerAttributeParser.getPreferTaskList(designer)))
                .map(Task::getTaskType)
                .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));

        Map<String, Long> consumedTaskTypeCountMap = getDesignerConsumedPreferTask(designer)
                .stream()
                .map(DesignerConsumedQuotaTask::getTaskType)
                .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));
        // 合并一下
        return Stream.concat(
                taskTypeCountMap.entrySet().stream(),
                consumedTaskTypeCountMap.entrySet().stream()
        ).collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                Long::sum
        ));
    }
}

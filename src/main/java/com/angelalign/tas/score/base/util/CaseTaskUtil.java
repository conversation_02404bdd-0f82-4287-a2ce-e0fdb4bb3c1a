package com.angelalign.tas.score.base.util;

import com.angelalign.tas.domain.assignment.CacheCaseTask;
import com.angelalign.tas.domain.assignment.CaseTask;
import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.util.StreamUtil;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

@Slf4j
public class CaseTaskUtil {
    protected static final Cache<String, List<CacheCaseTask>> caseTaskCache = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(5000).build();

    public static List<CacheCaseTask> findAndSortByCaseCode(String caseCode, Problem problem) {
        if (StringUtils.isBlank(caseCode) || problem == null) return List.of();
        return caseTaskCache.get(caseCode + problem.getId(), k -> problem.getCaseTasks()
                .stream()
                .filter(caseTask -> Objects.equals(caseTask.getCaseCode(), caseCode))
                .filter(caseTask -> caseTask.getAssigneeId() != null)
                .filter(StreamUtil.distinctByKey(CaseTask::getTaskId))
                .map(CacheCaseTask::covertFromDO)
                .sorted((o1, o2) -> o2.getTaskId().compareTo(o1.getTaskId()))
                .toList());
    }

    public static void reset() {
        caseTaskCache.invalidateAll();
    }
}

package com.angelalign.tas.score.base.util.math;

import com.angelalign.tas.domain.assignment.Designer;

import java.util.Arrays;
import java.util.List;
import java.util.function.Function;

public class VectorDeviationUtil {
    /**
     * 计算向量的单位向量
     */
    public static double[] unitVector(double[] vector) {
        double norm = Math.sqrt(Arrays.stream(vector).map(x -> x * x).sum());
        return Arrays.stream(vector).map(x -> x / norm).toArray();
    }

    public static double calculateVectorDeviation(double[] actual, double[] expected) {
        if (actual.length != expected.length) {
            throw new IllegalArgumentException("Vector dimensions must match");
        }
        // 归一化
        double actualSum = Arrays.stream(actual).sum();
        double expectedSum = Arrays.stream(expected).sum();

        if (actualSum == 0 || expectedSum == 0) return 0;

        // 计算归一化后的期望平方和
        double expectedSquareSum = Arrays.stream(expected)
                .map(x -> x / expectedSum)
                .map(x -> x * x)
                .sum();

        // 1 - 期望平方和（1是归一化后实际值的和）
        // 这个值越小说明越均衡（期望越均匀，平方和越小）
        return Math.abs(1.0 - expectedSquareSum);
    }

    /**
     * 计算两个向量的余弦相似度
     */
    public static double calculateVectorCosineSimilarity(double[] actualVector, double[] expectedVector) {
        if (actualVector.length != expectedVector.length) {
            throw new IllegalArgumentException("Vector dimensions must match");
        }

        // 计算点积
        double dotProduct = 0;
        for (int i = 0; i < actualVector.length; i++) {
            dotProduct += actualVector[i] * expectedVector[i];
        }

        // 计算向量模长
        double norm1 = calculateVectorNorm(actualVector);
        double norm2 = calculateVectorNorm(expectedVector);

        // 避免除零
        if (norm1 == 0 || norm2 == 0) {
            return 0;
        }

        // 余弦相似度 = 点积 / (模长1 × 模长2)
        return dotProduct / (norm1 * norm2);
    }

    /**
     * 计算多维向量的余弦相似度
     * 支持两种聚合方式：按行计算和按列计算
     *
     * @param actualMatrix 实际值矩阵 [行数][列数]
     * @param expectedMatrix 期望值矩阵 [行数][列数]
     * @param aggregateByRow true=按行聚合(每行是一个向量), false=按列聚合(每列是一个向量)
     * @param useWeightedAverage true=使用加权平均, false=使用简单平均
     * @return 聚合后的余弦相似度
     */
    public static double calculateMultiDimensionalCosineSimilarity(
            double[][] actualMatrix,
            double[][] expectedMatrix,
            boolean aggregateByRow,
            boolean useWeightedAverage) {

        if (actualMatrix.length != expectedMatrix.length) {
            throw new IllegalArgumentException("Matrix row dimensions must match");
        }

        if (actualMatrix.length == 0 || actualMatrix[0].length != expectedMatrix[0].length) {
            throw new IllegalArgumentException("Matrix column dimensions must match");
        }

        int rows = actualMatrix.length;
        int cols = actualMatrix[0].length;

        double totalSimilarity = 0.0;
        double totalWeight = 0.0;
        int validVectors = 0;
        double minSimilarity = 1.0; // 记录最小相似度

        if (aggregateByRow) {
            // 按行聚合：每行作为一个向量
            for (int i = 0; i < rows; i++) {
                double[] actualRow = actualMatrix[i];
                double[] expectedRow = expectedMatrix[i];

                double similarity = calculateVectorCosineSimilarity(actualRow, expectedRow);
                minSimilarity = Math.min(minSimilarity, similarity);

                if (useWeightedAverage) {
                    // 使用该行的期望值总和作为权重
                    double weight = Arrays.stream(expectedRow).sum();
                    if (weight > 0) {
                        totalSimilarity += similarity * weight;
                        totalWeight += weight;
                        validVectors++;
                    }
                } else {
                    // 简单平均
                    totalSimilarity += similarity;
                    validVectors++;
                }
            }
        } else {
            // 按列聚合：每列作为一个向量
            for (int j = 0; j < cols; j++) {
                double[] actualCol = new double[rows];
                double[] expectedCol = new double[rows];

                for (int i = 0; i < rows; i++) {
                    actualCol[i] = actualMatrix[i][j];
                    expectedCol[i] = expectedMatrix[i][j];
                }

                double similarity = calculateVectorCosineSimilarity(actualCol, expectedCol);
                minSimilarity = Math.min(minSimilarity, similarity);

                if (useWeightedAverage) {
                    // 使用该列的期望值总和作为权重
                    double weight = Arrays.stream(expectedCol).sum();
                    if (weight > 0) {
                        totalSimilarity += similarity * weight;
                        totalWeight += weight;
                        validVectors++;
                    }
                } else {
                    // 简单平均
                    totalSimilarity += similarity;
                    validVectors++;
                }
            }
        }

        if (validVectors == 0) {
            return 1.0; // 没有有效向量时返回完全相似
        }

        double averageSimilarity = useWeightedAverage ?
            (totalWeight > 0 ? totalSimilarity / totalWeight : 1.0) :
            totalSimilarity / validVectors;

        // 使用最小值惩罚：如果有一个维度偏差很大，对整体影响很大
        // 使用几何平均的思想：average * min^penalty_factor
        double penaltyFactor = 0.5; // 惩罚因子，可以调整
        return averageSimilarity * Math.pow(minSimilarity, penaltyFactor);
    }

    /**
     * 计算多维向量的余弦相似度 - 使用最小值策略
     * 当有一个值偏差很严重时，对整体影响很大
     *
     * @param actualMatrix 实际值矩阵 [行数][列数]
     * @param expectedMatrix 期望值矩阵 [行数][列数]
     * @param aggregateByRow true=按行聚合(每行是一个向量), false=按列聚合(每列是一个向量)
     * @param minValueWeight 最小值的权重 (0-1之间，越大越严格)
     * @return 聚合后的余弦相似度
     */
    public static double calculateMultiDimensionalCosineSimilarityWithMinPenalty(
            double[][] actualMatrix,
            double[][] expectedMatrix,
            boolean aggregateByRow,
            double minValueWeight) {

        if (actualMatrix.length != expectedMatrix.length) {
            throw new IllegalArgumentException("Matrix row dimensions must match");
        }

        if (actualMatrix.length == 0 || actualMatrix[0].length != expectedMatrix[0].length) {
            throw new IllegalArgumentException("Matrix column dimensions must match");
        }

        int rows = actualMatrix.length;
        int cols = actualMatrix[0].length;

        double totalSimilarity = 0.0;
        double totalWeight = 0.0;
        int validVectors = 0;
        double minSimilarity = 1.0; // 记录最小相似度

        if (aggregateByRow) {
            // 按行聚合：每行作为一个向量
            for (int i = 0; i < rows; i++) {
                double[] actualRow = actualMatrix[i];
                double[] expectedRow = expectedMatrix[i];

                double similarity = calculateVectorCosineSimilarity(actualRow, expectedRow);
                minSimilarity = Math.min(minSimilarity, similarity);

                // 使用该行的期望值总和作为权重
                double weight = Arrays.stream(expectedRow).sum();
                if (weight > 0) {
                    totalSimilarity += similarity * weight;
                    totalWeight += weight;
                    validVectors++;
                }
            }
        } else {
            // 按列聚合：每列作为一个向量
            for (int j = 0; j < cols; j++) {
                double[] actualCol = new double[rows];
                double[] expectedCol = new double[rows];

                for (int i = 0; i < rows; i++) {
                    actualCol[i] = actualMatrix[i][j];
                    expectedCol[i] = expectedMatrix[i][j];
                }

                double similarity = calculateVectorCosineSimilarity(actualCol, expectedCol);
                minSimilarity = Math.min(minSimilarity, similarity);

                // 使用该列的期望值总和作为权重
                double weight = Arrays.stream(expectedCol).sum();
                if (weight > 0) {
                    totalSimilarity += similarity * weight;
                    totalWeight += weight;
                    validVectors++;
                }
            }
        }

        if (validVectors == 0) {
            return 1.0; // 没有有效向量时返回完全相似
        }

        double averageSimilarity = totalWeight > 0 ? totalSimilarity / totalWeight : 1.0;

        // 使用最小值惩罚策略：
        // result = (1 - minValueWeight) * average + minValueWeight * min
        // 当minValueWeight接近1时，最小值影响很大
        // 当minValueWeight接近0时，退化为普通加权平均
        return (1 - minValueWeight) * averageSimilarity + minValueWeight * minSimilarity;
    }

    public static double calculateVectorSimilarityDeviation(
            List<Designer> designers,
            Function<Designer, Double> actualValueExtractor,
            Function<Designer, Double> expectedValueExtractor) {

        // 提取实际值 abc
        double[] actualValues = designers.stream()
                .mapToDouble(actualValueExtractor::apply)
                .toArray();

        // 提取期望值 a1 b1 c1
        double[] expectedValues = designers.stream()
                .mapToDouble(expectedValueExtractor::apply)
                .toArray();

        return calculateActualExpectedMatchDeviation(actualValues, expectedValues);
    }

    /**
     * 计算向量的模长（欧几里得范数）
     */
    public static double calculateVectorNorm(double[] vector) {
        double sumOfSquares = 0;
        for (double value : vector) {
            sumOfSquares += value * value;
        }
        return Math.sqrt(sumOfSquares);
    }


    public static double calculateActualExpectedMatchDeviation(double[] actualValues, double[] expectedValues) {
        // 将abc分别平分（归一化）
        double actualSum = Arrays.stream(actualValues).sum();
        double expectedSum = Arrays.stream(expectedValues).sum();

        if (actualSum == 0 || expectedSum == 0) {
            return 0;
        }

        // 计算归一化比例向量
        double[] actualRatios = Arrays.stream(actualValues)
                .map(value -> value / actualSum)
                .toArray();

        double[] expectedRatios = Arrays.stream(expectedValues)
                .map(value -> value / expectedSum)
                .toArray();

        // 计算 a/a1, b/b1, c/c1 的点积
        double dotProduct = 0;
        for (int i = 0; i < actualValues.length; i++) {
            if (expectedRatios[i] != 0) {
                dotProduct += (actualRatios[i] / expectedRatios[i]) * expectedRatios[i];
            }
        }

        // 计算期望向量的平方和
        double expectedSquareSum = Arrays.stream(expectedRatios)
                .map(value -> value * value)
                .sum();

        // 点积 - 期望平方和
        return Math.abs(dotProduct - expectedSquareSum);
    }
}

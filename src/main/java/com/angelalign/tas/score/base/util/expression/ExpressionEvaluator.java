package com.angelalign.tas.score.base.util.expression;

import com.angelalign.tas.util.Jackson;

import java.util.*;
import java.util.stream.Collectors;

public class ExpressionEvaluator {
    private static final Set<String> OPERATORS = new HashSet<>(Arrays.stream(LogicType.values()).map(LogicType::name).toList());
    private static final String PAREN_OPEN = "(";
    private static final String PAREN_CLOSE = ")";

    public static boolean evaluate(String expr, List<String> input) {
        Condition parse = parse(expr);
        return evaluate(parse, input);
    }

    public static boolean evaluate(Condition condition, List<String> input) {
        Map<String, Boolean> stringBooleanMap = buildContext(input);
        return condition.test(stringBooleanMap);
    }

    private static Condition parse(String expr) {
        Queue<String> tokens = tokenize(expr);
        return parseExpression(tokens);
    }

    public static String toConditionJson(String expr) {
        Condition parse = parse(expr);
        return Jackson.toJsonString(parse);
    }

    private static Queue<String> tokenize(String expr) {
        return Arrays.stream(expr
                        .replace("(", " ( ")
                        .replace(")", " ) ")
                        .split("\\s+"))
                .filter(t -> !t.isEmpty())
                .collect(Collectors.toCollection(LinkedList::new));
    }

    public static String toExpression(Condition condition) {
        return condition.toExpression();
    }

    private static Condition parseExpression(Queue<String> tokens) {
        List<Condition> conditions = new ArrayList<>();
        String currentLogic = "OR"; // 默认逻辑

        while (!tokens.isEmpty()) {
            String token = tokens.poll();

            if (PAREN_OPEN.equals(token)) {
                conditions.add(parseExpression(tokens));
            } else if (PAREN_CLOSE.equals(token)) {
                break;
            } else if (OPERATORS.contains(token)) {
                currentLogic = token;
            } else {
                conditions.add(new AtomicCondition(token));

                if (!tokens.isEmpty() && OPERATORS.contains(tokens.peek())) {
                    currentLogic = tokens.poll();
                }
            }
        }

        return conditions.size() == 1 ?
                conditions.get(0) :
                new CompositeCondition(
                        LogicType.valueOf(currentLogic),
                        conditions
                );
    }

    private static Map<String, Boolean> buildContext(List<String> input) {
        return input
                .stream()
                .collect(Collectors.toMap(
                        String::toUpperCase,
                        v -> true,
                        (a, b) -> a,
                        HashMap::new
                ));
    }
}
package com.angelalign.tas.score.base.constraint;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.enums.TaskTypeCodeEnum;
import com.angelalign.tas.score.base.util.DesignerCapacityConvertor;
import com.angelalign.tas.score.base.util.math.MathematicalFunctionUtil;
import com.angelalign.tas.score.base.util.ProblemUtil;
import org.optaplanner.core.api.score.buildin.bendablelong.BendableLongScore;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintFactory;

import java.util.Map;

public class CapacityConstraint {
    @Deprecated
    protected Constraint minimizeTaskTypeCountByCapacityRatioPenalizeConstraint_v5(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .filter(designer -> designer.getCapacity() != null && designer.getCapacityDailyTotalQuota() > 0)
                .penalizeLong(
                        BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1),
                        designer -> {
                            // 当前设计分到的工单类型和数量 只保留筛选和排牙
                            Map<String, Long> designerTaskTypeCountMap = DesignerCapacityConvertor.getDesignerTaskTypeCountMap(designer);


                            // 当前设计师预期的工单类型和数量
                            Map<String, Double> designerExpectedTaskTypeQuota = ProblemUtil.calculateExpectedTaskTypeCount(designer);
                            designerExpectedTaskTypeQuota.entrySet()
                                    .removeIf(entry -> !entry.getKey().equals(TaskTypeCodeEnum.executeDesign3dDentition.name())
                                                       && !entry.getKey().equals(TaskTypeCodeEnum.executeDesignSelection.name()));

                            // 计算每种工单类型的实际数量与预期数量的偏差，并应用边际递增惩罚
                            long totalPenalty = 0L;

                            // 遍历所有预期的工单类型
                            for (Map.Entry<String, Double> expectedEntry : designerExpectedTaskTypeQuota.entrySet()) {
                                String taskTypeCode = expectedEntry.getKey();
                                double expectedCount = expectedEntry.getValue();

                                // 获取实际分配的数量（通过TaskType的code匹配）
                                Long designerTaskTypeCount = designerTaskTypeCountMap.getOrDefault(taskTypeCode, 0L);
                                double deviation = designerTaskTypeCount - expectedCount;
                                if (deviation > 1) {
                                    deviation = Math.pow(deviation, 1.5);
                                }
                                // 计算偏差（实际数量与预期数量的差值）
                                double deviationPow = Math.pow(Math.abs(deviation) * 10, 1.5);

                                // 使用边际递增惩罚，偏差越大惩罚越重
                                long penalty = MathematicalFunctionUtil.marginalPenaltyByCapacity(deviationPow, designer);
                                totalPenalty += penalty;
                            }
                            return totalPenalty;
                        }
                )
                .asConstraint("minimizeTaskTypeCountByCapacityRatioPenalizeConstraint_v4");
    }
}

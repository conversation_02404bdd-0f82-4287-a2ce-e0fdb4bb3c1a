package com.angelalign.tas.score.base.cache;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.score.base.util.DesignerCapacityConvertor;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;

import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * Designer额度计算缓存
 * 提供高性能的Designer额度计算缓存服务
 */
@Slf4j
public class DesignerQuotaCache {
    private static final Boolean enableCache = Boolean.FALSE;

    // 缓存Designer的总消耗额度
    private static final Cache<String, Double> totalConsumedQuotaCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)  // 10分钟过期
            .maximumSize(5000)
            .recordStats()
            .build();

    // 缓存Designer的指定任务额度
    private static final Cache<String, Double> appointedTaskQuotaCache = Caffeine.newBuilder()
            .expireAfterWrite(10, TimeUnit.MINUTES)  // 10分钟过期
            .maximumSize(5000)
            .recordStats()
            .build();

    /**
     * 获取缓存的总消耗额度，如果缓存未命中则计算并缓存
     */
    public static Double getTotalConsumedQuota(Designer designer) {
        if (!enableCache) {
            double consumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);
            double assignedQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer);
            return consumedQuota + assignedQuota;
        }
        String cacheKey = getCacheKey(designer);

        return totalConsumedQuotaCache.get(cacheKey, key -> {
            // 缓存未命中时计算
            double consumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);
            double assignedQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer);
            return consumedQuota + assignedQuota;
        });
    }

    /**
     * 获取缓存的指定任务额度，如果缓存未命中则计算并缓存
     */
    public static Double getAppointedTaskQuota(Designer designer) {
        if (!enableCache) {
            return designer.getTasks()
                    .stream()
                    .filter(task -> CollectionUtils.isNotEmpty(task.getPreferredDesigner()))
                    .filter(task -> task.getPreferredDesigner().stream()
                            .anyMatch(d -> d.getCode().equals(designer.getCode())))
                    .mapToDouble(task -> DesignerCapacityConvertor.covertAssignedTaskQuota(designer, Set.of(task)))
                    .sum();
        }
        String cacheKey = getCacheKey(designer);

        return appointedTaskQuotaCache.get(cacheKey, key -> {
            // 缓存未命中时计算
            return designer.getTasks()
                    .stream()
                    .filter(task -> CollectionUtils.isNotEmpty(task.getPreferredDesigner()))
                    .filter(task -> task.getPreferredDesigner().stream()
                            .anyMatch(d -> d.getCode().equals(designer.getCode())))
                    .mapToDouble(task -> DesignerCapacityConvertor.covertAssignedTaskQuota(designer, Set.of(task)))
                    .sum();
        });
    }

    /**
     * 手动更新Designer的缓存
     */
    public static void updateCache(Designer designer) {
        String cacheKey = getCacheKey(designer);

        // 重新计算并更新缓存
        double totalQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer)
                            + DesignerCapacityConvertor.covertAssignedTaskQuota(designer);
        totalConsumedQuotaCache.put(cacheKey, totalQuota);

        double appointedQuota = designer.getTasks()
                .stream()
                .filter(task -> CollectionUtils.isNotEmpty(task.getPreferredDesigner()))
                .filter(task -> task.getPreferredDesigner().stream()
                        .anyMatch(d -> d.getCode().equals(designer.getCode())))
                .mapToDouble(task -> DesignerCapacityConvertor.covertAssignedTaskQuota(designer, Set.of(task)))
                .sum();
        appointedTaskQuotaCache.put(cacheKey, appointedQuota);

//        log.info("Updated cache for designer {}: totalQuota={}, appointedQuota={}",
//                designer.getCode(), totalQuota, appointedQuota);
    }

    /**
     * 清除Designer的缓存
     */
    public static void invalidateCache(Designer designer) {
        String cacheKey = getCacheKey(designer);
        totalConsumedQuotaCache.invalidate(cacheKey);
        appointedTaskQuotaCache.invalidate(cacheKey);

        log.info("Invalidated cache for designer: {}", designer.getCode());
    }

    /**
     * 清除所有缓存
     */
    public static void invalidateAllCache() {
        totalConsumedQuotaCache.invalidateAll();
        appointedTaskQuotaCache.invalidateAll();
        log.info("Invalidated all designer quota cache");
    }

    /**
     * 获取缓存统计信息
     */
    public static String getCacheStats() {
        return String.format("TotalQuotaCache: hits=%d, misses=%d, size=%d; AppointedQuotaCache: hits=%d, misses=%d, size=%d",
                totalConsumedQuotaCache.stats().hitCount(),
                totalConsumedQuotaCache.stats().missCount(),
                totalConsumedQuotaCache.estimatedSize(),
                appointedTaskQuotaCache.stats().hitCount(),
                appointedTaskQuotaCache.stats().missCount(),
                appointedTaskQuotaCache.estimatedSize());
    }

    /**
     * 生成缓存键
     */
    private static String getCacheKey(Designer designer) {
        // 使用designer的code和problem的id作为缓存键
        return designer.getCode() + "_" + designer.getProblem().getId() + "_" + System.identityHashCode(designer.getTasks());
    }
}

package com.angelalign.tas.score.base.cache;

import com.angelalign.tas.domain.parser.ConsumedQuotaTaskAttributeParser;
import com.angelalign.tas.domain.parser.DesignerAttributeParser;
import com.angelalign.tas.domain.parser.TaskAttributeParser;
import com.angelalign.tas.solver.phase.listener.PhaseQueueManager;
import com.angelalign.tas.score.base.util.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 缓存管理服务
 * 提供缓存的生命周期管理和手动更新功能
 */
@Service
@Slf4j
public class CacheManagementService {

    /**
     * 在求解结束时清理缓存
     */
    public void cleanupCacheAfterSolving() {
        log.info("Cleaning up cache after solving...");
        DesignerQuotaCache.invalidateAllCache();
        CaseTaskUtil.reset();
        DesignerCapacityConvertor.invalidateAll();
        DesignerPreferTaskOrSkillUtil.invalidateAll();
        ProblemUtil.invalidateAll();
        TaskAttributeParser.invalidateAll();
        ConsumedQuotaTaskAttributeParser.invalidateAll();
        DesignerAttributeParser.invalidateAll();
        PhaseQueueManager.invalidateAll();
        PreferTaskExpectedRatioUtil.invalidateAll();
        log.info("Cache cleanup completed");
    }
}

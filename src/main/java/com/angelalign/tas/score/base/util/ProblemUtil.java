package com.angelalign.tas.score.base.util;

import com.angelalign.tas.domain.assignment.*;
import com.angelalign.tas.domain.assignment.enums.CaseAllocateTypeEnum;
import com.angelalign.tas.domain.parser.DesignerAttributeParser;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class ProblemUtil {
    protected static final Cache<Long, Double> problemDesignerTotalQuota = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(5000).build();
    protected static final Cache<Long, Double> problemTaskTotalQuota = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(5000).build();
    protected static final Cache<String, Double> problemDesignerOverTotalQuota = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(5000).build();
    protected static final Cache<String, Map<String, Long>> problemTaskTypeTotalQuota = Caffeine.newBuilder()
            .expireAfterWrite(1, TimeUnit.HOURS)
            .maximumSize(5000).build();

    public static void invalidateAll() {
        problemDesignerTotalQuota.invalidateAll();
        problemTaskTotalQuota.invalidateAll();
        problemTaskTypeTotalQuota.invalidateAll();
        problemDesignerOverTotalQuota.invalidateAll();
    }

    private static Double getDesignerTotalDailyQuota(Problem problem) {
        return problemDesignerTotalQuota.get(problem.getId()
                , k -> problem.getDesigners()
                        .stream()
                        .filter(designer -> designer.onDuty)
                        .mapToDouble(Designer::getCapacityDailyTotalQuota)
                        .sum());
    }

    private static Double getMaxDesignerTotalDailyQuota(Problem problem) {
        return problemDesignerTotalQuota.get(999 + problem.getId()
                , k -> problem.getDesigners()
                        .stream()
                        .filter(designer -> designer.onDuty)
                        .map(Designer::getCapacityDailyTotalQuota)
                        .max(Comparator.naturalOrder())
                        .orElse(1.0)
        );
    }

    public static Map<String, Long> getAllTaskTypeQuota(Problem problem, String key, Predicate<Task> predicate, Predicate<DesignerConsumedQuotaTask> designerConsumedQuotaTaskPredicate) {
        return problemTaskTypeTotalQuota.get(problem.getId() + key
                , k -> {
                    // 本次分配的工单类型的数量
                    Map<String, Long> taskTypeCountMap = problem.getTasks()
                            .stream()
                            .filter(predicate)
                            .map(Task::getTaskType)
                            .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));
                    // 已经分配过的工单类型的数量
                    Map<String, Long> designerConsumedTaskTypeCountMap = problem.getDesigners()
                            .stream()
                            .filter(designer -> designer.onDuty)
                            .map(Designer::getDesignerConsumedQuotaTasks)
                            .flatMap(Set::stream)
                            .filter(designerConsumedQuotaTaskPredicate)
                            .map(DesignerConsumedQuotaTask::getTaskType)
                            .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));
                    // 合并一下
                    return Stream.concat(
                            taskTypeCountMap.entrySet().stream(),
                            designerConsumedTaskTypeCountMap.entrySet().stream()
                    ).collect(Collectors.toMap(
                            Map.Entry::getKey,
                            Map.Entry::getValue,
                            Long::sum
                    ));
                });
    }

    public static Map<String, Long> getAllTaskTypeQuota(Problem problem) {
        return getAllTaskTypeQuota(problem, "getAllTaskTypeQuota", t -> true, t -> true);
    }

    public static long getValidDesignerCount(Problem problem) {
        return problem.getDesigners().stream()
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(d -> d.getCapacity() != null)
                .count();
    }

    public static Double getAllDesignerTotalOverAmount(Problem problem) {
        return problemDesignerOverTotalQuota.get(problem.getId() + "getAllDesignerTotalOverAmount", key -> problem.getDesigners().stream()
                .filter(d -> d.getCapacity() != null)
                .mapToDouble(d -> Math.max(0, d.getCapacity().getOverAllocationLimit()))
                .sum());
    }

    public static Double getTaskTotalQuotaWithHistory(Problem problem, Capacity capacity) {
        return problemTaskTotalQuota.get(problem.getId()
                , k -> {
                    // 计算本次分配的工单总额度
                    double currentTaskQuota = problem.getTasks()
                            .stream()
                            .mapToDouble(task -> {
                                if (CaseAllocateTypeEnum.MIXED_TASK_QUANTITY.equals(capacity.getCaseAllocateType())) {
                                    return task.getBaseDurationInCount();
                                } else return task.getBaseDurationInMinutes();
                            })
                            .sum();
                    // 计算历史分配的工单总额度
                    double historyTaskQuota = problem.getDesigners().stream()
                            .mapToDouble(DesignerCapacityConvertor::covertConsumedTaskQuota)
                            .sum();
                    return currentTaskQuota + historyTaskQuota;
                });
    }

    public static Double calculateDesignerExpectedQuota(Designer designer) {
        Double totalTaskQuota = getTaskTotalQuotaWithHistory(designer.getProblem(), designer.getCapacity());
        return totalTaskQuota * getDesignerCapacityRatio(designer);
    }

    /**
     * 设计师的产能比例
     *
     * @param designer 设计师
     * @return 设计师的日额度比上全部的设计师的日额度总和
     */
    public static double getDesignerCapacityRatio(Designer designer) {
        return designer.getCapacityDailyTotalQuota() / getDesignerTotalDailyQuota(designer.getProblem());
    }

    /**
     * 设计师的产能比例
     *
     * @param designer 设计师
     * @return 设计师的日额度比上全部的设计师的日额度总和
     */
    public static double getDesignerCapacityRatio_V2(Designer designer) {
        return designer.getCapacityDailyTotalQuota() / getMaxDesignerTotalDailyQuota(designer.getProblem());
    }

    public static Double getDesignerPreferSkillTotalQuota(Problem problem) {
        return problem.getDesigners()
                .stream()
                .mapToDouble(d -> {
                    List<String> preferSkillExpression = DesignerAttributeParser.getPreferSkillExpression(d);
                    Set<Task> preferSkillTask = DesignerPreferTaskOrSkillUtil.getDesignerPreferSkillTask(preferSkillExpression, d.getTasks());
                    return DesignerCapacityConvertor.covertAssignedTaskQuota(d, preferSkillTask) + DesignerCapacityConvertor.covertConsumedTaskQuota(d);
                })
                .sum();
    }

    public static Set<Task> getDesignerPreferSkillOrTaskTotalQuota(Problem problem) {
        return problem.getTasks()
                .stream()
                .filter(task -> {
                    return DesignerPreferTaskOrSkillUtil.isDesignerPreferTask(task, task.getDesigner())
                           || DesignerPreferTaskOrSkillUtil.isDesignerPreferSkillTask(task, task.getDesigner());
                }).collect(Collectors.toSet());
    }

    public static Double getDesignerPreferSkillExpectedQuota(Designer designer) {
        return getDesignerPreferSkillTotalQuota(designer.getProblem()) * getDesignerCapacityRatio(designer);
    }

    public static Double getDesignerPreferSkillOrTaskExpectedQuota(Designer designer) {
        Set<Task> designerPreferSkillOrTaskTotalQuota = getDesignerPreferSkillOrTaskTotalQuota(designer.getProblem());
        Double covertAssignedTaskQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, designerPreferSkillOrTaskTotalQuota);
        return getDesignerPreferSkillOrTaskExpectedQuota(designer, covertAssignedTaskQuota);
    }

    public static Double getDesignerPreferSkillOrTaskExpectedQuota(Designer designer, Double totalQuota) {
        return totalQuota * getDesignerCapacityRatio(designer);
    }

    public static Map<String, Double> calculateExpectedTaskTypeCount(Designer designer) {
        double designerCapacityRatio = getDesignerCapacityRatio(designer);
        Map<String, Long> allTaskTypeQuota = getAllTaskTypeQuota(designer.getProblem());
        return allTaskTypeQuota.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue() * designerCapacityRatio
                ));
    }

    public static Map<String, Double> calculateExpectedPreferTaskTaskTaskTypeCount(Designer designer) {
        double designerCapacityRatio = getDesignerCapacityRatio(designer);
        // 本次分配的工单类型的数量
        Map<String, Long> taskTypeCountMap = designer.getProblem().getTasks()
                .stream()
                .filter(task -> DesignerPreferTaskOrSkillUtil.isDesignerPreferTask(task, task.getDesigner()))
                .map(Task::getTaskType)
                .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));
        // 已经分配过的工单类型的数量
        Map<String, Long> designerConsumedTaskTypeCountMap = designer.getProblem().getDesigners()
                .stream()
                .filter(designer1 -> designer1.onDuty)
                .map(Designer::getDesignerConsumedQuotaTasks)
                .flatMap(Set::stream)
                .filter(DesignerPreferTaskOrSkillUtil::isDesignerConsumedPreferTask)
                .map(DesignerConsumedQuotaTask::getTaskType)
                .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));
        // 合并一下
        return Stream.concat(
                        taskTypeCountMap.entrySet().stream(),
                        designerConsumedTaskTypeCountMap.entrySet().stream()
                ).collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        Long::sum
                )).entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue() * designerCapacityRatio
                ));
    }

    public static Map<String, Double> calculateExpectedPreferSkillTaskTaskTypeCount(Designer designer) {
        double designerCapacityRatio = getDesignerCapacityRatio(designer);

        // 本次分配的工单类型的数量
        Map<String, Long> taskTypeCountMap = designer.getProblem().getTasks()
                .stream()
                .filter(task -> DesignerPreferTaskOrSkillUtil.isDesignerPreferSkillTask(task, task.getDesigner()))
                .map(Task::getTaskType)
                .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));
        // 已经分配过的工单类型的数量
        Map<String, Long> designerConsumedTaskTypeCountMap = designer.getProblem().getDesigners()
                .stream()
                .filter(designer1 -> designer1.onDuty)
                .map(Designer::getDesignerConsumedQuotaTasks)
                .flatMap(Set::stream)
                .filter(DesignerPreferTaskOrSkillUtil::isDesignerConsumedPreferSkillTask)
                .map(DesignerConsumedQuotaTask::getTaskType)
                .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));
        // 合并一下
        return Stream.concat(
                        taskTypeCountMap.entrySet().stream(),
                        designerConsumedTaskTypeCountMap.entrySet().stream()
                ).collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        Long::sum
                )).entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue() * designerCapacityRatio
                ));
    }

    public static Map<String, Long> getAverageExpectedTaskTypeQuota(Problem problem) {
        Map<String, Long> allTaskTypeQuota = getAllTaskTypeQuota(problem);
        return allTaskTypeQuota.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue() * (1 / getValidDesignerCount(problem))
                ));
    }
}
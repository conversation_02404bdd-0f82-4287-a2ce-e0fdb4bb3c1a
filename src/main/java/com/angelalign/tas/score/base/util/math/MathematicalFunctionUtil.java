package com.angelalign.tas.score.base.util.math;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.score.base.util.ProblemUtil;
import com.angelalign.tas.util.DecimalUtils;

public class MathematicalFunctionUtil {

    // ==================== 边际递减奖励 ====================

    /**
     * 根据设计师产能的边际递减奖励
     * 使用对数函数实现边际递减效应，前几个单位奖励高，后续递减
     *
     * @param x        输入值（如工单数量、额度等）
     * @param designer 设计师对象
     * @return 边际递减奖励分数
     */
    public static long marginalRewardWithPowByCapacity(double x, Designer designer) {
        if (x <= 0) {
            return 0;
        }
        double marginalReward = Math.pow(Math.log(1 + x) * 100, 2);
        marginalReward *= Math.max(ProblemUtil.getDesignerCapacityRatio(designer), 0.005);
        return DecimalUtils.preciseRound(marginalReward);
    }

    public static double marginalRewardByCapacity(double x, Designer designer, int factor) {
        if (x <= 0) {
            return 0;
        }
        double marginalReward = Math.log(1 + x) * factor;
        marginalReward *= ProblemUtil.getDesignerCapacityRatio(designer);
        return marginalReward;
    }

    /**
     * 标准边际递减奖励
     * 使用对数函数实现边际递减效应
     *
     * @param x 输入值
     * @return 边际递减奖励分数
     */
    public static int marginalReward(double x) {
        if (x <= 0) {
            return 0;
        }
        double marginalReward = Math.pow(Math.log(1 + x) * 15, 2);
        return DecimalUtils.preciseRound(marginalReward);
    }

    // ==================== 边际递增惩罚 ====================

    /**
     * 根据设计师产能的边际递增惩罚
     * 使用指数函数实现边际递增效应，偏差越大惩罚越重
     *
     * @param x        输入值（如偏差量、超额量等）
     * @param designer 设计师对象
     * @return 边际递增惩罚分数
     */
    public static int marginalPenaltyByCapacity(double x, Designer designer) {
        if (x <= 0) {
            return 0;
        }
        // 采用1+x的形式，避免x越惩罚越小，但限制指数避免过度增长
        double marginalPenalty = Math.pow(1 + x, 2);

        marginalPenalty *= Math.max(ProblemUtil.getDesignerCapacityRatio(designer), 0.05);
        return DecimalUtils.preciseRound(marginalPenalty);
    }

    /**
     * 标准边际递增惩罚
     * 使用指数函数实现边际递增效应
     *
     * @param x 输入值
     * @return 边际递增惩罚分数
     */
    public static int marginalPenalty(double x) {
        if (x <= 0) {
            return 0;
        }
        // 限制输入值避免过度增长
        double limitedX = Math.min(x, 1000);
        double marginalPenalty = Math.pow(limitedX, 2);
        // 应用安全限制
        return DecimalUtils.preciseRound(marginalPenalty);
    }

    // ==================== 高级边际效应方法 ====================

    /**
     * 可配置的边际递减奖励
     *
     * @param x          输入值
     * @param designer   设计师对象
     * @param multiplier 乘数因子
     * @param maxReward  最大奖励上限
     * @return 边际递减奖励分数
     */
    public static int configurableMarginalRewardByCapacity(double x, Designer designer, double multiplier, int power, int maxReward) {
        if (x <= 0) {
            return 0;
        }

        double marginalReward = Math.pow(Math.log(1 + x) * multiplier, power);

        marginalReward *= Math.max(ProblemUtil.getDesignerCapacityRatio(designer), 0.05);
        int result = DecimalUtils.preciseRound(marginalReward);
        return Math.min(result, maxReward);
    }

    /**
     * 平衡型边际效应：小值奖励，大值惩罚
     * 适用于需要适度分配的场景
     *
     * @param x         输入值
     * @param designer  设计师对象
     * @param threshold 阈值（小于阈值奖励，大于阈值惩罚）
     * @return 平衡型分数（正数为奖励，负数为惩罚）
     */
    public static long balancedMarginalEffect(double x, Designer designer, double threshold) {
        if (x <= 0) {
            return 0;
        }

        if (x <= threshold) {
            // 小于阈值：边际递减奖励
            return marginalRewardWithPowByCapacity(x, designer);
        } else {
            // 大于阈值：边际递增惩罚
            double excess = x - threshold;
            return -marginalPenaltyByCapacity(excess, designer);
        }
    }

    /**
     * 指数函数实现的高敏感度边际递增函数
     *
     * @param x 输入值，范围 [0,1]
     * @param c 曲率参数（c > 0），c 越大则高区间越敏感
     * @return 输出值，范围 [0,1]
     */
    public static double expIncremental(double x, double c) {
        if (x <= 0) return 0;
        if (x >= 1) return 1;
        return (Math.exp(c * x) - 1) / (Math.exp(c) - 1);
    }

    /**
     * 根据产能比例缩放奖励，具有边际递减效应
     *
     * @param x          输入值必须是是0-1
     * @param designer   设计师
     * @param multiplier 需要放大的比例
     * @return 输出值是（0-1）* 放大的比例
     */
    public static double calculateScaledRewardByCapacity(double x, Designer designer, int multiplier) {
        if (x <= 0) return 0;
        if (x >= 1) return 1;
        double capacityRatio = designer != null ?
                ProblemUtil.getDesignerCapacityRatio_V2(designer) : 1;
        return Math.log(1 + x * (Math.E - 1)) / Math.log(Math.E) * capacityRatio * multiplier;
    }

    public static void main(String[] args) {

        System.out.println("0.1 " + (calculateScaledRewardByCapacity(0.1, null, 100) - calculateScaledRewardByCapacity(0.0, null, 100)));
        System.out.println("0.2 " + (calculateScaledRewardByCapacity(0.2, null, 100) - calculateScaledRewardByCapacity(0.1, null, 100)));
        System.out.println("0.3 " + (calculateScaledRewardByCapacity(0.3, null, 100) - calculateScaledRewardByCapacity(0.2, null, 100)));
        System.out.println("0.4 " + (calculateScaledRewardByCapacity(0.4, null, 100) - calculateScaledRewardByCapacity(0.3, null, 100)));
        System.out.println("0.5 " + (calculateScaledRewardByCapacity(0.5, null, 100) - calculateScaledRewardByCapacity(0.4, null, 100)));
        System.out.println("0.6 " + (calculateScaledRewardByCapacity(0.6, null, 100) - calculateScaledRewardByCapacity(0.5, null, 100)));
        System.out.println("0.7 " + (calculateScaledRewardByCapacity(0.7, null, 100) - calculateScaledRewardByCapacity(0.6, null, 100)));
        System.out.println("0.8 " + (calculateScaledRewardByCapacity(0.8, null, 100) - calculateScaledRewardByCapacity(0.7, null, 100)));
        System.out.println("0.9 " + (calculateScaledRewardByCapacity(0.9, null, 100) - calculateScaledRewardByCapacity(0.8, null, 100)));
        System.out.println("1 " + (calculateScaledRewardByCapacity(1, null, 100) - calculateScaledRewardByCapacity(0.9, null, 100)));


        System.out.println("1.1 " + (calculateScaledRewardByCapacity(1.1, null, 100) - calculateScaledRewardByCapacity(1.0, null, 100)));
        System.out.println("1.2 " + (calculateScaledRewardByCapacity(1.2, null, 100) - calculateScaledRewardByCapacity(1.1, null, 100)));
        System.out.println("1.3 " + (calculateScaledRewardByCapacity(1.3, null, 100) - calculateScaledRewardByCapacity(1.2, null, 100)));
        System.out.println("1.4 " + (calculateScaledRewardByCapacity(1.4, null, 100) - calculateScaledRewardByCapacity(1.3, null, 100)));
        System.out.println("1.5 " + (calculateScaledRewardByCapacity(1.5, null, 100) - calculateScaledRewardByCapacity(1.4, null, 100)));
        System.out.println("1.6 " + (calculateScaledRewardByCapacity(1.6, null, 100) - calculateScaledRewardByCapacity(1.5, null, 100)));
        System.out.println("1.7 " + (calculateScaledRewardByCapacity(1.7, null, 100) - calculateScaledRewardByCapacity(1.6, null, 100)));

    }
}
package com.angelalign.tas.score.base.constraint;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.score.base.util.*;
import com.angelalign.tas.score.base.util.math.VectorDeviationUtil;
import com.angelalign.tas.util.DecimalUtils;
import org.optaplanner.core.api.score.buildin.bendablelong.BendableLongScore;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintCollectors;
import org.optaplanner.core.api.score.stream.ConstraintFactory;

import java.util.*;

public class BalanceConstraint {
    /**
     * 基于向量点积的统一均衡约束
     * 同时优化工时均衡和任务类型均衡
     */
    public static Constraint taskTypeAndWorkTimeBalanceVectorConstraint(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .groupBy(
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getTotalConsumedQuota())),
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getCapacityDailyTotalQuota())),
                        ConstraintCollectors.toList()
                )
                .rewardLong(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1),
                        (totalActualQuota, dailyTotalQuota, designers) -> {
                            if (dailyTotalQuota <= 0 || designers.size() <= 1) {
                                return 0;
                            }
                            // 计算工时向量余弦相似度
                            double workTimeDeviation = calculateWorkTimeVectorCosineVectorSimilarity(designers);

                            // 计算任务类型余弦相似度
                            double taskTypeDeviation = TaskTypeVectorSimilarityUtil.calculateTaskTypeCosineVectorSimilarity(designers);

                            // 综合偏差
                            double workTimeWeight = 0.4;
                            double taskTypeWeight = 0.6;

                            double totalDeviation = workTimeWeight * workTimeDeviation + taskTypeWeight * taskTypeDeviation;

                            return Math.round(totalDeviation * 100000);
                        })
                .asConstraint("vectorDotProductBalanceConstraint");
    }

    public static Constraint taskTypeAndWorkTimeBalanceVectorConstraint_WithPreferTask(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .groupBy(
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getTotalConsumedQuota())),
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getCapacityDailyTotalQuota())),
                        ConstraintCollectors.toList()
                )
                .rewardLong(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1),
                        (totalActualQuota, dailyTotalQuota, designers) -> {
                            if (dailyTotalQuota <= 0 || designers.size() <= 1) {
                                return 0;
                            }
                            // 计算工时向量余弦相似度
                            double workTimeDeviation = calculateWorkTimeVectorCosineVectorSimilarity(designers);

                            // 计算任务类型余弦相似度
                            double taskTypeDeviation = TaskTypeVectorSimilarityUtil.calculateTaskTypeCosineVectorSimilarity(designers);

                            double preferTaskWorkTimeVectorCosineVectorSimilarity = calculatePreferTaskWorkTimeVectorCosineVectorSimilarity(designers);

                            // 综合偏差
                            double workTimeWeight = 0.4;
                            double taskTypeWeight = 0.4;
                            double preferTaskWorkTimeWeight = 0.2;

                            double totalDeviation = workTimeWeight * workTimeDeviation + taskTypeWeight * taskTypeDeviation + preferTaskWorkTimeWeight * preferTaskWorkTimeVectorCosineVectorSimilarity;

                            return Math.round(totalDeviation * 100000000);
                        })
                .asConstraint("vectorDotProductBalanceConstraint");
    }

    private static double calculatePreferTaskWorkTimeVectorCosineVectorSimilarity(List<Designer> designers) {
        Set<Task> designerPreferSkillOrTaskTotalQuota = ProblemUtil.getDesignerPreferSkillOrTaskTotalQuota(designers.get(0).getProblem());
        Double covertAssignedTaskQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designers.get(0), designerPreferSkillOrTaskTotalQuota);
        int n = designers.size();
        double[] actualVector = new double[n];
        double[] expectedVector = new double[n];

        // 构建实际工时向量和期望工时向量
        for (int i = 0; i < n; i++) {
            Designer designer = designers.get(i);

            // 实际工时
            Set<Task> preferTask = DesignerPreferTaskOrSkillUtil.getPreferTask(designer);
            Set<Task> preferSkillTask = DesignerPreferTaskOrSkillUtil.getPreferSkillTask(designer);
            preferTask.addAll(preferSkillTask);
            actualVector[i] = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferTask);
            // 预期的工时
            expectedVector[i] = ProblemUtil.getDesignerPreferSkillOrTaskExpectedQuota(designers.get(0), covertAssignedTaskQuota);
        }

        // 计算余弦相似度
        return VectorDeviationUtil.calculateVectorCosineSimilarity(actualVector, expectedVector);
    }

    /**
     * 计算工时分配的向量点积偏差
     */
    private static double calculateWorkTimeVectorCosineVectorSimilarity(List<Designer> designers) {
        int n = designers.size();
        double[] actualVector = new double[n];
        double[] expectedVector = new double[n];

        // 构建实际工时向量和期望工时向量
        for (int i = 0; i < n; i++) {
            Designer designer = designers.get(i);

            // 实际工时
            actualVector[i] = designer.getTotalConsumedQuota();
            // 预期的工时
            expectedVector[i] = ProblemUtil.calculateDesignerExpectedQuota(designer);
        }

        // 计算余弦相似度
        return VectorDeviationUtil.calculateVectorCosineSimilarity(actualVector, expectedVector);

    }
}

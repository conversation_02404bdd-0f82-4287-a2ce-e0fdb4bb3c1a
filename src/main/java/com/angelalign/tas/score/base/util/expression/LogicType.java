package com.angelalign.tas.score.base.util.expression;

import java.util.List;
import java.util.Map;

public enum LogicType {
    AND {
        @Override
        public boolean evaluate(List<Condition> conditions, Map<String, Boolean> context) {
            return conditions.stream().allMatch(c -> c.test(context));
        }
    },
    OR {
        @Override
        public boolean evaluate(List<Condition> conditions, Map<String, Boolean> context) {
            return conditions.stream().anyMatch(c -> c.test(context));
        }
    };

    public abstract boolean evaluate(List<Condition> conditions, Map<String, Boolean> context);
}

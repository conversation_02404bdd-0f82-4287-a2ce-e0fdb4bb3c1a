package com.angelalign.tas.score.base.util;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import org.apache.commons.collections4.CollectionUtils;

import java.util.function.BiPredicate;

public class ConstraintUtil {
    public static boolean designerIsNotTaskPreferDesigner(Task task,Designer designer) {
        return designerIsNotTaskPreferDesigner().test(task, designer);
    }

    public static BiPredicate<Task, Designer> designerIsNotTaskPreferDesigner() {
        return (task, designer) -> {
            if (CollectionUtils.isEmpty(task.getPreferredDesigner())) return true;
            return task.getPreferredDesigner().stream().noneMatch(d -> d.getCode().equals(designer.getCode()));
        };
    }
}

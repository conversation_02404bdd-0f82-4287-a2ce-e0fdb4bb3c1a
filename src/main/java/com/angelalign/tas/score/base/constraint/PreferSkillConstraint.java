package com.angelalign.tas.score.base.constraint;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.parser.DesignerAttributeParser;
import com.angelalign.tas.solver.phase.listener.PhaseQueueManager;
import com.angelalign.tas.score.base.util.*;
import com.angelalign.tas.score.base.util.math.KernelFunctionUtil;
import com.angelalign.tas.score.base.util.math.MathematicalFunctionUtil;
import com.angelalign.tas.score.base.util.math.VectorDeviationUtil;
import com.angelalign.tas.solver.phase.enums.PhaseName;
import com.angelalign.tas.util.DecimalUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.optaplanner.core.api.score.buildin.bendablelong.BendableLongScore;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintCollectors;
import org.optaplanner.core.api.score.stream.ConstraintFactory;
import org.optaplanner.core.api.score.stream.Joiners;

import java.util.List;
import java.util.Set;
import java.util.function.Function;

@Slf4j
public class PreferSkillConstraint {

    public static Constraint designerPreferSkillByCapacityRewardConstraint_WithOutTaskType(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Designer.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(designer -> CollectionUtils.isNotEmpty(DesignerAttributeParser.getPreferSkillExpression(designer)))
                .rewardLong(BendableLongScore.ofSoft(constraintDefinition.getHardLevelSize()
                                , constraintDefinition.getSoftLevelSize()
                                , constraintDefinition.getConstraintLevel(), 1),
                        designer -> {
                            if (designer.getOverAllocationLimit() <= 0) return 0;
                            Set<Task> preferTask = DesignerPreferTaskOrSkillUtil.getPreferTask(designer);

                            // 偏好技能的工单
                            Set<Task> preferSkillTask = DesignerPreferTaskOrSkillUtil.getPreferSkillTask(designer);

                            // 当前设计师历史分配的工单（不可动）
                            Double designerConsumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);

                            // 处理被认为是偏好技能的额度
                            Set<Task> beThoughtAsPreferSkillTask = designer.getAppointedTaskWithOutHistory();
                            beThoughtAsPreferSkillTask.addAll(preferSkillTask);
                            beThoughtAsPreferSkillTask.addAll(preferTask);

                            double totalQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, beThoughtAsPreferSkillTask)
                                                + designerConsumedQuota;

                            double ratio = totalQuota / Math.max(designer.getOverAllocationLimit(), 1);
                            return DecimalUtils.preciseRound(MathematicalFunctionUtil.marginalRewardByCapacity(ratio, designer, 1000000));
                        })
                .asConstraint("designerPreferSkillByCapacityRewardConstraint_ForPreferSkillTaskTypeSimilarity");
    }

    public static Constraint designerPreferSkillByCapacityRewardConstraint_ForOneScore(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Task.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .join(Designer.class, Joiners.equal(Task::getDesigner, d -> d))
                .filter((task, designer) -> CollectionUtils.isNotEmpty(DesignerAttributeParser.getPreferSkillExpression(designer)))
                .rewardLong(BendableLongScore.ofSoft(constraintDefinition.getHardLevelSize()
                                , constraintDefinition.getSoftLevelSize()
                                , constraintDefinition.getConstraintLevel(), 1),
                        (task, designer) -> {
                            if (designer.getOverAllocationLimit() <= 0) return 0;
                            boolean designerPreferSkillTask = DesignerPreferTaskOrSkillUtil.isDesignerPreferSkillTask(task, designer);
                            return designerPreferSkillTask ? 1 : 0;
                        })
                .asConstraint("designerPreferSkillByCapacityRewardConstraint_ForPreferSkillTaskTypeSimilarity");
    }

    public static Constraint designerPreferSkillByCapacityRewardConstraint_ForPreferSkillTaskTypeSimilarity(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Designer.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(designer -> CollectionUtils.isNotEmpty(DesignerAttributeParser.getPreferSkillExpression(designer)))
                .rewardLong(BendableLongScore.ofSoft(constraintDefinition.getHardLevelSize()
                                , constraintDefinition.getSoftLevelSize()
                                , constraintDefinition.getConstraintLevel(), 1),
                        designer -> {
                            if (designer.getOverAllocationLimit() <= 0) return 0;
                            double taskTypeSimilarity = TaskTypeVectorSimilarityUtil.calculatePreferSkillTaskTypeCosineVectorSimilarity(List.of(designer));
                            Set<Task> preferTask = DesignerPreferTaskOrSkillUtil.getPreferTask(designer);

                            // 偏好技能的工单
                            Set<Task> preferSkillTask = DesignerPreferTaskOrSkillUtil.getPreferSkillTask(designer);

                            // 当前设计师历史分配的工单（不可动）
                            Double designerConsumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);

                            // 处理被认为是偏好技能的额度
                            Set<Task> beThoughtAsPreferSkillTask = designer.getAppointedTaskWithOutHistory();
                            beThoughtAsPreferSkillTask.addAll(preferSkillTask);
                            beThoughtAsPreferSkillTask.addAll(preferTask);

                            double totalQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, beThoughtAsPreferSkillTask)
                                                + designerConsumedQuota;
                            double ratio = totalQuota / Math.max(designer.getOverAllocationLimit(), 1);
                            double reward = MathematicalFunctionUtil.calculateScaledRewardByCapacity(ratio, designer, 1);
                            return DecimalUtils.preciseRound((reward * 0.8 + taskTypeSimilarity * 0.2) * 100000000);
                        })
                .asConstraint("designerPreferSkillByCapacityRewardConstraint_ForPreferSkillTaskTypeSimilarity");
    }

    public static Constraint designerPreferSkillByCapacityRewardConstraint_ForExpectedPhase(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .filter(designer -> PhaseQueueManager.getCurrentPhase().equals(PhaseName.preferTaskSimulatedAnnealingPhase))
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(designer -> CollectionUtils.isNotEmpty(designer.getTasks()))
                .filter(designer -> CollectionUtils.isNotEmpty(DesignerAttributeParser.getPreferSkillExpression(designer)))
                // 声明对所有可能影响appointedTaskQuota的Task属性的依赖
                .ifExists(Task.class, Joiners.equal(Function.identity(), Task::getDesigner))
                .impactLong(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1)
                        , designer -> {
                            Set<Task> preferTask = DesignerPreferTaskOrSkillUtil.getPreferTask(designer);
                            // 当前设计师本次分配的偏好技能工单额度
                            Double designerPreferTaskActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferTask);
                            Set<Task> preferSkillTask = DesignerPreferTaskOrSkillUtil.getPreferSkillTask(designer);
                            // 当前设计师本次分配的偏好技能工单额度
                            Double designerPreferSkillActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferSkillTask);
                            // 当前设计师历史分配的工单（不可动）
                            Double designerConsumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);
                            return MathematicalFunctionUtil.marginalRewardWithPowByCapacity(designer.getAppointedTaskQuota()
                                                                                            + designerPreferSkillActualQuota
                                                                                            + designerPreferTaskActualQuota
                                                                                            + designerConsumedQuota, designer) - 20000;
                        })
                .asConstraint("fillDesignerPreferSkillRewardConstraint_v2");
    }

    /**
     * 计算个体设计师的工作负载比例
     *
     * @param designer 设计师
     * @return 工作负载比例
     */
    private static double calculatePreferSkillQuotaRatio(Designer designer) {
        Set<Task> preferTask = DesignerPreferTaskOrSkillUtil.getPreferTask(designer);
        Set<Task> preferSkillTask = DesignerPreferTaskOrSkillUtil.getPreferSkillTask(designer);

        // 当前设计师本次分配的偏好工单额度
        Double designerPreferTaskActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferTask);
        // 当前设计师本次分配的偏好技能工单额度
        Double designerPreferSkillActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferSkillTask);
        // 当前设计师历史分配的工单（不可动）
        Double designerConsumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);

        double totalQuota = designer.getAppointedTaskQuota()
                            + designerPreferSkillActualQuota
                            + designerPreferTaskActualQuota
                            + designerConsumedQuota;

        return totalQuota / Math.max(designer.getCapacityDailyTotalQuota(), 1.0);
    }

    public static Constraint designerPreferSkillByCapacityRewardConstraint_WithExpectedRatio(
            ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Designer.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(designer -> CollectionUtils.isNotEmpty(designer.getTasks()))
                .filter(designer -> CollectionUtils.isNotEmpty(DesignerAttributeParser.getPreferSkillExpression(designer)))
                .groupBy(
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getTotalConsumedQuota())),
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getCapacityDailyTotalQuota())),
                        ConstraintCollectors.toList()
                )
                .rewardLong(BendableLongScore.ofSoft(constraintDefinition.getHardLevelSize(), constraintDefinition.getSoftLevelSize(), constraintDefinition.getConstraintLevel(), 1),
                        (totalActualQuota, dailyTotalQuota, designers) -> {
                            if (dailyTotalQuota <= 0 || designers.size() <= 1) {
                                return 0;
                            }

                            // 计算工单类型相似度
                            double taskTypeSimilarity = TaskTypeVectorSimilarityUtil.calculateTaskTypeCosineVectorSimilarity(designers);

                            // 构建实际比例向量和预期比例向量
                            double[] actualRatios = new double[designers.size()];
                            double[] expectedRatios = new double[designers.size()];

                            for (int i = 0; i < designers.size(); i++) {
                                Designer designer = designers.get(i);

                                // 计算个体实际比例
                                actualRatios[i] = calculatePreferSkillQuotaRatio(designer);

                                // 计算个体预期比例
                                expectedRatios[i] = PreferTaskExpectedRatioUtil.getDesignerExpectedPreferSkillRatio(designer);
                            }

                            // 计算向量余弦相似度
                            double preferSkillRatioSimilarity = VectorDeviationUtil.calculateVectorCosineSimilarity(
                                    actualRatios, expectedRatios);

                            // 综合评分：工单类型相似度 + 优先技能比例相似度
                            double combinedScore = 0.3 * taskTypeSimilarity + 0.7 * preferSkillRatioSimilarity;
                            return DecimalUtils.preciseRound(combinedScore * 10000);
                        })
                .asConstraint("designerPreferSkillByCapacityRewardConstraint_WithExpectedRatio");
    }


    /**
     * 优先技能工单分配约束  - 使用核函数 + 公平性约束 +可配置参数的核函数版本
     * 评分公式：
     * score = w1 * task_type_similarity + w2 * prefer_fairness + w3 * K(individual_ratio)
     * 其中：
     * - task_type_similarity: 工单类型相似度（有明确期望值）
     * - prefer_fairness: 优先工单分配公平性（相对公平性，无需期望值）
     * - K(individual_ratio): 个体比例合理性（核函数）
     *
     * @param taskTypeWeight 工单类型相似度权重
     * @param kernelWeight   核函数权重
     * @param kernelGamma    核函数敏感度 (建议: 1.0-5.0，越大越严格)
     */
    public static Constraint designerPreferSkillByCapacityRewardConstraint_v4(
            ConstraintDefinition constraintDefinition,
            double taskTypeWeight, double kernelWeight,
            double kernelGamma) {
        return constraintDefinition.getConstraintFactory().forEach(Designer.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(designer -> CollectionUtils.isNotEmpty(designer.getTasks()))
                .filter(designer -> CollectionUtils.isNotEmpty(DesignerAttributeParser.getPreferSkillExpression(designer)))
                .groupBy(
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getTotalConsumedQuota())),
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getCapacityDailyTotalQuota())),
                        ConstraintCollectors.toList()
                )
                .rewardLong(BendableLongScore.ofSoft(constraintDefinition.getHardLevelSize(), constraintDefinition.getSoftLevelSize(), constraintDefinition.getConstraintLevel(), 1),
                        (totalActualQuota, dailyTotalQuota, designers) -> {
                            if (dailyTotalQuota <= 0 || designers.size() <= 1) {
                                return 0;
                            }

                            // 计算工单类型相似度
                            double taskTypeSimilarity = TaskTypeVectorSimilarityUtil.calculateTaskTypeCosineVectorSimilarity(designers);

                            // 计算个体得分并聚合
                            return designers.stream()
                                    .mapToLong(designer -> {
                                        // 计算个体比例
                                        double individualRatio = calculatePreferSkillQuotaRatio(designer);

                                        // 核函数：个体合理性评分
                                        double kernelScore = KernelFunctionUtil.calculateRBFKernel(individualRatio, 1.0, kernelGamma);

                                        double combinedScore = taskTypeWeight * taskTypeSimilarity
                                                               + kernelWeight * kernelScore;

                                        // 边际奖励计算
                                        double marginalReward = Math.log(1 + combinedScore * 100) * 1000;
                                        marginalReward *= ProblemUtil.getDesignerCapacityRatio(designer);

                                        return DecimalUtils.preciseRound(marginalReward);
                                    })
                                    .sum();
                        })
                .asConstraint("designerPreferSkillByCapacityRewardConstraint_v4");
    }
}

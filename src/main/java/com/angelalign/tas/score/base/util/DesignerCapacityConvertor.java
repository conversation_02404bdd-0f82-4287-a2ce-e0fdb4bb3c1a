package com.angelalign.tas.score.base.util;

import com.angelalign.tas.domain.assignment.*;
import com.angelalign.tas.domain.assignment.enums.CaseAllocateTypeEnum;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class DesignerCapacityConvertor {
    protected static final Cache<String, Double> designerConsumedTaskQuota = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();
    protected static final Cache<String, Map<String, Long>> designerTaskTypeQuota = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(100000).build();

    public static void invalidateAll() {
        designerConsumedTaskQuota.invalidateAll();
        designerTaskTypeQuota.invalidateAll();
    }

    public static Map<String, Long> getTaskConsumedTTypeCount(Designer designer) {
        return designerTaskTypeQuota.get(designer.getCode() + designer.getProblem().getId(), key -> {
            // 之前已经分过的工单类型和数量
            return designer.getDesignerConsumedQuotaTasks().stream()
                    .map(DesignerConsumedQuotaTask::getTaskType)
                    .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));
        });
    }

    public static Map<String, Long> getDesignerTaskTypeCountMap(Designer designer) {
        // 本次分配的工单类型和数量
        Map<String, Long> taskTypeCountMap = designer.getTasks().stream()
                .map(Task::getTaskType)
                .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));
        Map<String, Long> consumedTaskTypeCountMap = getTaskConsumedTTypeCount(designer);
        // 合并一下
        return Stream.concat(
                taskTypeCountMap.entrySet().stream(),
                consumedTaskTypeCountMap.entrySet().stream()
        ).collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                Long::sum
        ));
    }

    /**
     * 转换已经占用额度
     *
     * @param designer 设计师
     * @return 返回已经占用产能的额度
     */
    public static Double covertConsumedTaskQuota(Designer designer) {
        if (designer.getCapacity() == null) return 0.0;
        return designerConsumedTaskQuota.get(designer.getCode() + designer.getProblem().getId(), key -> {
            Capacity capacity = designer.getCapacity();
            return designer.getDesignerConsumedQuotaTasks()
                    .stream()
                    .mapToDouble(task -> {
                        if (CaseAllocateTypeEnum.MIXED_TASK_QUANTITY.equals(capacity.getCaseAllocateType())) {
                            return task.getConsumedCountQuota();
                        } else return task.getConsumedMinuteQuota();
                    }).sum();
        });
    }

    /**
     * 转换已经占用额度
     *
     * @param designer 设计师
     * @return 返回已经占用产能的额度
     */
    public static Double covertConsumedTaskQuota(Designer designer, List<DesignerConsumedQuotaTask> consumedQuotaTaskList) {
        if (designer.getCapacity() == null) return 0.0;
        Capacity capacity = designer.getCapacity();
        return consumedQuotaTaskList
                .stream()
                .mapToDouble(task -> {
                    if (CaseAllocateTypeEnum.MIXED_TASK_QUANTITY.equals(capacity.getCaseAllocateType())) {
                        return task.getConsumedCountQuota();
                    } else return task.getConsumedMinuteQuota();
                })
                .sum();

    }

    /**
     * 转换本次计算已经占用额度
     *
     * @param designer 设计师
     * @return 返回已经占用产能的额度
     */
    public static Double covertAssignedTaskQuota(Designer designer) {
        return covertAssignedTaskQuota(designer, designer.getTasks());
    }

    /**
     * 转换某个工单在分配某个设计师的前提下额度
     *
     * @param designer 设计师
     * @return 返回已经占用产能的额度
     */
    public static Double covertAssignedTaskQuota(Designer designer, Set<Task> taskList) {
        if (designer == null) return 0.0;
        if (designer.getCapacity() == null) return 0.0;
        Capacity capacity = designer.getCapacity();
        return taskList.stream()
                .mapToDouble(task -> {
                    if (CaseAllocateTypeEnum.MIXED_TASK_QUANTITY.equals(capacity.getCaseAllocateType())) {
                        return task.getBaseDurationInCount();
                    } else return Integer.valueOf(task.getBaseDurationInMinutes()).doubleValue();
                })
                .sum();

    }
}

package com.angelalign.tas.score.base.util.expression;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.List;
import java.util.Map;

@Data
public class CompositeCondition implements Condition {
    @JsonProperty("logicType")
    private LogicType logic;
    @JsonProperty("conditions")
    private List<Condition> conditions;

    @JsonCreator
    CompositeCondition(@JsonProperty("logicType") LogicType logic
            , @JsonProperty("conditions") List<Condition> conditions) {
        this.logic = logic;
        this.conditions = conditions;
    }

    @Override
    public String toExpression() {
        StringBuilder sb = new StringBuilder();
        sb.append('('); // 外层括号确保运算顺序

        for (int i = 0; i < conditions.size(); i++) {
            if (i > 0) {
                sb.append(" ").append(logic.name()).append(" "); // 逻辑运算符
            }
            sb.append(conditions.get(i).toExpression()); // 递归子条件
        }

        sb.append(')');
        return sb.toString();
    }

    @Override
    public boolean test(Map<String, Boolean> context) {
        return logic.evaluate(conditions, context);
    }
}


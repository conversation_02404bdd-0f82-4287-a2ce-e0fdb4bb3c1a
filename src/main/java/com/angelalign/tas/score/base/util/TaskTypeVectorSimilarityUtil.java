package com.angelalign.tas.score.base.util;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.DesignerConsumedQuotaTask;
import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.assignment.TaskType;
import com.angelalign.tas.domain.assignment.enums.TaskTypeCodeEnum;
import com.angelalign.tas.score.base.util.math.VectorDeviationUtil;
import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class TaskTypeVectorSimilarityUtil {

    // 支持的任务类型过滤器
    private static final List<String> SUPPORTED_TASK_TYPES = List.of(
            TaskTypeCodeEnum.executeDesign3dDentition.name(),
            TaskTypeCodeEnum.executeDesignSelection.name()
    );

    // 默认的最小值权重
    private static final double DEFAULT_MIN_VALUE_WEIGHT = 0.7;

    /**
     * 计算任务类型分配的二维向量相似度
     * 将所有工单类型的实际值和期望值构建成二维向量，直接计算相似度
     */
    public static double calculateTaskTypeCosineVectorSimilarity(List<Designer> designers) {
        return calculateTaskTypeVectorSimilarity(
                designers,
                DesignerCapacityConvertor::getDesignerTaskTypeCountMap,
                ProblemUtil::calculateExpectedTaskTypeCount
        );
    }

    /**
     * 计算偏好技能任务类型的二维向量相似度
     */
    public static double calculatePreferSkillTaskTypeCosineVectorSimilarity(List<Designer> designers) {
        if (CollectionUtils.isEmpty(designers)) {
            return 1.0;
        }

        // 只计算一次全局的优先技能工单统计
        Map<String, Long> globalPreferSkillTaskTypeCount = calculateGlobalPreferSkillTaskTypeCount(designers.get(0).getProblem());

        return calculateTaskTypeVectorSimilarity(
                designers,
                DesignerPreferTaskOrSkillUtil::getDesignerPreferSkillTaskTypeCountMap,
                designer -> calculateExpectedTaskTypeCountFromGlobal(designer, globalPreferSkillTaskTypeCount)
        );
    }

    /**
     * 计算偏好工单类型的二维向量相似度
     */
    public static double calculatePreferTaskTaskTypeCosineVectorSimilarity(List<Designer> designers) {
        if (CollectionUtils.isEmpty(designers)) {
            return 1.0;
        }
        Map<String, Long> globalPreferSkillTaskTypeCount = calculateGlobalPreferTaskTaskTypeCount(designers.get(0).getProblem());
        return calculateTaskTypeVectorSimilarity(
                designers,
                DesignerPreferTaskOrSkillUtil::getDesignerPreferTaskTaskTypeCountMap,
                designer -> calculateExpectedTaskTypeCountFromGlobal(designer, globalPreferSkillTaskTypeCount)
        );
    }

    /**
     * 通用的任务类型向量相似度计算方法
     *
     * @param designers            设计师列表
     * @param actualMapExtractor   实际值提取器
     * @param expectedMapExtractor 期望值提取器
     * @return 向量相似度
     */
    private static double calculateTaskTypeVectorSimilarity(
            List<Designer> designers,
            Function<Designer, Map<String, Long>> actualMapExtractor,
            Function<Designer, Map<String, Double>> expectedMapExtractor) {

        if (designers.isEmpty()) {
            return 1.0;
        }

        // 获取过滤后的任务类型
        Map<String, Long> allTaskTypeQuota = getFilteredTaskTypeQuota(designers.get(0));
        if (allTaskTypeQuota.isEmpty()) {
            return 1.0;
        }

        // 构建矩阵数据
        MatrixData matrixData = buildMatrixData(designers, allTaskTypeQuota, actualMapExtractor, expectedMapExtractor);

        // 计算向量相似度
        return VectorDeviationUtil.calculateMultiDimensionalCosineSimilarityWithMinPenalty(
                matrixData.actualMatrix,
                matrixData.expectedMatrix,
                false,
                TaskTypeVectorSimilarityUtil.DEFAULT_MIN_VALUE_WEIGHT
        );
    }

    /**
     * 获取过滤后的任务类型配额
     */
    private static Map<String, Long> getFilteredTaskTypeQuota(Designer designer) {
        return ProblemUtil.getAllTaskTypeQuota(designer.getProblem())
                .entrySet()
                .stream()
                .filter(entry -> SUPPORTED_TASK_TYPES.contains(entry.getKey()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue
                ));
    }

    /**
     * 计算全局的优先工单类型统计
     */
    private static Map<String, Long> calculateGlobalPreferTaskTaskTypeCount(Problem problem) {
        // 本次分配的优先技能工单
        Map<String, Long> currentTaskTypeCount = problem.getTasks()
                .stream()
                .filter(task -> task.getDesigner() != null)
                .filter(task -> DesignerPreferTaskOrSkillUtil.isDesignerPreferTask(task, task.getDesigner())) // 对其分配的设计师来说是优先技能
                .map(Task::getTaskType)
                .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));

        // 历史优先技能工单
        Map<String, Long> historyTaskTypeCount = problem.getDesigners()
                .stream()
                .filter(designer -> designer.onDuty)
                .map(DesignerPreferTaskOrSkillUtil::getDesignerConsumedPreferTask)
                .flatMap(List::stream)
                .map(DesignerConsumedQuotaTask::getTaskType)
                .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));

        // 合并当前和历史数据
        return Stream.concat(
                currentTaskTypeCount.entrySet().stream(),
                historyTaskTypeCount.entrySet().stream()
        ).collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                Long::sum
        ));
    }

    /**
     * 计算全局的优先技能工单类型统计
     */
    private static Map<String, Long> calculateGlobalPreferSkillTaskTypeCount(Problem problem) {
        // 本次分配的优先技能工单
        Map<String, Long> currentTaskTypeCount = problem.getTasks()
                .stream()
                .filter(task -> task.getDesigner() != null) // 已经被分配的工单
                .filter(task -> DesignerPreferTaskOrSkillUtil.isDesignerPreferSkillTask(task, task.getDesigner())) // 对其分配的设计师来说是优先技能
                .map(Task::getTaskType)
                .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));

        // 历史优先技能工单
        Map<String, Long> historyTaskTypeCount = problem.getDesigners()
                .stream()
                .filter(designer -> designer.onDuty)
                .map(DesignerPreferTaskOrSkillUtil::getDesignerConsumedPreferSkillTask)
                .flatMap(List::stream)
                .map(DesignerConsumedQuotaTask::getTaskType)
                .collect(Collectors.groupingBy(TaskType::getCode, Collectors.counting()));

        // 合并当前和历史数据
        return Stream.concat(
                currentTaskTypeCount.entrySet().stream(),
                historyTaskTypeCount.entrySet().stream()
        ).collect(Collectors.toMap(
                Map.Entry::getKey,
                Map.Entry::getValue,
                Long::sum
        ));
    }

    /**
     * 基于全局统计计算单个设计师的预期优先技能工单类型数量
     */
    private static Map<String, Double> calculateExpectedTaskTypeCountFromGlobal(
            Designer designer, Map<String, Long> globalTaskTypeCount) {
        double designerCapacityRatio = ProblemUtil.getDesignerCapacityRatio(designer);
        return globalTaskTypeCount.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue() * designerCapacityRatio
                ));
    }

    /**
     * 构建矩阵数据
     */
    private static MatrixData buildMatrixData(
            List<Designer> designers,
            Map<String, Long> allTaskTypeQuota,
            Function<Designer, Map<String, Long>> actualMapExtractor,
            Function<Designer, Map<String, Double>> expectedMapExtractor) {

        List<String> taskTypeList = new ArrayList<>(allTaskTypeQuota.keySet());
        int designerCount = designers.size();
        int taskTypeCount = taskTypeList.size();

        // 构建二维矩阵：[设计师][任务类型]
        double[][] actualMatrix = new double[designerCount][taskTypeCount];
        double[][] expectedMatrix = new double[designerCount][taskTypeCount];

        // 预先计算所有设计师的实际值和期望值Map，避免重复计算
        List<Map<String, Long>> actualMaps = designers.stream()
                .map(actualMapExtractor)
                .toList();

        List<Map<String, Double>> expectedMaps = designers.stream()
                .map(expectedMapExtractor)
                .toList();

        // 填充矩阵
        for (int i = 0; i < designerCount; i++) {
            for (int j = 0; j < taskTypeCount; j++) {
                String taskType = taskTypeList.get(j);
                actualMatrix[i][j] = actualMaps.get(i).getOrDefault(taskType, 0L).doubleValue();
                expectedMatrix[i][j] = expectedMaps.get(i).getOrDefault(taskType, 0.0);
            }
        }

        return new MatrixData(actualMatrix, expectedMatrix);
    }

    /**
     * 矩阵数据封装类
     */
    private record MatrixData(double[][] actualMatrix, double[][] expectedMatrix) {
    }

    /**
     * 计算任务类型分配的多维度综合均衡指数
     * 通过计算每个工单类型的标准化偏差，综合评估分配均衡程度
     * 返回值范围：0-1，越接近1表示分配越均衡
     */
    public static double calculateTaskTypeBalanceIndex(List<Designer> designers) {
        return calculateTaskTypeBalanceIndex(
                designers,
                DesignerCapacityConvertor::getDesignerTaskTypeCountMap,
                ProblemUtil::calculateExpectedTaskTypeCount
        );
    }

    /**
     * 通用的任务类型均衡指数计算方法
     *
     * @param designers            设计师列表
     * @param actualMapExtractor   实际值提取器
     * @param expectedMapExtractor 期望值提取器
     * @return 均衡指数 (0-1范围，越接近1越均衡)
     */
    private static double calculateTaskTypeBalanceIndex(
            List<Designer> designers,
            Function<Designer, Map<String, Long>> actualMapExtractor,
            Function<Designer, Map<String, Double>> expectedMapExtractor) {

        if (designers.isEmpty()) {
            return 1.0;
        }

        // 获取过滤后的任务类型
        Map<String, Long> allTaskTypeQuota = getFilteredTaskTypeQuota(designers.get(0));
        if (allTaskTypeQuota.isEmpty()) {
            return 1.0;
        }

        // 收集所有工单类型
        Set<String> taskTypes = allTaskTypeQuota.keySet();

        // 计算每个工单类型的综合偏差
        double totalSquaredDeviation = 0.0;
        int validTaskTypeCount = 0;

        for (String taskType : taskTypes) {
            // 收集该工单类型在所有设计师中的实际值和期望值
            List<Double> actualValues = new ArrayList<>();
            List<Double> expectedValues = new ArrayList<>();

            for (Designer designer : designers) {
                Map<String, Long> actualMap = actualMapExtractor.apply(designer);
                Map<String, Double> expectedMap = expectedMapExtractor.apply(designer);

                double actualValue = actualMap.getOrDefault(taskType, 0L).doubleValue();
                double expectedValue = expectedMap.getOrDefault(taskType, 0.0);

                actualValues.add(actualValue);
                expectedValues.add(expectedValue);
            }

            // 计算该工单类型的标准化偏差
            double taskTypeDeviation = calculateTaskTypeNormalizedDeviation(actualValues, expectedValues);

            if (!Double.isNaN(taskTypeDeviation) && !Double.isInfinite(taskTypeDeviation)) {
                totalSquaredDeviation += taskTypeDeviation * taskTypeDeviation;
                validTaskTypeCount++;
            }
        }

        if (validTaskTypeCount == 0) {
            return 1.0;
        }

        // 计算综合偏差 (均方根)
        double comprehensiveDeviation = Math.sqrt(totalSquaredDeviation / validTaskTypeCount);

        // 转换为0-1范围的均衡指数
        // 使用负指数函数确保平滑过渡：balance = e^(-k*deviation)
        // k值可以调整敏感度，这里使用2.0作为默认值
        double sensitivityFactor = 2.0;
        return Math.exp(-sensitivityFactor * comprehensiveDeviation);
    }

    /**
     * 计算单个工单类型的标准化偏差
     *
     * @param actualValues   实际值列表
     * @param expectedValues 期望值列表
     * @return 标准化偏差
     */
    private static double calculateTaskTypeNormalizedDeviation(List<Double> actualValues, List<Double> expectedValues) {
        if (actualValues.size() != expectedValues.size()) {
            return 0.0;
        }

        double totalSquaredError = 0.0;
        double totalExpected = 0.0;
        int validPairCount = 0;

        for (int i = 0; i < actualValues.size(); i++) {
            double actual = actualValues.get(i);
            double expected = expectedValues.get(i);

            if (expected > 0) {
                // 计算相对偏差：|actual - expected| / expected
                double relativeDeviation = Math.abs(actual - expected) / expected;
                totalSquaredError += relativeDeviation * relativeDeviation;
                validPairCount++;
            }

            totalExpected += expected;
        }

        if (validPairCount == 0) {
            return 0.0;
        }

        // 返回均方根相对偏差
        return Math.sqrt(totalSquaredError / validPairCount);
    }
}

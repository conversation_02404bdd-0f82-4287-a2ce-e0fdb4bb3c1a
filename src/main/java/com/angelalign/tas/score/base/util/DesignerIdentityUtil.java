package com.angelalign.tas.score.base.util;

import com.angelalign.tas.domain.assignment.Designer;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.domain.assignment.enums.DesignerRankEnum;

public class DesignerIdentityUtil {
    public static boolean enableFakerDesigner() {
        return true;
//       return false;
    }

    public static boolean isFakerDesigner(Task task) {
        return DesignerRankEnum.FAKER.equals(task.getDesigner().getRank());
    }

    public static boolean isFakerDesigner(Designer designer) {
        return DesignerRankEnum.FAKER.equals(designer.getRank());
    }

    public static boolean isAvailableDesigner(Task task) {
        if (enableFakerDesigner()) {
            return task.getDesigner() != null && !isFakerDesigner(task);
        }
        return task.getDesigner() != null;
    }

    public static boolean isAvailableDesigner(Designer designer) {
        if (enableFakerDesigner()) {
            return designer != null && !isFakerDesigner(designer);
        }
        return designer != null;
    }

    public static boolean isAvailableDesigner(Task task, Designer designer) {
        return isAvailableDesigner(task) && isAvailableDesigner(designer);
    }
}

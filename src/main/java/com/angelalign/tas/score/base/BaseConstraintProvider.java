package com.angelalign.tas.score.base;

import com.angelalign.tas.score.base.constraint.BaseConstraintPool;
import lombok.extern.slf4j.Slf4j;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintFactory;
import org.optaplanner.core.api.score.stream.ConstraintProvider;

import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

@Slf4j
public abstract class BaseConstraintProvider extends BaseConstraintPool implements ConstraintProvider {

    @Override
    public Constraint[] defineConstraints(ConstraintFactory constraintFactory) {
        List<Constraint> hardConstraints = getHardConstraints(constraintFactory);
        List<Constraint> softConstraints = getSoftConstraints(constraintFactory);
        return Stream.concat(hardConstraints.stream(), softConstraints.stream())
                .filter(Objects::nonNull)
                .toArray(Constraint[]::new);
    }

    public abstract List<Constraint> getHardConstraints(ConstraintFactory constraintFactory);


    public abstract List<Constraint> getSoftConstraints(ConstraintFactory constraintFactory);

}

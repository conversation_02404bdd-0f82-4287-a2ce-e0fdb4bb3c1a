package com.angelalign.tas.score.base.constraint;

import com.angelalign.tas.score.base.util.*;
import com.angelalign.tas.solver.phase.listener.PhaseQueueManager;
import com.angelalign.tas.domain.assignment.*;
import com.angelalign.tas.domain.assignment.enums.TaskTypeCodeEnum;
import com.angelalign.tas.domain.parser.ConsumedQuotaTaskAttributeParser;
import com.angelalign.tas.domain.parser.DesignerAttributeParser;
import com.angelalign.tas.domain.parser.TaskAttributeParser;
import com.angelalign.tas.domain.parser.model.PreferTask;
import com.angelalign.tas.rest.vo.support.MedTagVo;
import com.angelalign.tas.score.base.util.math.MathematicalFunctionUtil;
import com.angelalign.tas.solver.phase.enums.PhaseName;
import com.angelalign.tas.util.DecimalUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.optaplanner.core.api.score.buildin.bendablelong.BendableLongScore;
import org.optaplanner.core.api.score.stream.Constraint;
import org.optaplanner.core.api.score.stream.ConstraintCollectors;
import org.optaplanner.core.api.score.stream.ConstraintFactory;
import org.optaplanner.core.api.score.stream.Joiners;

import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static org.optaplanner.core.api.score.stream.ConstraintCollectors.count;
import static org.optaplanner.core.api.score.stream.Joiners.filtering;

@Slf4j
public class BaseConstraintPool {

    /**
     * 判断约束是否在当前阶段生效
     *
     * @param inactivePhases 不生效的阶段集合
     * @return 如果当前阶段不在不生效阶段集合中，则返回true
     */
    protected boolean isConstraintActiveInCurrentPhase(Set<PhaseName> inactivePhases) {
        if (inactivePhases == null || inactivePhases.isEmpty()) {
            return true; // 如果没有指定不生效阶段，则在所有阶段都生效
        }

        PhaseName currentPhase = PhaseQueueManager.getCurrentPhase();
        boolean isActive = !inactivePhases.contains(currentPhase);

        if (!isActive) {
            log.debug("约束在当前阶段 {} 不生效，不生效阶段: {}", currentPhase, inactivePhases);
        }

        return isActive;
    }

    public Predicate<Task> fullDesignModifyAssignLastDesignerPrivilege() {
        return task -> {
            if (!TaskTypeCodeEnum.executeFullDesignModification.name().equals(task.getTaskType().getCode())) {
                return false;
            }

            List<String> allDesigner = task.getProblem().getDesigners()
                    .stream()
                    .map(Designer::getOriginId)
                    .toList();
            // 当前病例的所有工单
            List<CacheCaseTask> byCaseCode = CaseTaskUtil.findAndSortByCaseCode(task.getCaseCode(), task.getProblem());
            // 如果在，那么就必须分给之前做过该工单的设计师
            String originId = task.getDesigner().getOriginId();

            return byCaseCode.stream()
                    .filter(caseTask -> caseTask.getAssigneeId() != null)
                    .filter(caseTask -> TaskTypeCodeEnum.isDesignTask(caseTask.getTaskTypeCode()))
                    .filter(caseTask -> caseTask.getPhaseType().equals(task.getPhaseType()))
                    // 所有需要分配的设计师是否在该工单之前做过的工单设计师范围内
                    .filter(caseTask -> allDesigner.contains(String.valueOf(caseTask.getAssigneeId())))
                    .filter(caseTask -> task.getProblem().getDesigners().stream()
                            .filter(designer -> designer.getOriginId().equals(String.valueOf(caseTask.getAssigneeId())))
                            .map(Designer::getOnDuty)
                            .findFirst().orElse(Boolean.FALSE))
                    .findFirst()
                    .map(caseTask -> originId.equals(String.valueOf(caseTask.getAssigneeId())))
                    .orElse(Boolean.FALSE);
        };
    }

    public Constraint penalizedDdmTaskConstraint(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Task.class)
                .filter(task -> isConstraintActiveInCurrentPhase(constraintDefinition.getInactivePhases()))
                .penalize(BendableLongScore.ofSoft(constraintDefinition.getHardLevelSize(), constraintDefinition.getSoftLevelSize(), constraintDefinition.getConstraintLevel(), 1L)
                        , task -> {
                            String originId = task.getDesigner().getOriginId();
                            List<CacheCaseTask> byCaseCode = CaseTaskUtil.findAndSortByCaseCode(task.getCaseCode(), task.getProblem());
                            // 找到生一个工单是当前分配设计师的索引并且扣分，找不到扣一百分
                            return IntStream.range(0, byCaseCode.size())
                                    .filter(i -> {
                                        CacheCaseTask caseTask = byCaseCode.get(i);
                                        return caseTask.getAssigneeId() != null
                                               && caseTask.getAssigneeId().toString().equals(originId);
                                    })
                                    .filter(i -> byCaseCode.get(i).getPhaseType().equals(task.getPhaseType()))
                                    .filter(i -> TaskTypeCodeEnum.isDdmTask(byCaseCode.get(i).getTaskTypeCode()))
                                    .findFirst()
                                    .orElse(100);
                        })
                .asConstraint("penalizedDdmTaskConstraint");
    }

    /**
     * 按照出现的索引奖励意向的的工单
     *
     * @return Constraint
     */
    public Constraint rewardPurposeTaskConstraint(ConstraintFactory constraintFactory
            , int level
            , int hardLevelSize
            , int softLevelSize
            , List<Predicate<Task>> predicateList) {
        return constraintFactory.forEach(Task.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .reward(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1L)
                        , task -> {
                            if (CollectionUtils.isEmpty(predicateList)) return 0;
                            for (int i = 1; i <= predicateList.size(); i++) {
                                int index = i - 1;
                                if (predicateList.get(index).test(task)) {
                                    return predicateList.size() - index;
                                }
                            }
                            return 0;
                        })
                .asConstraint("rewardAllTaskConstraint");
    }

    public Predicate<Task> preferredDesignerPrivilege() {
        return task -> {
            if (CollectionUtils.isEmpty(task.getPreferredDesigner())) return false;
            Designer designer1 = task.getDesigner();
            Boolean enableAboveQuota = DesignerAttributeParser.designerEnableAboveQuota(designer1);
            if (Boolean.FALSE.equals(enableAboveQuota)) return false;
            String code = designer1.getCode();
            return task.getPreferredDesigner().stream().anyMatch(designer -> designer.getCode().equals(code));
        };
    }

    /**
     * 惩罚所有的工单
     *
     * @return Constraint
     */
    protected Constraint penalizeAllTaskConstraint(ConstraintFactory constraintFactory
            , int level
            , int hardLevelSize
            , int softLevelSize
            , List<Predicate<Task>> predicateList, Set<PhaseName> inactivePhases) {
        return constraintFactory.forEach(Task.class)
                .filter(task -> isConstraintActiveInCurrentPhase(inactivePhases))
                .filter(task -> task.getDesigner() != null)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(task -> needCalculate(predicateList, task))
                .penalize(BendableLongScore.ofHard(hardLevelSize, softLevelSize, level, 1L)
                        , task -> 1)
                .asConstraint("penalizeConstraint");
    }

    /**
     * 工单分给它倾向的设计师扣分约束
     *
     * @return Constraint
     */
    protected Constraint taskMustAssignPreferredDesignerPenalizeConstraint(ConstraintFactory constraintFactory
            , int level
            , int hardLevelSize
            , int softLevelSize
            , List<Predicate<Task>> predicateList, Set<PhaseName> inactivePhases) {
        return constraintFactory.forEach(Task.class)
                .filter(task -> isConstraintActiveInCurrentPhase(inactivePhases))
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(task -> CollectionUtils.isNotEmpty(task.getPreferredDesigner()))
                .join(Designer.class, Joiners.equal(Task::getDesigner, d -> d)
                        , filtering((task, designer) -> designer != null
                                                        && needCalculate(predicateList, task)))
                .filter(ConstraintUtil.designerIsNotTaskPreferDesigner())
                .penalize(BendableLongScore.ofHard(hardLevelSize, softLevelSize, level, 1L)
                        , (task, designer) -> 1)
                .asConstraint("taskMustAssignPreferredDesignerPenalizeConstraint");
    }

    /**
     * 工单分给它倾向的设计师扣分约束
     *
     * @return Constraint
     */
    protected Constraint taskMustAssignPreferredDesignerPenalizeConstraint_ForChina(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Task.class)
                .filter(task -> isConstraintActiveInCurrentPhase(constraintDefinition.getInactivePhases()))
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(task -> CollectionUtils.isNotEmpty(task.getPreferredDesigner()))
                .filter(task -> TaskAttributeParser.taskMustAssignPreferredDesigner(task) || task.getPreferredDesigner()
                        .stream()
                        .anyMatch(designer -> designer.getOnDuty() && (task.getMissingSkillIgnoreDifficultyTag(designer, ConstraintDefinition.getDifficultyTagList()).isEmpty())))
                .join(Designer.class, Joiners.equal(Task::getDesigner, d -> d)
                        , filtering((task, designer) -> designer != null
                                                        && needCalculate(constraintDefinition.getPrivilegePredicateList(), task)))
                .filter(ConstraintUtil.designerIsNotTaskPreferDesigner())
                .penalize(BendableLongScore.ofHard(constraintDefinition.getHardLevelSize(), constraintDefinition.getSoftLevelSize(), constraintDefinition.getConstraintLevel(), 1L)
                        , (task, designer) -> 1)
                .asConstraint("taskMustAssignPreferredDesignerPenalizeConstraint");
    }

    protected Constraint taskPreferredDesignerRewardConstraint_ForChina(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Designer.class)
                .filter(designer -> isConstraintActiveInCurrentPhase(constraintDefinition.getInactivePhases()))
                .impactLong(BendableLongScore.ofSoft(constraintDefinition.getHardLevelSize(), constraintDefinition.getSoftLevelSize(), constraintDefinition.getConstraintLevel(), 1L)
                        , designer -> {

                            double appointedTaskQuota = designer.getAppointedTaskQuota();

                            if (appointedTaskQuota <= 0) {
                                return 0L;
                            }
                            double baseScore = designer.getAvailableQuota() <= 0 ? 1000 : 0;
                            double marginalReward = Math.log(1 + appointedTaskQuota) * 100;
                            return DecimalUtils.preciseRound(marginalReward * 100) - DecimalUtils.preciseRound(baseScore);
                        })
                .asConstraint("taskPreferredDesignerRewardConstraint_v3");
    }

    protected Constraint taskPreferredDesignerRewardConstraint_v2(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Designer.class)
                .filter(designer -> isConstraintActiveInCurrentPhase(constraintDefinition.getInactivePhases()))
                .impactLong(BendableLongScore.ofSoft(constraintDefinition.getHardLevelSize(), constraintDefinition.getSoftLevelSize(), constraintDefinition.getConstraintLevel(), 1L)
                        , designer -> {

                            double appointedTaskQuota = designer.getAppointedTaskQuota();

                            if (appointedTaskQuota <= 0) {
                                return 0L;
                            }
                            double baseScore = designer.getAvailableQuota() <= 0 ? 1000 : 0;
                            double marginalReward = Math.log(1 + appointedTaskQuota) * 100;
                            // 乘 设计师的产能比例
                            marginalReward *= ProblemUtil.getDesignerCapacityRatio(designer);
                            return DecimalUtils.preciseRound(marginalReward * 100) - DecimalUtils.preciseRound(baseScore);
                        })
                .asConstraint("taskPreferredDesignerRewardConstraint_v3");
    }

    /**
     * 工单标签优先级加分约束，优先级越高，越容易分到
     *
     * @return Constraint
     */
    protected Constraint medTadLevelScoreRewardConstraint(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Task.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .rewardLong(BendableLongScore.ofSoft(constraintDefinition.getHardLevelSize()
                                , constraintDefinition.getSoftLevelSize()
                                , constraintDefinition.getConstraintLevel(), 1L)
                        , task -> TaskAttributeParser.getMegTag(task).stream()
                                .mapToInt(MedTagVo::getLevelScore)
                                .sum())
                .asConstraint("medTadLevelScoreRewardConstraint");
    }

    /**
     * 优先分工单优先级更高的工单
     *
     * @return Constraint
     */
    protected Constraint taskPriorityScoreRewardConstraint(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Task.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .rewardLong(BendableLongScore.ofSoft(constraintDefinition.getHardLevelSize(), constraintDefinition.getSoftLevelSize()
                                , constraintDefinition.getConstraintLevel(), 1L)
                        , task -> Math.max(0, Optional.ofNullable(task.getPriorityScore()).orElse(0)))
                .asConstraint("taskPriorityScoreRewardConstraint");
    }

    protected Constraint taskFromSameCustomerAssignedToSameEmployeeRewardConstraint(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        // 需求：同一个医生的任务尽量分给同一个员工，并考虑设计师以往的分配情况
        // 解决方法：综合考虑当前和历史分配情况，组合越少，同一个医生的任务就越可能给了同一个员工
        return constraintFactory.forEach(Task.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .groupBy(
                        task -> task.getDentist().getCode() + "VS" + task.getDesigner().getCode(),
                        task -> task,
                        count()
                )
                .map((key, task, count) -> {
                    // 获取设计师对象
                    Designer designer = task.getDesigner();
                    // 获取设计师的历史分配任务数量
                    int historyCount = designer.getDesignerConsumedQuotaTasks().stream()
                            .filter(designerConsumedQuotaTask -> designerConsumedQuotaTask.getDentistCode().equals(task.getDentist().getCode()))
                            .toList().size();
                    // 综合当前和历史分配次数
                    return count + historyCount;
                })
                .reward(
                        BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1),
                        count -> count
                )
                .asConstraint("minimize combination of customer vs employee considering history");
    }

    /**
     * 不同员工的工时尽量均衡
     *
     * @return Constraint
     */
    protected Constraint minimizeWorkTimeVariancePenalizeConstraint_v2(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Designer.class)
                .filter(designer -> designer.getCapacity() != null)
                .penalizeLong(BendableLongScore.ofSoft(constraintDefinition.getHardLevelSize()
                                , constraintDefinition.getSoftLevelSize()
                                , constraintDefinition.getConstraintLevel(), 1),
                        designer -> {
                            Problem problem = designer.getProblem();

                            // 计算有效设计师数量
                            long validDesignerCount = ProblemUtil.getValidDesignerCount(problem);

                            if (validDesignerCount <= 1) {
                                return 0L; // 只有一个设计师，无需均衡
                            }

                            // 计算总工单数
                            Double totalTaskQuota = ProblemUtil.getTaskTotalQuotaWithHistory(problem, designer.getCapacity());

                            // 计算平均工单数（理想情况下每个设计师应该分配的工单数）
                            double averageTaskQuota = totalTaskQuota / validDesignerCount;

                            // 计算当前设计师的工单额度
                            double assignedQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer);
                            double currentTaskQuota = assignedQuota + DesignerAttributeParser.getProcessConsumedQuota(designer);

                            // 计算偏差
                            double deviation = currentTaskQuota - averageTaskQuota;
                            // 如果超出惩罚更多
                            if (deviation > 0) {
                                deviation = Math.pow(deviation, 1.5);
                            }

                            // 使用平方函数惩罚偏差，偏差越大惩罚越重
                            return DecimalUtils.preciseRound(Math.pow(Math.abs(deviation), 2));
                        })
                .asConstraint("minimizeWorkTimeAbsoluteVariancePenalizeConstraint");

    }

    protected Constraint noMissingSkillsPenalizeConstraint(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Task.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(task -> needCalculate(constraintDefinition.getPrivilegePredicateList(), task))
                .penalize(BendableLongScore.ofHard(constraintDefinition.getHardLevelSize(), constraintDefinition.getSoftLevelSize(), constraintDefinition.getConstraintLevel(), 1),
                        task -> task.getMissingSkill().size())
                .asConstraint("noMissingSkillsPenalizeConstraint");
    }

    /**
     * 超额分配按日额度比例均衡约束
     * 当存在超额分配时，确保超额部分也按照设计师的日额度比例进行均衡分配
     * 例如：A日额度100，B日额度200，超额部分应按1:2比例分配
     */
    protected Constraint designerCapacityOutOfWorkPenalizeConstraint_v2(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Designer.class)
                .filter(designer -> {
                    Capacity capacity = designer.getCapacity();
                    return capacity != null && capacity.getOverAllocationLimit() > 0;
                })
                .penalize(BendableLongScore.ofSoft(constraintDefinition.getHardLevelSize()
                                , constraintDefinition.getSoftLevelSize()
                                , constraintDefinition.getConstraintLevel()
                                , 1),
                        designer -> {
                            // 已经超额了
                            Double totalConsumedQuota = designer.getTotalConsumedQuota();
                            Double dailyQuota = designer.getCapacityDailyTotalQuota();
                            if (totalConsumedQuota < dailyQuota) {
                                return 0;
                            }

                            // 计算当前设计师的实际超额量
                            double actualOverAmount = Math.max(0, totalConsumedQuota - dailyQuota);

                            return MathematicalFunctionUtil.marginalPenaltyByCapacity(Math.abs(actualOverAmount), designer);
                        })
                .asConstraint("designerCapacityOutOfWorkPenalizeConstraint_v2");
    }

    protected Constraint designerMustHasAvailableCapacityPenalizeConstraint_v2(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Task.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(task -> needCalculate(constraintDefinition.getPrivilegePredicateList(), task))
                .join(Designer.class, Joiners.equal(Task::getDesigner, d -> d))
                .penalizeLong(BendableLongScore.ofHard(constraintDefinition.getHardLevelSize()
                                , constraintDefinition.getSoftLevelSize()
                                , constraintDefinition.getConstraintLevel()
                                , 1),
                        (task, designer) -> {
                            if (designer.getCapacity() == null || designer.getCapacity().getCaseAllocateType() == null) {
                                return 1;
                            }
                            double deviation = designer.getOverAllocationLimit() - designer.getTotalConsumedQuota();
                            // 没有超额或者刚好分满额度不扣分
                            if (deviation >= 0) {
                                return 0;
                            }
                            // 超出的不满一单 不扣分
                            if (Math.abs(deviation) < task.getConsumeQuota()) {
                                return 0;
                            }
                            return Math.max(DecimalUtils.preciseRound(Math.abs(deviation)), 1);
                        }
                ).asConstraint("designerMustHasAvailableCapacityPenalizeConstraint_v2");
    }

    private <T> boolean needCalculate(List<Predicate<T>> predicates, T task) {
        if (CollectionUtils.isEmpty(predicates)) return true;
        for (Predicate<T> predicate : predicates) {
            if (predicate.test(task)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 虚拟的设计师需要扣分
     *
     * @return Constraint
     */
    public Constraint fakerDesignerPenalizeConstraint_v2(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Designer.class)
                .filter(designer -> isConstraintActiveInCurrentPhase(constraintDefinition.getInactivePhases()))
                .filter(DesignerIdentityUtil::isFakerDesigner)
                .filter(designer -> CollectionUtils.isNotEmpty(designer.getTasks()))
                .penalize(BendableLongScore.ofSoft(constraintDefinition.getHardLevelSize(), constraintDefinition.getSoftLevelSize()
                                , constraintDefinition.getConstraintLevel(), 1)
                        , designer -> designer.getTasks().stream()
                                // 避免没有工单优先级造成不扣分。。
                                .mapToInt(task -> Optional.ofNullable(task.getPriorityScore()).orElse(1))
                                .sum())
                .asConstraint("fakerDesignerPenalizeConstraint_v2");
    }

    /**
     * 虚拟的设计师需要扣分
     * 病例指定的尽可能分掉
     *
     * @return Constraint
     */
    public Constraint fakerDesignerPenalizeConstraint_v3(ConstraintFactory factory, int level, int hardLevelSize, int softLevelSize) {
        return factory.forEach(Designer.class)
                .filter(DesignerIdentityUtil::isFakerDesigner)
                .penalize(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1)
                        , designer -> designer.getTasks().stream()
                                // 避免没有工单优先级造成不扣分。。
                                .mapToInt(task -> {
                                    int preferDesignerPenalize = Boolean.TRUE.equals(TaskAttributeParser.getOrderTaskRuleAssign(task)) ? 1 : 0;
                                    return preferDesignerPenalize * 5000 + Optional.ofNullable(task.getPriorityScore()).orElse(1);
                                })
                                .sum())
                .asConstraint("fakerDesignerPenalizeConstraint_v2");
    }

    /**
     * 如设计师“是否全设计修改只分配自己的”=是，这些设计师不可分配其他人的全设计修改单
     *
     * @return Constraint
     */
    public Constraint fullDesignModifyExclusivePenalizeConstraint(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Task.class)
                // 只对全设计修改生效
                .filter(task -> TaskTypeCodeEnum.executeFullDesignModification.name().equals(task.getTaskType().getCode()))
                // 过滤掉虚拟人
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                // 筛选全设计修改为是的
                .filter(task -> Optional.ofNullable(task.getDesigner().getCapacity()).map(Capacity::getModifyDesignExclusive).orElse(false))
                .filter(task -> {
                    // 当前分配的设计师
                    String originId = task.getDesigner().getOriginId();
                    List<CacheCaseTask> byCaseCode = CaseTaskUtil.findAndSortByCaseCode(task.getCaseCode(), task.getProblem());
                    // 找到上一个工单并且等于当前设计师
                    return byCaseCode.stream()
                            .filter(caseTask -> Objects.nonNull(caseTask.getAssigneeId()))
                            .filter(caseTask -> TaskTypeCodeEnum.isDesignTask(caseTask.getTaskTypeCode()))
                            .findFirst()
                            .filter(caseTask -> caseTask.getAssigneeId().toString().equals(originId))
                            .map(data -> Boolean.FALSE)
                            .orElse(Boolean.TRUE);
                })
                .penalizeLong(BendableLongScore.ofHard(hardLevelSize, softLevelSize, level, 1),
                        task -> 1
                ).asConstraint("fullDesignModifyExclusivePenalizeConstraint");
    }

    /**
     * 如果存在设计师做过该工单之前的工单或方案
     * 那么一定要分给他
     *
     * @return Constraint
     */
    public Constraint designModificationMustSameDesignerPenalizeConstraint(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Task.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(task -> TaskTypeCodeEnum.executeFullDesignModification.name().equals(task.getTaskType().getCode()))
                .penalize(BendableLongScore.ofHard(hardLevelSize, softLevelSize, level, 1), task -> {
                    List<String> allDesigner = task.getProblem().getDesigners()
                            .stream()
                            .map(Designer::getOriginId)
                            .toList();

                    // 当前病例的所有工单
                    List<CacheCaseTask> byCaseCode = CaseTaskUtil.findAndSortByCaseCode(task.getCaseCode(), task.getProblem());
                    // 如果在，那么就必须分给之前做过该工单的设计师
                    String originId = task.getDesigner().getOriginId();

                    return byCaseCode.stream()
                            .filter(caseTask -> TaskTypeCodeEnum.isDesignTask(caseTask.getTaskTypeCode()))
                            .filter(caseTask -> caseTask.getPhaseType().equals(task.getPhaseType()))
                            // 所有需要分配的设计师是否在该工单之前做过的工单设计师范围内
                            .filter(caseTask -> allDesigner.contains(String.valueOf(caseTask.getAssigneeId())))
                            .filter(caseTask -> task.getProblem().getDesigners().stream()
                                    .filter(designer -> designer.getOriginId().equals(String.valueOf(caseTask.getAssigneeId())))
                                    .map(Designer::getOnDuty)
                                    .findFirst().orElse(Boolean.FALSE))
                            .findFirst()
                            .map(caseTask -> originId.equals(String.valueOf(caseTask.getAssigneeId())) ? 0 : 1)
                            .orElse(0);
                })
                .asConstraint("designModificationMustSameDesignerPenalizeConstraint");
    }

    /**
     * 某个标签分配按照设计师的日额度均衡
     *
     * @return Constraint
     */
    public Constraint originTagBalancedRatioPenalizedConstraint(List<String> originalTag, ConstraintFactory constraintFactory
            , int level
            , int hardLevelSize
            , int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .rewardLong(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1),
                        designer -> {
                            // 设计师当前计算分配的工单
                            Set<Task> taskList = designer.getTasks()
                                    .stream()
                                    .filter(assigneTask -> TaskAttributeParser.getMegTag(assigneTask)
                                            .stream()
                                            .map(MedTagVo::getCode)
                                            .collect(Collectors.toSet())
                                            .containsAll(originalTag))
                                    .collect(Collectors.toCollection(() -> new HashSet<>(designer.getTasks().size())));
                            // 设计师已经占用的当前数量
                            List<DesignerConsumedQuotaTask> consumedQuotaTasks = designer.getDesignerConsumedQuotaTasks()
                                    .stream()
                                    .filter(designerConsumedQuotaTask -> ConsumedQuotaTaskAttributeParser.getMegTag(designerConsumedQuotaTask)
                                            .stream()
                                            .map(MedTagVo::getCode)
                                            .collect(Collectors.toCollection(() -> new HashSet<>(designer.getTasks().size())))
                                            .containsAll(originalTag))
                                    .toList();
                            Double consumedTaskQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer, consumedQuotaTasks);
                            Double taskQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, taskList);
                            double rewardByCapacity = MathematicalFunctionUtil.marginalRewardByCapacity(Math.max(0, consumedTaskQuota + taskQuota), designer, 25);
                            return DecimalUtils.preciseRound(rewardByCapacity);
                        })
                .asConstraint("originTagBalancedByCapacityRatio" + originalTag + "PenalizedConstraint");
    }

    protected Constraint minimizeWorkTimeByCapacityVariancePenalizeConstraint_v3(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .penalizeLong(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1),
                        designer -> {
                            double designerExpectedQuota = ProblemUtil.calculateDesignerExpectedQuota(designer);
                            // 获取设计师实际消耗的额度（历史 + 当前分配）
                            double actualConsumedQuota = designer.getTotalConsumedQuota();

                            // 计算偏差
                            double deviation = actualConsumedQuota - designerExpectedQuota;
                            // 如果超出扣分更多
                            if (deviation > 0) {
                                deviation = Math.pow(deviation, 1.8);
                            }

                            return MathematicalFunctionUtil.marginalPenaltyByCapacity(Math.abs(deviation), designer);
                        })
                .asConstraint("minimizeWorkTimeByCapacityVariancePenalizeConstraint");
    }

    /**
     * 最小化工单类型分布不均衡惩罚约束 v3版本
     * 通过计算工单类型分布的方差来衡量分布均匀程度，方差越大惩罚越重
     *
     * @param constraintFactory 约束工厂
     * @param level             软约束级别
     * @param hardLevelSize     硬约束级别数量
     * @param softLevelSize     软约束级别数量
     * @return 约束对象
     */
    protected Constraint minimizeTaskTypeCountBalancePenalizeConstraint_v3(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .filter(designer -> designer.getCapacity() != null && designer.getCapacityDailyTotalQuota() > 0)
                .penalizeLong(
                        BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1),
                        designer -> {
                            // 当前设计分到的工单类型和数量
                            Map<String, Long> designerTaskTypeCountMap = DesignerCapacityConvertor.getDesignerTaskTypeCountMap(designer);
                            // 当前设计预期的工单类型和数量
                            Map<String, Long> designerExpectedTaskTypeQuota = ProblemUtil.getAverageExpectedTaskTypeQuota(designer.getProblem());

                            // 如果没有预期分配或实际分配，不需要计算
                            if (designerExpectedTaskTypeQuota.isEmpty()) {
                                return 0L;
                            }

                            // 计算每种工单类型的实际数量与预期数量的偏差，并应用边际递增惩罚
                            long totalPenalty = 0L;

                            // 遍历所有预期的工单类型
                            for (Map.Entry<String, Long> expectedEntry : designerExpectedTaskTypeQuota.entrySet()) {
                                String taskTypeCode = expectedEntry.getKey();
                                double expectedCount = expectedEntry.getValue();

                                // 获取实际分配的数量（通过TaskType的code匹配）
                                Long designerTaskTypeCount = designerTaskTypeCountMap.getOrDefault(taskTypeCode, 0L);

                                // 计算偏差（实际数量与预期数量的差值）
                                double deviation = Math.pow(Math.abs(designerTaskTypeCount - expectedCount) * 10, 1.5);

                                // 使用边际递增惩罚，偏差越大惩罚越重
                                long penalty = MathematicalFunctionUtil.marginalPenaltyByCapacity(deviation, designer);
                                totalPenalty += penalty;
                            }
                            return totalPenalty;
                        }
                )
                .asConstraint("minimizeTaskTypeCountByCapacityRatioPenalizeConstraint_v3");
    }

    /**
     * 最小化工单类型分布不均衡惩罚约束 v4版本
     * 按照设计师产能比例来衡量工单类型分布的均衡性
     * 产能高的设计师应该分到更多的工单类型，且每种类型的数量也应该按产能比例分配
     *
     * @param constraintFactory 约束工厂
     * @param level             软约束级别
     * @param hardLevelSize     硬约束级别数量
     * @param softLevelSize     软约束级别数量
     * @return 约束对象
     */
    protected Constraint minimizeTaskTypeCountByCapacityRatioPenalizeConstraint_v4(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .filter(designer -> designer.getCapacity() != null && designer.getCapacityDailyTotalQuota() > 0)
                .penalizeLong(
                        BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1),
                        designer -> {
                            // 当前设计分到的工单类型和数量
                            Map<String, Long> designerTaskTypeCountMap = DesignerCapacityConvertor.getDesignerTaskTypeCountMap(designer);
                            // 当前设计师预期的工单类型和数量
                            Map<String, Double> designerExpectedTaskTypeQuota = ProblemUtil.calculateExpectedTaskTypeCount(designer);

                            // 如果没有预期分配或实际分配，不需要计算
                            if (designerExpectedTaskTypeQuota.isEmpty()) {
                                return 0L;
                            }

                            // 计算每种工单类型的实际数量与预期数量的偏差，并应用边际递增惩罚
                            long totalPenalty = 0L;

                            // 遍历所有预期的工单类型
                            for (Map.Entry<String, Double> expectedEntry : designerExpectedTaskTypeQuota.entrySet()) {
                                String taskTypeCode = expectedEntry.getKey();
                                double expectedCount = expectedEntry.getValue();

                                // 获取实际分配的数量（通过TaskType的code匹配）
                                Long designerTaskTypeCount = designerTaskTypeCountMap.getOrDefault(taskTypeCode, 0L);

                                // 计算偏差（实际数量与预期数量的差值）
                                double deviation = Math.pow(Math.abs(designerTaskTypeCount - expectedCount) * 10, 1.5);

                                // 使用边际递增惩罚，偏差越大惩罚越重
                                long penalty = MathematicalFunctionUtil.marginalPenaltyByCapacity(deviation, designer);
                                totalPenalty += penalty;
                            }
                            return totalPenalty;
                        }
                )
                .asConstraint("minimizeTaskTypeCountByCapacityRatioPenalizeConstraint_v4");
    }

    /**
     * 设计师必须分配给有产能的设计师
     *
     * @param constraintFactory constraintFactory
     * @param level             level
     * @param hardLevelSize     hardLevelSize
     * @param softLevelSize     softLevelSize
     * @return Constraint
     */
    protected Constraint designerHasAvailableCapacityPenalizeSoftConstraint(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Task.class)
                .join(Designer.class, Joiners.equal(Task::getDesigner, d -> d))
                .penalizeLong(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1),
                        (task, designer) -> {
                            Capacity capacity = designer.getCapacity();

                            if (capacity == null || capacity.getCaseAllocateType() == null || CollectionUtils.isEmpty(capacity.getCapacityTaskTypeQuotas())) {
                                return 100;
                            }

                            double abs = Math.abs(Math.max(0, designer.getTotalConsumedQuota() - task.getDesigner().getOverAllocationLimit()));
                            return DecimalUtils.preciseRound(abs);
                        }
                ).asConstraint("designerHasAvailableCapacityPenalizeSoftConstraint");
    }

    /**
     * 不在岗的设计师扣分硬约束
     *
     * @return return
     */
    public Constraint unDutyDesignerPenalizeConstraint(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Task.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(task -> !Boolean.TRUE.equals(task.getDesigner().getOnDuty()))
                .penalizeLong(BendableLongScore.ofHard(hardLevelSize, softLevelSize, level, 1),
                        task -> 1)
                .asConstraint("unDutyDesignerPenalizeConstraint");
    }

    /**
     * 被限制的设计师只能分病例指定的设计师
     *
     * @return return
     */
    public Constraint periodLimitDesignerOnlyAssignPreferTaskPenalizeConstraint(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .filter(DesignerAttributeParser::isPeriodLimitDesigner)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .penalizeLong(BendableLongScore.ofHard(hardLevelSize, softLevelSize, level, 1),
                        designer -> designer.getTasks()
                                .stream()
                                .filter(task -> ConstraintUtil.designerIsNotTaskPreferDesigner(task, designer))
                                .count())
                .asConstraint("periodLimitDesignerOnlyAssignerPreferTaskPenalizeConstraint");
    }

    /**
     * 设计师的偏好工单不能超过设定的额度
     *
     * @return Constraint
     */
    public Constraint designerPreferTaskLimitedPenalizeConstraint(ConstraintDefinition constraintDefinition) {
        return constraintDefinition.getConstraintFactory().forEach(Task.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(task -> needCalculate(constraintDefinition.getPrivilegePredicateList(), task))
                .filter(task -> CollectionUtils.isNotEmpty(DesignerAttributeParser.getPreferTaskList(task.getDesigner())))
                .join(Designer.class, Joiners.equal(Task::getDesigner, d -> d))
                .filter(ConstraintUtil::designerIsNotTaskPreferDesigner)
                .penalizeLong(BendableLongScore.ofHard(constraintDefinition.getHardLevelSize()
                                , constraintDefinition.getSoftLevelSize()
                                , constraintDefinition.getConstraintLevel()
                                , 1)
                        , (task, designer) -> {
                            // 计算已经占用优先工单的数量
                            List<PreferTask> preferTaskList = DesignerAttributeParser.getPreferTaskList(designer);
                            double sum = preferTaskList.stream()
                                    .mapToDouble(preferTask -> {
                                        // 筛选符合优先工单表达式的消耗工单
                                        List<DesignerConsumedQuotaTask> designerConsumedPreferTask = DesignerPreferTaskOrSkillUtil.getDesignerConsumedPreferTask(List.of(preferTask), designer);
                                        // 计算已经分配过的工单消耗的产能信息
                                        Double consumedPreferTaskQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer, designerConsumedPreferTask);
                                        // 筛选当前已经分配的符合优先工单的工单
                                        Set<Task> designerPreferTask = DesignerPreferTaskOrSkillUtil.getDesignerPreferTask(List.of(preferTask), designer.getTasks());
                                        // 计算当前工单一共消耗的产能信息
                                        Double preferTaskQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, designerPreferTask);
                                        // 判断是否有超过的
                                        double diff = (preferTaskQuota + consumedPreferTaskQuota) - designer.getCapacityDailyTotalQuota() * preferTask.getQuota();
                                        return Math.max(0, DecimalUtils.preciseRound(diff * 10));
                                    })
                                    .sum();
                            return DecimalUtils.preciseRound(sum);
                        })
                .asConstraint("designerPreferTaskLimitedPenalizeConstraint");
    }

    public Constraint designerPreferTaskByCapacityRewardConstraint_v4(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(designer -> CollectionUtils.isNotEmpty(DesignerAttributeParser.getPreferTaskList(designer)))
                .groupBy(
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getTotalConsumedQuota())),
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getCapacityDailyTotalQuota())),
                        ConstraintCollectors.toList()
                )
                .rewardLong(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1),
                        (totalActualQuota, dailyTotalQuota, designers) -> {

                            double taskTypeSimilarity = TaskTypeVectorSimilarityUtil.calculateTaskTypeCosineVectorSimilarity(designers);

                            return designers.stream()
                                    .mapToLong(designer -> {
                                        Set<Task> preferTask = DesignerPreferTaskOrSkillUtil.getPreferTask(designer);
                                        // 当前设计师本次分配的偏好技能工单额度
                                        Double designerPreferTaskActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferTask);
                                        // 当前设计师历史分配的工单（不可动）
                                        Double designerConsumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);
                                        double totalQuota = designer.getAppointedTaskQuota()
                                                            + designerPreferTaskActualQuota
                                                            + designerConsumedQuota;
                                        double ratio = totalQuota / Math.max(designer.getCapacityDailyTotalQuota(), 1);
                                        double rewardByCapacity = MathematicalFunctionUtil.marginalRewardByCapacity(ratio * 100, designer, 100);
                                        double score = rewardByCapacity * 0.7 + taskTypeSimilarity * 0.3 * 100;
                                        return DecimalUtils.preciseRound(score * 100);
                                    })
                                    .sum();
                        })
                .asConstraint("designerPreferTaskByCapacityRewardConstraint_v4");
    }


    public Constraint designerPreferSkillByCapacityRewardConstraint_v4(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(designer -> CollectionUtils.isNotEmpty(DesignerAttributeParser.getPreferSkillExpression(designer)))
                .groupBy(
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getTotalConsumedQuota())),
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getCapacityDailyTotalQuota())),
                        ConstraintCollectors.toList()
                )
                .rewardLong(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1),
                        (totalActualQuota, dailyTotalQuota, designers) -> {

                            double taskTypeSimilarity = TaskTypeVectorSimilarityUtil.calculateTaskTypeCosineVectorSimilarity(designers);

                            return designers.stream()
                                    .mapToLong(designer -> {
                                        Set<Task> preferTask = DesignerPreferTaskOrSkillUtil.getPreferTask(designer);
                                        // 当前设计师本次分配的偏好技能工单额度
                                        Double designerPreferTaskActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferTask);
                                        Set<Task> preferSkillTask = DesignerPreferTaskOrSkillUtil.getPreferSkillTask(designer);
                                        // 当前设计师本次分配的偏好技能工单额度
                                        Double designerPreferSkillActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferSkillTask);
                                        // 当前设计师历史分配的工单（不可动）
                                        Double designerConsumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);

                                        double totalQuota = designer.getAppointedTaskQuota()
                                                            + designerPreferSkillActualQuota
                                                            + designerPreferTaskActualQuota
                                                            + designerConsumedQuota;
                                        if (totalQuota <= 0) return 0;
                                        double ratio = totalQuota / Math.max(designer.getCapacityDailyTotalQuota(), 1);
                                        double rewardByCapacity = MathematicalFunctionUtil.marginalRewardByCapacity(ratio * 100, designer, 100);
                                        double score = rewardByCapacity * 0.7 + taskTypeSimilarity * 0.3 * 100;
                                        return DecimalUtils.preciseRound(score * 100);
                                    })
                                    .sum();
                        })
                .asConstraint("designerPreferSkillByCapacityRewardConstraint_v4");
    }

    public Constraint designerPreferTaskByCapacityRewardConstraint_v3(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(designer -> CollectionUtils.isNotEmpty(designer.getTasks()))
                .filter(designer -> CollectionUtils.isNotEmpty(DesignerAttributeParser.getPreferTaskList(designer)))
                // 声明对所有可能影响appointedTaskQuota的Task属性的依赖
                .groupBy(
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getTotalConsumedQuota())),
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getCapacityDailyTotalQuota())),
                        ConstraintCollectors.toList()
                )
                .rewardLong(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1),
                        (totalActualQuota, dailyTotalQuota, designers) -> {

                            double taskTypeSimilarity = TaskTypeVectorSimilarityUtil.calculateTaskTypeCosineVectorSimilarity(designers);

                            return designers.stream()
                                    .mapToLong(designer -> {
                                        Set<Task> preferTask = DesignerPreferTaskOrSkillUtil.getPreferTask(designer);
                                        // 当前设计师本次分配的偏好技能工单额度
                                        Double designerPreferTaskActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferTask);
                                        // 当前设计师历史分配的工单（不可动）
                                        Double designerConsumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);
                                        double totalQuota = designer.getAppointedTaskQuota()
                                                            + designerPreferTaskActualQuota
                                                            + designerConsumedQuota;
                                        double ratio = totalQuota / Math.max(designer.getCapacityDailyTotalQuota(), 1);
                                        double score = taskTypeSimilarity * 0.3 + ratio * 0.7;
                                        double marginalReward = Math.log(1 + score * 100) * 1000;
                                        marginalReward *= ProblemUtil.getDesignerCapacityRatio(designer);
                                        return DecimalUtils.preciseRound(marginalReward);
                                    })
                                    .sum();
                        })
                .asConstraint("designerPreferTaskByCapacityRewardConstraint_v3");
    }

    @Deprecated
    public Constraint designerPreferSkillByCapacityRewardConstraint_v3(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(designer -> CollectionUtils.isNotEmpty(designer.getTasks()))
                .filter(designer -> CollectionUtils.isNotEmpty(DesignerAttributeParser.getPreferSkillExpression(designer)))
                .groupBy(
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getTotalConsumedQuota())),
                        ConstraintCollectors.sum(designer -> DecimalUtils.preciseRound(designer.getCapacityDailyTotalQuota())),
                        ConstraintCollectors.toList()
                )
                .rewardLong(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1),
                        (totalActualQuota, dailyTotalQuota, designers) -> {

                            double taskTypeSimilarity = TaskTypeVectorSimilarityUtil.calculateTaskTypeCosineVectorSimilarity(designers);

                            return designers.stream()
                                    .mapToLong(designer -> {
                                        Set<Task> preferTask = DesignerPreferTaskOrSkillUtil.getPreferTask(designer);
                                        // 当前设计师本次分配的偏好技能工单额度
                                        Double designerPreferTaskActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferTask);
                                        Set<Task> preferSkillTask = DesignerPreferTaskOrSkillUtil.getPreferSkillTask(designer);
                                        // 当前设计师本次分配的偏好技能工单额度
                                        Double designerPreferSkillActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferSkillTask);
                                        // 当前设计师历史分配的工单（不可动）
                                        Double designerConsumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);

                                        double totalQuota = designer.getAppointedTaskQuota()
                                                            + designerPreferSkillActualQuota
                                                            + designerPreferTaskActualQuota
                                                            + designerConsumedQuota;
                                        double ratio = totalQuota / Math.max(designer.getCapacityDailyTotalQuota(), 1);
                                        double score = taskTypeSimilarity * 0.3 + ratio * 0.7;
                                        double marginalReward = Math.log(1 + score * 100) * 1000;
                                        marginalReward *= ProblemUtil.getDesignerCapacityRatio(designer);
                                        return DecimalUtils.preciseRound(marginalReward);
                                    })
                                    .sum();
                        })
                .asConstraint("fillDesignerPreferSkillRewardConstraint_v3");
    }

    /**
     * 优先填满设计师的优先工单约束（避免垄断）
     * 使用边际递减奖励机制，确保设计师优先分配自己的偏好工单，同时考虑工作量均衡
     * <p>
     * 公式：
     * pow(ln(1+(偏好技能工单额度+已经消耗的额度) * 25 , 2))
     *
     * @return Constraint
     */
    public Constraint fillDesignerPreferTaskRewardConstraint_ForAtp(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(designer -> !DesignerAttributeParser.getPreferTaskList(designer).isEmpty())
                .impactLong(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1), designer -> {
                    Set<Task> preferTask = DesignerPreferTaskOrSkillUtil.getPreferTask(designer);
                    // 当前设计师本次分配的偏好技能工单额度
                    Double designerPreferTaskActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferTask);
                    // 当前设计师历史分配的工单（不可动）
                    Double designerConsumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);
                    return MathematicalFunctionUtil.marginalReward(designer.getAppointedTaskQuota()
                                                                   + designerPreferTaskActualQuota
                                                                   + designerConsumedQuota);
                }).asConstraint("fillDesignerPreferTaskRewardConstraint_v2");
    }

    public Constraint fillDesignerPreferSkillRewardConstraint_ForAtp(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Designer.class)
                .filter(DesignerIdentityUtil::isAvailableDesigner)
                .filter(designer -> CollectionUtils.isNotEmpty(DesignerAttributeParser.getPreferSkillExpression(designer)))
                .filter(designer -> CollectionUtils.isNotEmpty(designer.getTasks()))
                .impactLong(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1)
                        , designer -> {
                            Set<Task> preferTask = DesignerPreferTaskOrSkillUtil.getPreferTask(designer);
                            // 当前设计师本次分配的偏好技能工单额度
                            Double designerPreferTaskActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferTask);
                            Set<Task> preferSkillTask = DesignerPreferTaskOrSkillUtil.getPreferSkillTask(designer);
                            // 当前设计师本次分配的偏好技能工单额度
                            Double designerPreferSkillActualQuota = DesignerCapacityConvertor.covertAssignedTaskQuota(designer, preferSkillTask);
                            // 当前设计师历史分配的工单（不可动）
                            Double designerConsumedQuota = DesignerCapacityConvertor.covertConsumedTaskQuota(designer);
                            return MathematicalFunctionUtil.marginalReward(
                                    designer.getAppointedTaskQuota()
                                    + designerPreferTaskActualQuota
                                    + designerPreferSkillActualQuota
                                    + designerConsumedQuota);
                        })
                .asConstraint("fillDesignerPreferSkillRewardConstraint_v2");
    }

    /**
     * 设计师优先分某某些标签的工单
     *
     * @return Constraint
     */
    public Constraint designerPreferMedTagRewardConstraint_V2(ConstraintFactory constraintFactory
            , int level
            , int hardLevelSize
            , int softLevelSize
            , List<String> medTagCodeList) {
        return constraintFactory.forEach(Task.class)
                .rewardLong(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1)
                        , task -> {
                            int intExact = Math.toIntExact(TaskAttributeParser.getMegTag(task).stream()
                                    .filter(medTagVo -> medTagCodeList.contains(medTagVo.getCode()))
                                    .count());
                            return MathematicalFunctionUtil.marginalRewardWithPowByCapacity(Math.pow(intExact, 3), task.getDesigner());
                        })
                .asConstraint("designerPreferMedTagRewardConstraint_2");
    }

    /**
     * 设计师的职级越低 越更容易分到工单
     *
     * @return Constraint
     */
    public Constraint taskPreferLowLevelPenalizeConstraint(ConstraintFactory constraintFactory, int level, int hardLevelSize, int softLevelSize) {
        return constraintFactory.forEach(Task.class)
                .penalize(BendableLongScore.ofSoft(hardLevelSize, softLevelSize, level, 1)
                        , task -> task.getDesigner().getDesignerLevelSequence())
                .asConstraint("taskPreferLowLevelRewardConstraint");
    }
}
package com.angelalign.tas.score.base.util.math;

public class KernelFunctionUtil {
    /**
     * RBF核函数（高斯核）
     * K(x) = exp(-γ * (x - target)²)
     *
     * @param value  输入值
     * @param target 目标值（期望比例）
     * @param gamma  敏感度参数
     * @return 核函数值 [0,1]
     */
    public static double calculateRBFKernel(double value, double target, double gamma) {
        double deviation = value - target;
        return Math.exp(-gamma * deviation * deviation);
    }
}

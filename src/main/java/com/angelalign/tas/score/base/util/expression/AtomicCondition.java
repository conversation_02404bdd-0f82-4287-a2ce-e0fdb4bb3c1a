package com.angelalign.tas.score.base.util.expression;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.util.Map;

@Data
public class AtomicCondition implements Condition {
    @JsonProperty("identifier")
    private String identifier;

    @JsonCreator
    AtomicCondition(@JsonProperty("identifier") String identifier) {
        this.identifier = identifier;
    }

    @Override
    public String toExpression() {
        return identifier; // 直接返回标识符
    }
    @Override
    public boolean test(Map<String, Boolean> context) {
        return context.getOrDefault(identifier.toUpperCase(), false);
    }
}

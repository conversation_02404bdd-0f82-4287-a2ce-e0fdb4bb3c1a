package com.angelalign.tas.score.base.util.math;

import java.util.Arrays;

/**
 * 数组变换工具类
 * 输入：double数组（每个元素0-1之间）
 * 输出：0-1之间的double值（数组元素和越大，输出越大）
 */
public class ArrayTransformUtil {
    /**
     * 计算数组的双目标优化得分（和越大、元素越均衡，得分越高）
     * <p>
     * 数学原理：
     * score = (k * sum - log(1 + cv)) / (1 + |k * sum - log(1 + cv)|)
     * 其中 coefficientOfVariation = σ/μ
     * 
     * 这个函数的特点：
     * 1. 使用 x/(1+|x|) 函数确保输出在(-1,1)范围内，然后映射到(0,1)
     * 2. 避免了sigmoid函数的精度问题
     * 3. k * sum 项放大和的差异
     * 4. log(1 + cv) 项压缩均衡性的影响
     * 5. 当和相同时，更均衡的数组得分更高
     * 6. 当和不同时，即使不均衡，和更大的数组得分更高
     *
     * @param array 输入数组（任意值域和长度）
     * @param k     尺度参数（控制总和对得分的影响强度，建议值：0.01-0.1）
     * @return 优化得分，范围 (0,1)
     */
    public static double normalizeTransform(double[] array) {
        if (array == null || array.length == 0) return 0;
        double k = 0.5;
        
        // 1. 计算基础统计量
        double sum = Arrays.stream(array).sum();
        double mean = sum / array.length;

        // 处理均值为零的特殊情况
        if (Math.abs(mean) < 1e-10) {
            // 当所有元素为0时，定义CV为0（完美均衡）
            if (Arrays.stream(array).allMatch(x -> Math.abs(x) < 1e-10)) {
                double rawScore = k * sum;
                return (rawScore / (1.0 + Math.abs(rawScore)) + 1.0) / 2.0; // 映射到(0,1)
            }
            return 0; // 非全零但均值为零，视为高度不均衡
        }

        // 2. 计算变异系数（Coefficient of Variation）
        double variance = Arrays.stream(array)
                .map(x -> Math.pow(x - mean, 2))
                .average()
                .orElse(0);
        double stdDev = Math.sqrt(variance);
        double cv = stdDev / Math.abs(mean); // 保证CV非负

        // 3. 核心函数计算 - 使用更敏感的映射函数
        double sumComponent = k * sum;
        double balanceComponent = 5.0 * Math.log(1.0 + cv); // 大幅增加均衡性惩罚的权重
        double rawScore = sumComponent - balanceComponent;
        
        // 使用更敏感的映射函数：x/(0.2+|x|) 让差异更明显
        double normalizedScore = rawScore / (0.2 + Math.abs(rawScore));
        return (normalizedScore + 1.0) / 2.0;
    }

    /**
     * 将数组元素和通过sigmoid函数映射到(0,1)范围
     *
     * @param array 输入数组，每个元素0-1之间
     * @param scale 缩放因子，控制函数陡峭程度，默认1.0
     * @return 0-1之间的输出值
     */
    public static double sigmoidTransform(double[] array, double scale) {
        double sum = Arrays.stream(array).sum();
        return 1.0 / (1.0 + Math.exp(-scale * sum));
    }

    /**
     * Sigmoid变换函数
     */
    public static double sigmoidTransform(double[] array) {
        return sigmoidTransform(array, 1.0);
    }

    /**
     * 线性变换函数
     * 将数组元素和线性映射到[0, 1]范围
     * 注意：需要知道可能的和的范围
     *
     * @param array          输入数组
     * @param maxPossibleSum 最大可能的和（通常是数组长度）
     * @return 0-1之间的输出值
     */
    public static double linearTransform(double[] array, double maxPossibleSum) {
        double sum = Arrays.stream(array).sum();
        double result = sum / maxPossibleSum;
        return Math.max(0.0, Math.min(1.0, result)); // 确保结果在[0,1]范围内
    }

    /**
     * 线性变换函数（使用数组长度作为最大可能和）
     */
    public static double linearTransform(double[] array) {
        return linearTransform(array, array.length);
    }

    /**
     * 对数变换函数
     * 适合处理数值差异较大的情况
     *
     * @param array 输入数组
     * @param base  对数底数
     * @return 0-1之间的输出值
     */
    public static double logTransform(double[] array, double base) {
        double sum = Arrays.stream(array).sum();
        if (sum <= 0) {
            return 0.0;
        }
        return Math.log(1 + sum) / Math.log(1 + base);
    }

    /**
     * 对数变换函数（使用自然对数）
     */
    public static double logTransform(double[] array) {
        return logTransform(array, Math.E);
    }

    /**
     * 幂函数变换
     *
     * @param array 输入数组
     * @param power 幂次，<1时函数更陡峭，>1时函数更平缓
     * @return 0-1之间的输出值
     */
    public static double powerTransform(double[] array, double power) {
        double sum = Arrays.stream(array).sum();
        return Math.pow(sum, power);
    }

    /**
     * 平方根变换（power=0.5）
     */
    public static double sqrtTransform(double[] array) {
        return powerTransform(array, 0.5);
    }

    /**
     * 平方变换（power=2）
     */
    public static double squareTransform(double[] array) {
        return powerTransform(array, 2.0);
    }

    /**
     * Sigmoid 函数：将任意实数映射到 (0,1)
     */
    private static double sigmoid(double x) {
        return 1.0 / (1.0 + Math.exp(-x));
    }

    /**
     * 指数变换函数
     *
     * @param array 输入数组
     * @param base  指数底数
     * @return 0-1之间的输出值
     */
    public static double exponentialTransform(double[] array, double base) {
        double sum = Arrays.stream(array).sum();
        return 1.0 - Math.pow(base, -sum);
    }

    /**
     * 指数变换函数（使用e作为底数）
     */
    public static double exponentialTransform(double[] array) {
        return exponentialTransform(array, Math.E);
    }

    /**
     * 测试函数
     */
    public static void main(String[] args) {


        // 测试数据：每个数组的元素都是0-1之间
        double[] array1 = {0.7147937707720211, 1.0020811677038275, 0.8522845117030398
                , 0.8490190417966686, 1.0144785195688801, 0.8198915917499229
                , 0.8198915917499229, 0.8465616732800196, 1.0041580220928046
                , 0.8122328620674136, 0.8881941729649485, 0.9581521567869873
                , 0.8457409637576849, 1.0124228365658292, 1.0062305898749053
                , 0.8173466556826152, 0.8530989261379818, 0.8866290718846924
                , 0.8440971508067067, 0.8215838362577492, 0.8668269082630562
                , 0.997914491994847, 0.8173466556826152, 0.816496580927726};
        double[] array2 = {0.8147937707720211, 1.0020811677038275, 0.8522845117030398
                , 0.8490190417966686, 1.0144785195688801, 0.8198915917499229
                , 0.8198915917499229, 0.8465616732800196, 1.0041580220928046
                , 0.8122328620674136, 0.8881941729649485, 0.9581521567869873
                , 0.8457409637576849, 1.0124228365658292, 1.0062305898749053
                , 0.8173466556826152, 0.8530989261379818, 0.8866290718846924
                , 0.8440971508067067, 0.8215838362577492, 0.8668269082630562
                , 0.997914491994847, 0.8173466556826152, 0.816496580927726};
        double[] array3 = {0.9147937707720211, 1.0020811677038275, 0.8522845117030398
                , 0.8490190417966686, 1.0144785195688801, 0.8198915917499229
                , 0.8198915917499229, 0.8465616732800196, 1.0041580220928046
                , 0.8122328620674136, 0.8881941729649485, 0.9581521567869873
                , 0.8457409637576849, 1.0124228365658292, 1.0062305898749053
                , 0.8173466556826152, 0.8530989261379818, 0.8866290718846924
                , 0.8440971508067067, 0.8215838362577492, 0.8668269082630562
                , 0.997914491994847, 0.8173466556826152, 0.816496580927726};

        double[] array4 = {
                22.79236509781123, 22.80987212636832, 22.74827297560066
                , 22.827305903545707, 22.85332055621106, 22.757128806068636
                , 22.757128806068636, 22.774784199716613, 22.801127806964477
                , 22.765965854273375, 22.861956137885947, 22.6857494730262
                , 23.129248833765793, 22.765965854273375, 22.79236509781123
                , 22.80987212636832, 22.712660223190415, 22.879173793239737
                , 22.801127806964477, 22.774784199716613, 22.757128806068636
                , 22.79236509781123, 22.827305903545707, 22.80987212636832};
        double[] array5 = {
                23.79236509781123, 22.80987212636832, 22.74827297560066
                , 22.827305903545707, 22.85332055621106, 22.757128806068636
                , 22.757128806068636, 22.774784199716613, 22.801127806964477
                , 22.765965854273375, 22.861956137885947, 22.6857494730262
                , 23.129248833765793, 22.765965854273375, 22.79236509781123
                , 22.80987212636832, 22.712660223190415, 22.879173793239737
                , 22.801127806964477, 22.774784199716613, 22.757128806068636
                , 22.79236509781123, 22.827305903545707, 22.80987212636832};

        System.out.println("=== 数组变换函数测试 ===");
        System.out.println("数组1: " + Arrays.toString(array1) + " (和=" + Arrays.stream(array1).sum() + ")");
        System.out.println("数组2: " + Arrays.toString(array2) + " (和=" + Arrays.stream(array2).sum() + ")");
        System.out.println("数组3: " + Arrays.toString(array3) + " (和=" + Arrays.stream(array3).sum() + ")");
        System.out.println();

        System.out.println("1. Sigmoid变换结果：");
        System.out.println("数组1: " + sigmoidTransform(array1));
        System.out.println("数组2: " + sigmoidTransform(array2));
        System.out.println("数组3: " + sigmoidTransform(array3));
        System.out.println();

        System.out.println("2. 线性变换结果：");
        System.out.println("数组1: " + linearTransform(array1));
        System.out.println("数组2: " + linearTransform(array2));
        System.out.println("数组3: " + linearTransform(array3));
        System.out.println();

        System.out.println("3. 对数变换结果：");
        System.out.println("数组1: " + logTransform(array1));
        System.out.println("数组2: " + logTransform(array2));
        System.out.println("数组3: " + logTransform(array3));
        System.out.println();

        System.out.println("4. 平方根变换结果：");
        System.out.println("数组1: " + sqrtTransform(array1));
        System.out.println("数组2: " + sqrtTransform(array2));
        System.out.println("数组3: " + sqrtTransform(array3));
        System.out.println();

        System.out.println("5. 平方和变换结果：");
        System.out.println("数组1: " + squareTransform(array1));
        System.out.println("数组2: " + squareTransform(array2));
        System.out.println("数组3: " + squareTransform(array3));
        System.out.println();

        System.out.println("6. 指数变换函数结果：");
        System.out.println("数组1: " + exponentialTransform(array1));
        System.out.println("数组2: " + exponentialTransform(array2));
        System.out.println("数组3: " + exponentialTransform(array3));
        System.out.println();

        // 测试优先级排序逻辑
        System.out.println("=== 优先级排序测试 ===");

        // 测试1：和相同，比较均衡性
        double[] unbalanced = {233.0, 17.0}; // 不均衡：和为120，方差大
        double[] balanced = {200.0, 50.0};   // 均衡：和为120，方差小

        System.out.println("测试1 - 和相同的情况：");
        System.out.println("不均衡数组: " + Arrays.toString(array5) + " (和=" + Arrays.stream(unbalanced).sum() + ")");
        System.out.println("均衡数组: " + Arrays.toString(array4) + " (和=" + Arrays.stream(balanced).sum() + ")");
        System.out.println("不均衡数组结果: " + normalizeTransform(array5));
        System.out.println("均衡数组结果: " + normalizeTransform(array4));
        System.out.println("均衡数组结果 > 不均衡数组结果: " + (normalizeTransform(array4) > normalizeTransform(array5)));
        System.out.println();

        // 测试2：和不同，和大的应该结果更大
        double[] lowSum = {30.0, 30.0};   // 和=60，均衡
        double[] highSum = {80.0, 80.0};  // 和=160，均衡

        System.out.println("测试2 - 和不同的情况：");
        System.out.println("低和数组: " + Arrays.toString(lowSum) + " (和=" + Arrays.stream(lowSum).sum() + ")");
        System.out.println("高和数组: " + Arrays.toString(highSum) + " (和=" + Arrays.stream(highSum).sum() + ")");
        System.out.println("低和数组结果: " + normalizeTransform(lowSum));
        System.out.println("高和数组结果: " + normalizeTransform(highSum));
        System.out.println("高和数组结果 > 低和数组结果: " + (normalizeTransform(highSum) > normalizeTransform(lowSum)));
        System.out.println();

        // 测试3：和不同，即使低和的更均衡，高和的也应该结果更大
        double[] lowSumBalanced = {40.0, 40.0};     // 和=80，均衡
        double[] highSumUnbalanced = {90.0, 10.0};  // 和=100，不均衡

        System.out.println("测试3 - 和优先于均衡性：");
        System.out.println("低和均衡数组: " + Arrays.toString(lowSumBalanced) + " (和=" + Arrays.stream(lowSumBalanced).sum() + ")");
        System.out.println("高和不均衡数组: " + Arrays.toString(highSumUnbalanced) + " (和=" + Arrays.stream(highSumUnbalanced).sum() + ")");
        System.out.println("低和均衡数组结果: " + normalizeTransform(lowSumBalanced));
        System.out.println("高和不均衡数组结果: " + normalizeTransform(highSumUnbalanced));
        System.out.println("高和不均衡数组结果 > 低和均衡数组结果: " + (normalizeTransform(highSumUnbalanced) > normalizeTransform(lowSumBalanced)));
        System.out.println();

        // 测试4：大数值测试
        double[] largeValues = {1000.0, 1000.0, 1000.0};  // 和=3000，均衡
        double[] smallValues = {1.0, 1.0, 1.0};           // 和=3，均衡

        System.out.println("测试4 - 大数值测试：");
        System.out.println("大数值数组: " + Arrays.toString(largeValues) + " (和=" + Arrays.stream(largeValues).sum() + ")");
        System.out.println("小数值数组: " + Arrays.toString(smallValues) + " (和=" + Arrays.stream(smallValues).sum() + ")");
        System.out.println("大数值数组结果: " + normalizeTransform(largeValues));
        System.out.println("小数值数组结果: " + normalizeTransform(smallValues));
        System.out.println("大数值数组结果 > 小数值数组结果: " + (normalizeTransform(largeValues) > normalizeTransform(smallValues)));
        System.out.println();

        // 测试5：边界情况
        double[] allZeros = {0.0, 0.0, 0.0};
        double[] allOnes = {1.0, 1.0, 1.0};

        System.out.println("测试5 - 边界情况：");
        System.out.println("全零数组: " + Arrays.toString(allZeros) + " (和=" + Arrays.stream(allZeros).sum() + ")");
        System.out.println("全一数组: " + Arrays.toString(allOnes) + " (和=" + Arrays.stream(allOnes).sum() + ")");
        System.out.println("全零数组结果: " + normalizeTransform(allZeros));
        System.out.println("全一数组结果: " + normalizeTransform(allOnes));
    }
} 
package com.angelalign.tas.score.base.util.expression;

import com.fasterxml.jackson.annotation.JsonSubTypes;
import com.fasterxml.jackson.annotation.JsonTypeInfo;

import java.util.Map;
import java.util.function.Predicate;

@JsonTypeInfo(use = JsonTypeInfo.Id.NAME, property = "type")
@JsonSubTypes({
        @JsonSubTypes.Type(value = CompositeCondition.class, name = "composite"),
        @JsonSubTypes.Type(value = AtomicCondition.class, name = "atomic")
})
public interface Condition extends Predicate<Map<String, Boolean>> {
    String toExpression();
}


package com.angelalign.tas.common.http;

import lombok.*;

import javax.annotation.Nullable;
import java.util.Map;

@Getter
@Builder
@ToString
@EqualsAndHashCode
public class HttpRequest {
    @NonNull
    private String method;
    @NonNull
    private String url;
    @Nullable
    private Map<String, String> queries;
    @Nullable
    private Map<String, String> headers;
    @Nullable
    private String body;
    @Nullable
    private Map<String, String> urlEncodedForm;
}

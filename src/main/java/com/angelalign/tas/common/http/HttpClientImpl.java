package com.angelalign.tas.common.http;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.*;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Nullable;
import java.net.URI;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 通用http客户端
 */
@Component
public class HttpClientImpl implements HttpClient {
    private static final int CONNECTION_POOL_TTL = 60_000;
    private static final int CONNECTION_POOL_MAX_SIZE = 1000;
    private static final int CONNECTION_POOL_MAX_SIZE_PER_ROUTE = 30;
    private static final int CONNECT_TIMEOUT = 30_000;
    private static final int CONNECTION_REQUEST_TIMEOUT = 30_000;
    private static final int SOCKET_TIMEOUT = 30_000;
    private static final CloseableHttpClient httpClient = HttpClientBuilder.create()
            .setConnectionManager(
                    new PoolingHttpClientConnectionManager(CONNECTION_POOL_TTL, TimeUnit.MILLISECONDS) {{
                        setMaxTotal(CONNECTION_POOL_MAX_SIZE);
                        setDefaultMaxPerRoute(CONNECTION_POOL_MAX_SIZE_PER_ROUTE);
                    }})
            .setDefaultRequestConfig(
                    RequestConfig.custom()
                            .setConnectTimeout(CONNECT_TIMEOUT)
                            .setConnectionRequestTimeout(CONNECTION_REQUEST_TIMEOUT)
                            .setSocketTimeout(SOCKET_TIMEOUT)
                            .build())
//            .setRetryHandler(new HttpRetryHandler(3, 1000, 1.0))
            .build();

    @Override
    public HttpResponse send(HttpRequest httpRequest) {
        HttpRequestBase request = buildRequest(
                httpRequest.getMethod(),
                httpRequest.getUrl(),
                httpRequest.getQueries(),
                httpRequest.getHeaders(),
                httpRequest.getBody(),
                httpRequest.getUrlEncodedForm());
        try (CloseableHttpResponse response = httpClient.execute(request)) {
            return HttpResponse.builder()
                    .code(response.getStatusLine().getStatusCode())
                    .body(EntityUtils.toString(response.getEntity()))
                    .build();
        } catch (Exception e) {
            throw new HttpException(httpRequest, e);
        } finally {
            request.releaseConnection();
        }
    }


    private HttpRequestBase buildRequest(
            String method, String url,
            @Nullable Map<String, String> queryParams,
            @Nullable Map<String, String> headers,
            @Nullable String body,
            @Nullable Map<String, String> urlEncodedForm) {
        HttpRequestBase request;
        if (Objects.nonNull(queryParams)) {
            url = appendParamsToUrl(url, queryParams);
        }
        StringEntity entity = null;
        if (!StringUtils.equals(method, HttpGet.METHOD_NAME)
                && (!StringUtils.equals(method, HttpDelete.METHOD_NAME) || StringUtils.isNotEmpty(body))) {
            entity = new StringEntity(body == null ? "" : body, StandardCharsets.UTF_8);
        }

        switch (method) {
            case HttpGet.METHOD_NAME:
                request = new HttpGet(url);
                break;
            case HttpPost.METHOD_NAME:
                HttpPost httpPost = new HttpPost(url);
                if (MapUtils.isNotEmpty(urlEncodedForm)) {
                    List<NameValuePair> params = new ArrayList<>();
                    urlEncodedForm.forEach((key, value) -> params.add(new BasicNameValuePair(key, value)));
                    httpPost.setEntity(new UrlEncodedFormEntity(params, StandardCharsets.UTF_8));
                } else {
                    httpPost.setEntity(entity);
                }
                request = httpPost;
                break;
            case HttpDelete.METHOD_NAME:
                if (Objects.isNull(entity)) {
                    request = new HttpDelete(url);
                } else {
                    HttpEntityEnclosingHttpDelete httpDelete = new HttpEntityEnclosingHttpDelete(url);
                    httpDelete.setEntity(entity);
                    request = httpDelete;
                }
                break;
            case HttpPut.METHOD_NAME:
                HttpPut httpPut = new HttpPut(url);
                httpPut.setEntity(entity);
                request = httpPut;
                break;
            case HttpPatch.METHOD_NAME:
                HttpPatch httpPatch = new HttpPatch(url);
                httpPatch.setEntity(entity);
                request = httpPatch;
                break;
            default:
                throw new RuntimeException("unrecognized http method: " + method);
        }
        if (Objects.nonNull(headers)) {
            headers.forEach(request::setHeader);
        }
        return request;
    }

    private String appendParamsToUrl(String url, Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return url;
        }
        String encodedParams = encodeParams(params);
        if (url.contains("?")) {
            return url + "&" + encodedParams;
        } else {
            return url + "?" + encodedParams;
        }
    }

    private String encodeParams(Map<String, String> params) {
        return params.entrySet().stream()
                .map(entry -> {
                    String key = URLEncoder.encode(entry.getKey(), StandardCharsets.UTF_8);
                    String value = URLEncoder.encode(entry.getValue(), StandardCharsets.UTF_8);
                    return key + "=" + value;
                })
                .collect(Collectors.joining("&"));
    }

    private static class HttpEntityEnclosingHttpDelete extends HttpEntityEnclosingRequestBase {
        public HttpEntityEnclosingHttpDelete(final String uri) {
            super();
            setURI(URI.create(uri));
        }

        @Override
        public String getMethod() {
            return HttpDelete.METHOD_NAME;
        }
    }
}

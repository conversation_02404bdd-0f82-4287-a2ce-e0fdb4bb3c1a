package com.angelalign.tas.common.http;

import com.google.common.collect.Lists;
import lombok.Builder;
import lombok.Getter;
import lombok.ToString;

import javax.annotation.Nullable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Builder
@ToString
@Getter
public class HttpRequestParams {
    @Nullable
    private String authorization;
    @Nullable
    private String contentType;
    @Builder.Default
    private Map<String, String> queries = new HashMap<>();
    @Nullable
    private String body;
    @Builder.Default
    private List<String> pathVariables = Lists.newArrayList();
    @Builder.Default
    private Map<String, String> urlEncodedForm = new HashMap<>();
}

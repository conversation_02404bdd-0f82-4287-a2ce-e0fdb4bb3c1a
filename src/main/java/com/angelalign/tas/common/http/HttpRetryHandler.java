package com.angelalign.tas.common.http;

import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.client.HttpRequestRetryHandler;
import org.apache.http.protocol.HttpContext;
import org.apache.http.protocol.HttpCoreContext;

import java.io.IOException;

public class HttpRetryHandler implements HttpRequestRetryHandler {

    private final int maxRetries; //最大重试次数
    private final long initialInterval;//初始等待时间，单位为毫秒
    private final double multiplier; //每次重试的等待时间倍增因子

    public HttpRetryHandler(int maxRetries, long initialInterval, double multiplier) {
        this.maxRetries = maxRetries;
        this.initialInterval = initialInterval;
        this.multiplier = multiplier;
    }


    @Override
    public boolean retryRequest(IOException exception, int executionCount, HttpContext context) {
        if (executionCount > maxRetries) {
            return false;
        }

        HttpRequest request = (HttpRequest) context.getAttribute(HttpCoreContext.HTTP_REQUEST);
        boolean idempotent = !(request instanceof HttpEntityEnclosingRequest);

        if (idempotent) {
            try {
                long interval = (long) (initialInterval * Math.pow(multiplier, executionCount - 1));
                Thread.sleep(interval);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                return false;
            }
            return true;
        }
        return false;
    }
}

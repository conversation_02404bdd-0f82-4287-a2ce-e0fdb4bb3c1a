package com.angelalign.tas.common.gms;

import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;

@Getter
@AllArgsConstructor
public enum MtsServerEndpoint {
    GET_MTS_SOLUTION(HttpPost.METHOD_NAME, "/api/task/assigment/getTaskAssignee"),
    ;

    private final String httpMethod;
    private final String uri;

}




package com.angelalign.tas.common.gms;

import com.angelalign.tas.common.http.HttpRequestParams;
import com.angelalign.tas.rest.vo.SolutionVo;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import com.angelalign.tas.util.Jackson;
import com.fasterxml.jackson.databind.JsonNode;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.shidaits.common.security.service.impl.SecurityServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.concurrent.TimeUnit;

@Service
@Slf4j
public class MtsServerProxy {
    @Autowired
    private MtsServerClient mtsServerClient;
    private final String AUTH_PREFIX = "Bearer ";

    @Value("${gms.point.account:shihm}")
    private String account;

    @Value("${gms.point.password:123456}")
    private String password;

    @Value("${gms.point.cas-url:https://cas-ad.share.eainc.com:8443/cas/v1/tickets}")
    private String casUrl;

    @Value("${gms.point.auth-uri:/shidaits/shiro-cas}")
    private String authUri;

    @Value("${gms.point.cookieName:JSESSIONID}")
    private String cookieName;

    protected static final Cache<String, String> tokenMap = Caffeine.newBuilder()
            .expireAfterWrite(30, TimeUnit.MINUTES)
            .maximumSize(10).build();

    /**
     * 根据配置账号获取Token
     *
     * @param host 地址
     * @return Token
     */
    private synchronized String geToken(String host) {
        return tokenMap.get(host, key -> {
            SecurityServiceImpl securityService = new SecurityServiceImpl();
            securityService.setCasServerUrl(casUrl);
            securityService.setCookieName(cookieName);
            securityService.setServiceUrl(host + authUri);
            securityService.setUsername(account);
            securityService.setPassword(password);
            try {
                log.info("start to get token for MTS");
                String savedToken = securityService.getSessionId();
                log.info("finish getting token for MTS");
                return AUTH_PREFIX + savedToken;
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return null;
            }
        });
    }

    /**
     * 获取输入的id在mts现在的负责人id
     *
     * @param callBackUrl 回调的地址，会推断出mts的host
     * @param taskId      工单id 的列表
     * @return 在mts 的分配结果
     */
    public List<AssignedTaskVo> getMtsSolution(String callBackUrl, List<Long> taskId) {
        String host = parseMtsServerUrl(callBackUrl);
        HttpRequestParams build = HttpRequestParams.builder()
                .body(Jackson.toJsonString(taskId))
                .contentType(MediaType.APPLICATION_JSON_VALUE)
                .authorization(geToken(host))
                .build();

        String access = mtsServerClient.access(host + MtsServerEndpoint.GET_MTS_SOLUTION.getUri()
                , MtsServerEndpoint.GET_MTS_SOLUTION.getHttpMethod()
                , build);
        JsonNode jsonNode1 = Jackson.jsonNodeOf(access).get("data");
        if (jsonNode1.isArray()) {
            return Jackson.covertObjectListFromJsonString(jsonNode1.toString(), AssignedTaskVo.class);
        }
        return List.of();
    }

    private String parseMtsServerUrl(String callBackUrl) {
        if (callBackUrl == null || callBackUrl.trim().isEmpty()) {
            return "";
        }

        try {
            // 使用URI来解析URL
            URI uri = new URI(callBackUrl);

            // 构建基础URL：协议 + 主机 + 端口（如果有）
            StringBuilder baseUrl = new StringBuilder();
            baseUrl.append(uri.getScheme()).append("://").append(uri.getHost());

            // 如果有端口且不是默认端口，则添加端口
            int port = uri.getPort();
            if (port != -1 &&
                !((uri.getScheme().equals("http") && port == 80) ||
                  (uri.getScheme().equals("https") && port == 443))) {
                baseUrl.append(":").append(port);
            }

            return baseUrl.toString();

        } catch (URISyntaxException e) {
            // 如果URI解析失败，使用字符串处理方式作为备选方案
            return parseUrlByString(callBackUrl);
        }
    }

    /**
     * 备选的字符串解析方式
     */
    private String parseUrlByString(String callBackUrl) {
        try {
            // 查找第三个斜杠的位置（协议后的第一个斜杠）
            int protocolEnd = callBackUrl.indexOf("://");
            if (protocolEnd == -1) {
                return "";
            }

            // 从协议结束后开始查找路径
            int pathStart = callBackUrl.indexOf("/", protocolEnd + 3);
            if (pathStart == -1) {
                // 如果没有路径，整个URL就是基础URL
                return callBackUrl;
            }

            // 返回从开始到路径之前的部分
            return callBackUrl.substring(0, pathStart);

        } catch (Exception e) {
            return "";
        }
    }

}

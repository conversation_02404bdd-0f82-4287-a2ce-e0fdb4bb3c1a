package com.angelalign.tas.common.callback;


import com.angelalign.tas.common.http.HttpClient;
import com.angelalign.tas.common.http.HttpRequest;
import com.angelalign.tas.common.http.HttpRequestParams;
import com.angelalign.tas.common.http.HttpResponse;
import com.google.common.net.HttpHeaders;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

@Slf4j
@Component
public class CallBackClient {
    @Autowired
    private HttpClient httpClient;

    public String access(String endpointHost, String method, HttpRequestParams requestParams) {
        HttpResponse response;
        Map<String, String> headers = new HashMap<>();
        Map<String, String> queries = null;
        String body = null;
        if (Objects.nonNull(requestParams)) {
            if (Objects.nonNull(requestParams.getAuthorization())) {
                headers.put(HttpHeaders.AUTHORIZATION, requestParams.getAuthorization());
            }
            if (Objects.nonNull(requestParams.getContentType())) {
                headers.put(HttpHeaders.CONTENT_TYPE, requestParams.getContentType());
            }
            if (MapUtils.isNotEmpty(requestParams.getQueries())) {
                queries = requestParams.getQueries();
            }
            body = Objects.nonNull(requestParams.getBody()) ? requestParams.getBody() : null;
        }
        HttpRequest request = HttpRequest.builder()
                .method(method)
                .url(gmsUriBuilder(endpointHost, requestParams.getPathVariables()))
                .queries(queries)
                .headers(headers)
                .urlEncodedForm(requestParams.getUrlEncodedForm())
                .body(body)
                .build();
        try {
            response = httpClient.send(request);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new RuntimeException(e);
        }
        log.info("accessed callBack. response= {}", response);
        return response.getBody();
    }

    private String gmsUriBuilder(String uri, List<String> pathVariables) {
        if (CollectionUtils.isEmpty(pathVariables)) {
            return uri;
        }
        return String.format(uri, pathVariables.toArray());
    }
}

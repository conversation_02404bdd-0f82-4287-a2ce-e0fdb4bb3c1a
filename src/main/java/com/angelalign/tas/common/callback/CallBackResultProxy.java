package com.angelalign.tas.common.callback;


import com.angelalign.tas.common.http.HttpRequestParams;
import com.angelalign.tas.rest.vo.SolutionVo;
import com.angelalign.tas.util.Jackson;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class CallBackResultProxy {
    @Autowired
    private CallBackClient callBackClient;


    public void callBack(String url, SolutionVo solutionVo) {
        try {
            HttpRequestParams build = HttpRequestParams.builder()
                    .body(Jackson.toJsonString(solutionVo))
                    .contentType(MediaType.APPLICATION_JSON_VALUE)
                    .build();
            callBackClient.access(url, "POST", build);
        } catch (Exception e) {
            log.error("callBack error: {}", e.getMessage(), e);
        }
    }
}
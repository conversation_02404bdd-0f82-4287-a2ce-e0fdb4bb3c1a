package com.angelalign.tas.score.base.util;

import com.angelalign.tas.score.base.util.math.MathematicalFunctionUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import static org.junit.jupiter.api.Assertions.*;

/**
 * MarginalCalculateUtil 测试类
 * 主要测试 calculateScaledRewardByCapacity 方法的边际递减效果
 */
public class MathematicalFunctionUtilTest {

    @Test
    @DisplayName("测试 calculateScaledRewardByCapacity 方法的边际递减效果")
    public void testCalculateScaledRewardByCapacity() {
        System.out.println("=== 测试 calculateScaledRewardByCapacity 边际递减效果 ===");
        
        // 测试数据：模拟你提到的输入值
        double[] testInputs = {1.0, 1.1, 1.2, 1.256, 1.3, 1.4, 1.427, 1.438, 1.5, 1.6, 1.7, 1.8, 1.9, 2.0};
        int targetRange = 100;
        
        System.out.println("输入值\t\t输出值\t\t边际增量\t累计增量");
        System.out.println("------------------------------------------------------------");
        
        double previousOutput = 0;
        double previousMarginalIncrease = 0;
        
        for (int i = 0; i < testInputs.length; i++) {
            double input = testInputs[i];
            double output = MathematicalFunctionUtil.calculateScaledRewardByCapacity(input, null, targetRange);
            
            double marginalIncrease = output - previousOutput;
            double totalIncrease = output - (i > 0 ? MathematicalFunctionUtil.calculateScaledRewardByCapacity(testInputs[0], null, targetRange) : 0);
            
            System.out.printf("%.3f\t\t%.6f\t%.6f\t%.6f%n", 
                input, output, marginalIncrease, totalIncrease);
            
            // 验证边际递减效果：后面的边际增量应该小于前面的
            if (i > 1) {
                assertTrue(marginalIncrease <= previousMarginalIncrease + 0.001, // 允许小的数值误差
                    String.format("边际递减失效：输入%.3f的边际增量(%.6f)应该小于等于输入%.3f的边际增量(%.6f)", 
                        input, marginalIncrease, testInputs[i-1], previousMarginalIncrease));
            }
            
            previousOutput = output;
            previousMarginalIncrease = marginalIncrease;
        }
    }

    @Test
    @DisplayName("测试特定区间的敏感性")
    public void testSensitivityInSpecificRange() {
        System.out.println("\n=== 测试特定区间的敏感性 ===");
        
        // 测试你提到的具体数值
        double input1 = 1.2;
        double input2 = 1.256;
        double input3 = 1.427;
        double input4 = 1.438;
        
        double output1 = MathematicalFunctionUtil.calculateScaledRewardByCapacity(input1, null, 100);
        double output2 = MathematicalFunctionUtil.calculateScaledRewardByCapacity(input2, null, 100);
        double output3 = MathematicalFunctionUtil.calculateScaledRewardByCapacity(input3, null, 100);
        double output4 = MathematicalFunctionUtil.calculateScaledRewardByCapacity(input4, null, 100);
        
        double diff1 = output2 - output1; // 1.256 - 1.2 = 0.056 的差异
        double diff2 = output4 - output3; // 1.438 - 1.427 = 0.011 的差异
        
        System.out.printf("输入1.2 -> 输出: %.6f%n", output1);
        System.out.printf("输入1.256 -> 输出: %.6f (差异: %.6f)%n", output2, diff1);
        System.out.printf("输入1.427 -> 输出: %.6f%n", output3);
        System.out.printf("输入1.438 -> 输出: %.6f (差异: %.6f)%n", output4, diff2);
        
        // 验证：输入差异大的应该有更大的输出差异
        double inputDiff1 = 1.256 - 1.2;   // 0.056
        double inputDiff2 = 1.438 - 1.427; // 0.011
        
        System.out.printf("输入差异比例: %.3f : %.3f = %.2f : 1%n", 
            inputDiff1, inputDiff2, inputDiff1 / inputDiff2);
        System.out.printf("输出差异比例: %.6f : %.6f = %.2f : 1%n", 
            diff1, diff2, diff1 / diff2);
        
        // 验证敏感性：更大的输入差异应该产生更大的输出差异
        assertTrue(diff1 > diff2, 
            String.format("敏感性测试失败：输入差异%.3f应该产生比输入差异%.3f更大的输出差异", 
                inputDiff1, inputDiff2));
    }

    @Test
    @DisplayName("测试边界值")
    public void testBoundaryValues() {
        System.out.println("\n=== 测试边界值 ===");
        
        // 测试边界值
        double result0 = MathematicalFunctionUtil.calculateScaledRewardByCapacity(0, null, 100);
        double resultNegative = MathematicalFunctionUtil.calculateScaledRewardByCapacity(-1, null, 100);
        double resultVerySmall = MathematicalFunctionUtil.calculateScaledRewardByCapacity(0.001, null, 100);
        double resultVeryLarge = MathematicalFunctionUtil.calculateScaledRewardByCapacity(100, null, 100);
        
        System.out.printf("输入0 -> 输出: %.6f%n", result0);
        System.out.printf("输入-1 -> 输出: %.6f%n", resultNegative);
        System.out.printf("输入0.001 -> 输出: %.6f%n", resultVerySmall);
        System.out.printf("输入100 -> 输出: %.6f%n", resultVeryLarge);
        
        // 验证边界值行为
        assertEquals(0, result0, "输入0应该返回0");
        assertEquals(0, resultNegative, "负数输入应该返回0");
        assertTrue(resultVerySmall > 0, "很小的正数应该返回正值");
        assertTrue(resultVeryLarge <= 100, "输出应该不超过目标范围");
        assertTrue(resultVeryLarge > resultVerySmall, "大输入应该产生大输出");
    }

    @Test
    @DisplayName("测试不同目标范围")
    public void testDifferentTargetRanges() {
        System.out.println("\n=== 测试不同目标范围 ===");
        
        double input = 1.5;
        int[] targetRanges = {50, 100, 150, 200};
        
        System.out.println("目标范围\t输出值\t\t比例");
        System.out.println("--------------------------------");
        
        for (int targetRange : targetRanges) {
            double output = MathematicalFunctionUtil.calculateScaledRewardByCapacity(input, null, targetRange);
            double ratio = output / targetRange;
            
            System.out.printf("%d\t\t%.6f\t%.4f%n", targetRange, output, ratio);
            
            // 验证输出不超过目标范围
            assertTrue(output <= targetRange, 
                String.format("输出%.6f不应该超过目标范围%d", output, targetRange));
        }
    }
}

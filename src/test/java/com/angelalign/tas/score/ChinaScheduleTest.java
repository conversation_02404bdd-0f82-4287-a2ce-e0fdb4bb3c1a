package com.angelalign.tas.score;

import com.angelalign.tas.domain.assignment.enums.CaseAllocateTypeEnum;
import com.angelalign.tas.domain.parser.model.PreferTask;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import com.angelalign.tas.rest.vo.support.MedTagVo;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.FlexibleProblemGenerator;
import com.angelalign.tas.util.TasUnitTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
@Transactional
public class ChinaScheduleTest {

    @Test
    @Timeout(600_000)
    // 1、 设计师必须有可用的产能
    public void mustHasAvailableCapacity() {
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .solverName(SolverTypeEnum.CHINA_DESIGN_SCHEDULE.name())
                .tasks(3)
                .designers(2)
                .defaultDesignerCapacity(0) // 设置默认产能
                .defaultCapacityRatio(0.3f) // 产能有0.3的浮动
                .defaultCapacityAllocationType(CaseAllocateTypeEnum.MIXED_TASK_QUANTITY)
                // 生成的两个设计师全都没有任产能
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(0)
                                .code("designer1")
                                .enableAllSkills()
                )
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(0)
                                .code("designer2")
                                .enableAllSkills()
                )
                .skills(2)
                .build();
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo, false);
        Assertions.assertEquals(0, assignedTaskVos.size());
    }

    @Test
    @Timeout(600_000)
    // 2、必须有可用的产能，但是优先填满设计师的产能是有特权
    public void mustHasAvailableCapacityFillCapacityPrivilege() {
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .tasks(3)
                .solverName(SolverTypeEnum.OVERSEA_DESIGN_SCHEDULE.name())
                .designers(1)
                .defaultDesignerCapacity(0)
                .defaultCapacityRatio(0)
                .defaultCapacityAllocationType(CaseAllocateTypeEnum.MIXED_TASK_QUANTITY)
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(1)
                                .code("designer1")
                                .enableAllSkills()
                )
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(1)
                                .code("designer2")
                                .enableAllSkills()
                )
                // 一共2个设计有2个额度，所有的工单需要2.5个
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .requireQuotaCount(0.5f)
                                .code("task1")
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .requireQuotaCount(1f)
                                .code("task2")
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .requireQuotaCount(1f)
                                .code("task2")
                )
                .skills(1)
                .build();
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        // 全部分配
        Assertions.assertEquals(3, assignedTaskVos.size());
    }

    @Test
    @Timeout(600_000)
    // 3、病例指定分配的设计师 如果只有难度标签不匹配允许分配
    public void caseAssigmentDesignerDifficultyTagPrivilege() {
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .tasks(3)
                .solverName(SolverTypeEnum.CHINA_DESIGN_SCHEDULE.name())
                .designers(1)
                .defaultDesignerCapacity(10)
                .defaultCapacityRatio(0.3f)
                .defaultCapacityAllocationType(CaseAllocateTypeEnum.MIXED_TASK_QUANTITY)
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .code("designer1")
                                // 这个设计师没有难度的技能
                                .withSkill("2")
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .preferDesigners("1")
                                .requireSkill(List.of("1", "2"))
                                .code("task1")
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .preferDesigners("1")
                                .requireSkill(List.of("1", "2"))
                                .code("task2")
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .preferDesigners("1")
                                .requireSkill(List.of("1", "2"))
                                .code("task3")
                )
                .withSkill(FlexibleProblemGenerator.SkillConfig.create()
                        .id(1)
                        .code("ORDER_TAG-NEW_B_LEVEL")
                )
                .withSkill(FlexibleProblemGenerator.SkillConfig.create()
                        .id(2)
                        .code("other skill")
                )
                .skills(1)
                .build();
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        // 全部分配
        Assertions.assertEquals(3, assignedTaskVos.size());
    }

    @Test
    @Timeout(600_000)
    // 设计师优先工单的数量不能超上限
    public void designerPreferTaskLimitedPenalizeConstraint() {
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .tasks(3)
                .solverName(SolverTypeEnum.CHINA_DESIGN_SCHEDULE.name())
                .designers(1)
                .defaultCapacityRatio(0.0f)
                .defaultCapacityAllocationType(CaseAllocateTypeEnum.MIXED_TASK_QUANTITY)
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(5)
                                .code("designer1")
                                .enableAllSkills()
                                // 设计师片偏好工单是A 或者 B
                                .preferTaskExpression(List.of(PreferTask
                                        .builder()
                                        .expression("(A OR B)")
                                        .quota(0.2)
                                        .build()))
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .code("task1")
                                .requireQuotaCount(1f)
                                .orderTag(List.of(
                                        MedTagVo.builder()
                                                .code("A")
                                                .build()))
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .code("task2")
                                .requireQuotaCount(1f)
                                .orderTag(List.of(
                                        MedTagVo.builder()
                                                .code("B")
                                                .build()))
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .code("task3")
                                .requireQuotaCount(1f)
                                .orderTag(List.of(
                                        MedTagVo.builder()
                                                .code("A")
                                                .build()))
                )
                .skills(1)
                .build();
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        // 不能超过他的额度，应该只能分掉1个
        Assertions.assertEquals(1, assignedTaskVos.size());
    }

    @Test
    @Timeout(600_000)
    // 优先工单先分到约束
    public void preferTaskPriorityRewardConstraint() {
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .tasks(5)
                .solverName(SolverTypeEnum.CHINA_DESIGN_SCHEDULE.name())
                .designers(1)
                .defaultCapacityRatio(0.0f)
                .defaultCapacityAllocationType(CaseAllocateTypeEnum.MIXED_TASK_QUANTITY)
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(10)
                                .code("designer1")
                                .enableAllSkills()
                                // 偏好的技能
                                .preferSkillExpression(List.of("(C OR D)"))
                                // 设计师片偏好工单是A 或者 B
                                .preferTaskExpression(List.of(PreferTask
                                        .builder()
                                        .expression("(A OR B)")
                                        .quota(1d)
                                        .build()))
                )
                .withTask(
                        // 这个工单每次必须存在
                        FlexibleProblemGenerator.TaskConfig.create()
                                .code("task1")
                                .requireQuotaCount(1f)
                                .orderTag(List.of(
                                        MedTagVo.builder()
                                                .code("A")
                                                .build()))
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .code("task2")
                                .requireQuotaCount(1f)
                                .orderTag(List.of(
                                        MedTagVo.builder()
                                                .code("C")
                                                .build()))
                )
                .skills(1)
                .build();
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        List<AssignedTaskVo> list = assignedTaskVos.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getTaskCode().equalsIgnoreCase("task1"))
                .toList();
        Assertions.assertEquals(1, list.size());
    }

    @Test
    @Timeout(600_000)
    // 优先填满设计师的偏好技能
    public void preferSkillPriorityRewardConstraint() {
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .tasks(2)
                .solverName(SolverTypeEnum.CHINA_DESIGN_SCHEDULE.name())
                .designers(1)
                .defaultCapacityRatio(0.0f)
                .defaultCapacityAllocationType(CaseAllocateTypeEnum.MIXED_TASK_QUANTITY)
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(1)
                                .code("designer1")
                                .enableAllSkills()
                                // 设计师的偏好技能是C 或者D
                                .preferSkillExpression(List.of("(C OR D)"))
                )
                .withTask(
                        // 这个工单每次必须存在
                        FlexibleProblemGenerator.TaskConfig.create()
                                .code("task1")
                                .requireQuotaCount(1f)
                                .orderTag(List.of(
                                        MedTagVo.builder()
                                                .code("A")
                                                .build()))
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .code("task2")
                                .requireQuotaCount(1f)
                                .orderTag(List.of(
                                        MedTagVo.builder()
                                                .code("C")
                                                .build()))
                )
                .skills(1)
                .build();
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        List<AssignedTaskVo> list = assignedTaskVos.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getTaskCode().equalsIgnoreCase("task2"))
                .toList();
        Assertions.assertEquals(1, list.size());
    }

    @Test
    @Timeout(600_000)
    // 不同员工的工作量按照日额度均衡
    public void designerBalanceByCapacityConstraint() {
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .tasks(25)
                .solverName(SolverTypeEnum.CHINA_DESIGN_SCHEDULE.name())
                .designers(1)
                .defaultCapacityRatio(0.0f)
                .defaultCapacityAllocationType(CaseAllocateTypeEnum.MIXED_TASK_QUANTITY)
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(10)
                                .code("designer1")
                                .enableAllSkills()
                )
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(15)
                                .code("designer2")
                                .enableAllSkills()
                )
                .skills(1)
                .build();
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        // 工单全部分配
        Assertions.assertEquals(25, assignedTaskVos.size());

        Map<String, List<AssignedTaskVo>> collect = assignedTaskVos.stream().collect(Collectors.groupingBy(AssignedTaskVo::getDesignerCode));
        // 每个设计师都有分配工单
        Assertions.assertEquals(2, collect.size());
        List<Integer> list = collect.values().stream()
                .map(List::size)
                .sorted(Comparator.comparing(Integer::intValue).reversed())
                .toList();

        float radio = list.get(0) / (float) list.get(1);
        Assertions.assertEquals(2, radio, 0.5);
    }

    @Test
    @Timeout(600_000)
    // 设计师必须有可用的产能，但是病例指定分配的设计师是有特权
    public void mustHasAvailableCapacityPreferDesignerPrivilege() {
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .tasks(3)
                .solverName(SolverTypeEnum.OVERSEA_DESIGN_SCHEDULE.name())
                .designers(1)
                .defaultDesignerCapacity(0)
                .defaultCapacityRatio(0)
                .defaultCapacityAllocationType(CaseAllocateTypeEnum.MIXED_TASK_QUANTITY)
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .id(1L)
                                // 但是没有任何产能
                                .capacity(0)
                                .code("designer1")
                                // 设计师有全部的技能
                                .enableAllSkills()
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .preferDesigners("1")
                                .code("task1")
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .preferDesigners("1")
                                .code("task2")
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .preferDesigners("1")
                                .code("task3")
                )
                .skills(1)
                .build();
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        // 全部分配
        Assertions.assertEquals(3, assignedTaskVos.size());
    }
}

package com.angelalign.tas.score;

import com.angelalign.tas.domain.assignment.enums.CaseAllocateTypeEnum;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import com.angelalign.tas.domain.assignment.enums.EntityAttributeKey;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.FlexibleProblemGenerator;
import com.angelalign.tas.util.TasUnitTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
@Transactional
public class ChinaRandomInspectionConstraintTest {
    @Test
    @Timeout(600_000)
    // 1、 设计师必须有可用的产能
    public void mustHasAvailableCapacity() {
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .solverName(SolverTypeEnum.CHINA_RANDOM_INSPECTION_SCHEDULE.name())
                .tasks(3)
                .designers(2)
                .defaultDesignerCapacity(0) // 设置默认产能
                .defaultCapacityRatio(0.3f) // 产能有0.3的浮动
                .defaultCapacityAllocationType(CaseAllocateTypeEnum.MIXED_TASK_QUANTITY)
                // 生成的两个设计师全都没有任产能
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(0)
                                .code("designer1")
                                .enableAllSkills()
                )
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(0)
                                .code("designer2")
                                .enableAllSkills()
                )
                .skills(2)
                .build();
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo, false);
        Assertions.assertEquals(0, assignedTaskVos.size());
    }

    @Test
    @Timeout(600_000)
    public void taskCannotAssignSameTeamDesigner() {
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .solverName(SolverTypeEnum.CHINA_RANDOM_INSPECTION_SCHEDULE.name())
                .tasks(3)
                .designers(2)
                .defaultDesignerCapacity(100) // 设置默认产能
                .defaultCapacityRatio(0.3f) // 产能有0.3的浮动
                .defaultCapacityAllocationType(CaseAllocateTypeEnum.MIXED_TASK_QUANTITY)
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .code("designer1")
                                .enableAllSkills()
                                .addAttribute(EntityAttributeKey.DESIGNER_TEAM.getKey(), "team1")
                )
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .code("designer2")
                                .enableAllSkills()
                                .addAttribute(EntityAttributeKey.DESIGNER_TEAM.getKey(), "team2")
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .code("task1")
                                .addAttribute(EntityAttributeKey.TASK_TEAM.getKey(), "team1")
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .code("task2")
                                .addAttribute(EntityAttributeKey.TASK_TEAM.getKey(), "team1")
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .code("task3")
                                .addAttribute(EntityAttributeKey.TASK_TEAM.getKey(), "team2")
                )
                .skills(2)
                .build();
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo, false);
        Assertions.assertEquals(3, assignedTaskVos.size());

        long count = assignedTaskVos.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getDesignerCode().equalsIgnoreCase("designer2"))
                .count();
        Assertions.assertEquals(2, count);
    }
}

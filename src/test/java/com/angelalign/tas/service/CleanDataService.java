package com.angelalign.tas.service;

import com.angelalign.tas.persistence.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class CleanDataService {
    @Autowired
    private TaProblemRepository problemRepository;
    @Autowired
    private TaCaseTaskRepository taCaseTaskRepository;
    @Autowired
    private TaCapacityTaskTypeQuotaRepository capacityTaskTypeQuotaRepository;
    @Autowired
    private TaCapacityRepository capacityRepository;
    @Autowired
    private TaSkillRepository skillRepository;
    @Autowired
    private TaDesignerRepository designerRepository;
    @Autowired
    private TaDentistRepository dentistRepository;
    @Autowired
    private TaMedTagRepository medTagRepository;
    @Autowired
    private TaTaskTypeRepository taskTypeRepository;
    @Autowired
    private TaTaskRepository taskRepository;

    public void cleanData() {
//        CaseTaskService caseTaskService = ApplicationContextHolder.getBean(CaseTaskService.class);
//        Problem query = new Problem();
//        query.setClient("test");
//        List<Problem> allTestProblem = problemRepository.findAll(Example.of(query));
//        // problem Id
//        List<Long> problemId = allTestProblem.stream().map(Problem::getId).toList();
//        problemRepository.deleteAll(allTestProblem);
//        // designer id
//        List<Long> designerIds = allTestProblem
//                .stream()
//                .map(problem1 -> problem1.getDesigners().stream().map(Designer::getId).toList())
//                .flatMap(List::stream)
//                .toList();
//        // dentist id
//        List<Long> dentistsId = allTestProblem
//                .stream()
//                .map(problem1 -> problem1.getDentists().stream().map(Dentist::getId).toList())
//                .flatMap(List::stream)
//                .toList();
//        // task id
//        List<Long> taskId = allTestProblem
//                .stream()
//                .map(problem1 -> problem1.getTasks().stream().map(Task::getId).toList())
//                .flatMap(List::stream)
//                .toList();
//        // taskType id
//        List<Long> taskTypeId = allTestProblem
//                .stream()
//                .map(problem -> problem.getTaskTypes().stream().map(TaskType::getId).toList())
//                .flatMap(List::stream)
//                .toList();
//        // skill id
//        List<Long> skillId = allTestProblem
//                .stream()
//                .map(problem -> problem.getSkills().stream().map(Skill::getId).toList())
//                .flatMap(List::stream)
//                .toList();
//        // medTag id
//        List<Long> medTagId = allTestProblem
//                .stream()
//                .map(problem -> problem.getMedTags().stream().map(MedTag::getId).toList())
//                .flatMap(List::stream)
//                .toList();
//        // capacity id
//        List<Long> capacityId = allTestProblem.stream().map(problem -> problem.getDesigners()
//                        .stream()
//                        .filter(designer -> designer.getCapacity() != null)
//                        .map(designer -> designer.getCapacity().getId())
//                        .toList())
//                .flatMap(List::stream)
//                .toList();
//        List<Long> taskTypeQuotaIds = allTestProblem.stream().map(problem -> problem.getDesigners()
//                        .stream()
//                        .filter(designer -> designer.getCapacity() != null)
//                        .filter(designer -> designer.getCapacity().getCapacityTaskTypeQuotas() != null)
//                        .map(designer -> designer.getCapacity().getCapacityTaskTypeQuotas())
//                        .map(capacityTaskTypeQuotas -> capacityTaskTypeQuotas.stream().map(CapacityTaskTypeQuota::getId).toList())
//                        .flatMap(List::stream)
//                        .toList())
//                .flatMap(List::stream)
//                .toList();
//        List<Long> caseTaskIds = allTestProblem
//                .stream()
//                .map(problem -> problem.getTasks()
//                        .stream()
//                        .map(Task::getCaseCode)
//                        .distinct()
//                        .map(caseTaskService::findAndSortByCaseCode)
//                        .flatMap(List::stream)
//                        .map(CacheCaseTask::getId)
//                        .toList())
//                .flatMap(List::stream)
//                .toList();

//        skillRepository.deleteAllByIdInBatch(skillId);
//        dentistRepository.deleteAllByIdInBatch(dentistsId);
//        medTagRepository.deleteAllByIdInBatch(medTagId);
//        designerRepository.deleteAllByIdInBatch(designerIds);
//        capacityTaskTypeQuotaRepository.deleteAllByIdInBatch(taskTypeQuotaIds);
//        caseTaskRepository.deleteAllByIdInBatch(caseTaskIds);
//        taskTypeRepository.deleteAllByIdInBatch(taskTypeId);
//        taskRepository.deleteAllByIdInBatch(taskId);
//        caseTaskRepository.deleteAllByIdInBatch(caseTaskIds);
//        capacityRepository.deleteAllByIdInBatch(capacityId);
//        problemRepository.deleteAllByIdInBatch(problemId);
    }
}

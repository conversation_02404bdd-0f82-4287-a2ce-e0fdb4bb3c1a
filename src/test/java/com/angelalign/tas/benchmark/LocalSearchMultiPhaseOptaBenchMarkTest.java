package com.angelalign.tas.benchmark;

import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.domain.assignment.Task;
import com.angelalign.tas.service.ProblemService;
import com.angelalign.tas.solver.OptaSolverConfigProvider;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.solver.version.SolverVersionRouterFactory;
import com.angelalign.tas.solver.version.base.SolverVersionRouter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.optaplanner.benchmark.api.PlannerBenchmark;
import org.optaplanner.benchmark.api.PlannerBenchmarkFactory;
import org.optaplanner.benchmark.config.PlannerBenchmarkConfig;
import org.optaplanner.benchmark.config.SolverBenchmarkConfig;
import org.optaplanner.benchmark.config.ranking.SolverRankingType;
import org.optaplanner.benchmark.config.report.BenchmarkReportConfig;
import org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicPhaseConfig;
import org.optaplanner.core.config.heuristic.selector.entity.EntitySelectorConfig;
import org.optaplanner.core.config.heuristic.selector.entity.EntitySorterManner;
import org.optaplanner.core.config.heuristic.selector.move.generic.ChangeMoveSelectorConfig;
import org.optaplanner.core.config.heuristic.selector.value.ValueSelectorConfig;
import org.optaplanner.core.config.heuristic.selector.value.ValueSorterManner;
import org.optaplanner.core.config.localsearch.LocalSearchPhaseConfig;
import org.optaplanner.core.config.localsearch.decider.acceptor.LocalSearchAcceptorConfig;
import org.optaplanner.core.config.localsearch.decider.forager.FinalistPodiumType;
import org.optaplanner.core.config.localsearch.decider.forager.LocalSearchForagerConfig;
import org.optaplanner.core.config.localsearch.decider.forager.LocalSearchPickEarlyType;
import org.optaplanner.core.config.phase.PhaseConfig;
import org.optaplanner.core.config.phase.custom.CustomPhaseConfig;
import org.optaplanner.core.config.solver.SolverConfig;
import org.optaplanner.core.config.solver.termination.TerminationConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
@Transactional
public class LocalSearchMultiPhaseOptaBenchMarkTest {
    private final OptaSolverConfigProvider optaSolverConfigProvider = new OptaSolverConfigProvider();
    @Autowired
    private ProblemService problemService;
    @Autowired
    private SolverVersionRouterFactory solverVersionRouterFactory;

    @Test
    @Timeout(60000_000)
    public void benchMark() {
        int times = 1;

        // 获取测试数据集
        Problem problem = problemService.getProblemById(432631L).orElseThrow();
//        Problem problem1 = assignmentProblemService.getProblemById(1124L).orElseThrow();

        SolverTypeEnum solverTypeEnum = SolverTypeEnum.findSolverByName(problem.getSolverName());
        SolverVersionRouter router = solverVersionRouterFactory.getRouter(problem.getSolverName());
        SolverConfig defaultSolverConfig = optaSolverConfigProvider.getDefaultSolverConfig(problem, router);


        SolverConfig fakerDesignerConfig = defaultSolverConfig.copyConfig();
        modifyLocalSearchMultiPhase(solverTypeEnum, 500, fakerDesignerConfig, problem);
        SolverConfig fakerDesignerConfig1 = defaultSolverConfig.copyConfig();
        modifyLocalSearchMultiPhase(solverTypeEnum, 1000, fakerDesignerConfig, problem);
        // 准备基准测试配置集合
        List<SolverBenchmarkConfig> benchmarkConfigs = new ArrayList<>();
        for (int i = 0; i < times; i++) {
            // 默认配置
            benchmarkConfigs.add(createBenchmarkConfig(
                    "默认soft1000hard2" + i,
                    defaultSolverConfig
            ));

            // 多本地搜索阶段
            benchmarkConfigs.add(createBenchmarkConfig(
                    "500温度hard0" + i,
                    fakerDesignerConfig
            ));
            benchmarkConfigs.add(createBenchmarkConfig(
                    "1000温度hard0" + i,
                    fakerDesignerConfig1
            ));
        }

        // 创建基准测试总配置
        PlannerBenchmarkConfig benchmarkConfig = new PlannerBenchmarkConfig();
        benchmarkConfig.setSolverBenchmarkConfigList(benchmarkConfigs);
        // 设置输出目录
        benchmarkConfig.setBenchmarkDirectory(new File("target/benchmarks"));
        // 预热 十秒
        benchmarkConfig.setWarmUpSecondsSpentLimit(10L);
        BenchmarkReportConfig benchmarkReportConfig = new BenchmarkReportConfig();

        benchmarkReportConfig.setSolverRankingType(SolverRankingType.TOTAL_SCORE);
        benchmarkConfig.setBenchmarkReportConfig(benchmarkReportConfig);
        PlannerBenchmarkFactory benchmarkFactory = PlannerBenchmarkFactory.create(benchmarkConfig);

        // 执行基准测试
        PlannerBenchmark plannerBenchmark = benchmarkFactory.buildPlannerBenchmark(problem.convertToSolution());
        plannerBenchmark.benchmark();
    }

    private SolverBenchmarkConfig createBenchmarkConfig(String name, SolverConfig config) {
        SolverBenchmarkConfig benchmarkConfig = new SolverBenchmarkConfig();
        benchmarkConfig.setName(name);
        benchmarkConfig.setSolverConfig(config);
        return benchmarkConfig;
    }

    private void modifyLocalSearchMultiPhase(SolverTypeEnum solverTypeEnum, int temp, SolverConfig config, Problem problem) {
        ArrayList<PhaseConfig> phaseConfigs = new ArrayList<>();

        // 启发构建前阶段
        CustomPhaseConfig preConstructionHeuristicPhaseConfig = optaSolverConfigProvider.getPreConstructionHeuristicPhaseConfig(solverTypeEnum.getCustomPhaseClass());
        // 启发构建阶段
        ConstructionHeuristicPhaseConfig constructionHeuristicPhaseConfig = optaSolverConfigProvider.getConstructionHeuristicPhaseConfig(problem, 0.1f);
        ConstructionHeuristicPhaseConfig hignPriorityTaskConstructionHeuristicPhaseConfig = optaSolverConfigProvider.getHignPriorityTaskConstructionHeuristicPhaseConfig();
        // 低优先级启发构建阶段
        ConstructionHeuristicPhaseConfig lowPriorityTaskConstructionHeuristicPhaseConfig = optaSolverConfigProvider.getLowPriorityTaskConstructionHeuristicPhaseConfig();
        // 虚拟设计师补偿阶段
        CustomPhaseConfig fakerDesignerCorrectionPhase = optaSolverConfigProvider.getFakerDesignerCorrectionPhase();
        // localSearch 阶段
        LocalSearchPhaseConfig simulatedAnnealingPhaseConfig = optaSolverConfigProvider.getSimulatedAnnealingPhaseConfig_v2(solverTypeEnum.getHardLevelSize()
                , solverTypeEnum.getSoftLevelSize(), problem, temp, 1f);

//        LocalSearchPhaseConfig tabuSearchPhaseConfig = optaSolverConfigProvider.getTabuSearchPhaseConfig(problem, 0.2f);
//        LocalSearchPhaseConfig lateAcceptancePhaseConfig = getLateAcceptancePhaseConfig(problem, 0.3f);
        phaseConfigs.add(preConstructionHeuristicPhaseConfig);
        phaseConfigs.add(hignPriorityTaskConstructionHeuristicPhaseConfig);
        phaseConfigs.add(lowPriorityTaskConstructionHeuristicPhaseConfig);
        phaseConfigs.add(fakerDesignerCorrectionPhase);
        phaseConfigs.add(simulatedAnnealingPhaseConfig);
        phaseConfigs.add(fakerDesignerCorrectionPhase);

        config.setPhaseConfigList(phaseConfigs);
    }

    public LocalSearchPhaseConfig getLateAcceptancePhaseConfig(Problem problem, float ratio) {
        LocalSearchPhaseConfig phaseConfig = new LocalSearchPhaseConfig();

        // 延迟接受核心参数
        LocalSearchAcceptorConfig acceptorConfig = new LocalSearchAcceptorConfig();
        acceptorConfig.setLateAcceptanceSize(problem.getTasks().size() / 2); // 历史窗口大小=总工单数/2
//        acceptorConfig.setLateAcceptance
//        acceptorConfig.setLateAcceptanceThreshold(0.05); // 允许5%的分数衰减
        phaseConfig.setAcceptorConfig(acceptorConfig);

        // 移动选择策略（精细调整）
        ChangeMoveSelectorConfig moveSelectorConfig = new ChangeMoveSelectorConfig()
                .withEntitySelectorConfig(new EntitySelectorConfig()
                        .withEntityClass(Task.class)
                        .withSorterManner(EntitySorterManner.DECREASING_DIFFICULTY))
                .withValueSelectorConfig(new ValueSelectorConfig()
                        .withVariableName("designer")
                        .withSorterManner(ValueSorterManner.DECREASING_STRENGTH));
        phaseConfig.setMoveSelectorConfig(moveSelectorConfig);

        // 候选解管理策略
        LocalSearchForagerConfig foragerConfig = new LocalSearchForagerConfig();
        foragerConfig.setAcceptedCountLimit(500); // 限制内存消耗
        foragerConfig.setPickEarlyType(LocalSearchPickEarlyType.FIRST_LAST_STEP_SCORE_IMPROVING);
        foragerConfig.setFinalistPodiumType(FinalistPodiumType.STRATEGIC_OSCILLATION_BY_LEVEL);
        phaseConfig.setForagerConfig(foragerConfig);
        TerminationConfig terminationConfig = optaSolverConfigProvider.chooseTerminationCondition(problem.getTasks().size()
                , problem.getDesigners().size(), ratio);
        phaseConfig.setTerminationConfig(terminationConfig);
        return phaseConfig;
    }
}

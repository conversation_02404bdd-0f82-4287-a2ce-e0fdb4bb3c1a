package com.angelalign.tas.benchmark;

import com.angelalign.tas.domain.assignment.Problem;
import com.angelalign.tas.service.ProblemService;
import com.angelalign.tas.solver.OptaSolverConfigProvider;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.solver.version.SolverVersionRouterFactory;
import com.angelalign.tas.solver.version.base.SolverVersionRouter;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.optaplanner.benchmark.api.PlannerBenchmark;
import org.optaplanner.benchmark.api.PlannerBenchmarkFactory;
import org.optaplanner.benchmark.config.PlannerBenchmarkConfig;
import org.optaplanner.benchmark.config.SolverBenchmarkConfig;
import org.optaplanner.benchmark.config.ranking.SolverRankingType;
import org.optaplanner.benchmark.config.report.BenchmarkReportConfig;
import org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicPhaseConfig;
import org.optaplanner.core.config.constructionheuristic.ConstructionHeuristicType;
import org.optaplanner.core.config.constructionheuristic.decider.forager.ConstructionHeuristicForagerConfig;
import org.optaplanner.core.config.constructionheuristic.decider.forager.ConstructionHeuristicPickEarlyType;
import org.optaplanner.core.config.heuristic.selector.entity.EntitySorterManner;
import org.optaplanner.core.config.phase.PhaseConfig;
import org.optaplanner.core.config.solver.SolverConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
@Transactional
public class ConstructionHeuristicMultiPhaseTest {
    private final OptaSolverConfigProvider optaSolverConfigProvider = new OptaSolverConfigProvider();
    @Autowired
    private ProblemService problemService;
    @Autowired
    private SolverVersionRouterFactory solverVersionRouterFactory;

    @Test
    @Timeout(60000_000)
    public void benchMark() {

        // 获取测试数据集
        Problem problem = problemService.getProblemById(6039L).orElseThrow();

        SolverTypeEnum solverTypeEnum = SolverTypeEnum.findSolverByName(problem.getSolverName());
        SolverVersionRouter router = solverVersionRouterFactory.getRouter(problem.getSolverName());
        SolverConfig defaultSolverConfig = optaSolverConfigProvider.getDefaultSolverConfig(problem, router);

        // 准备基准测试配置集合
        List<SolverBenchmarkConfig> benchmarkConfigs = new ArrayList<>();

        // 默认配置
        benchmarkConfigs.add(createBenchmarkConfig(
                "DefaultConfig",
                defaultSolverConfig
        ));

        SolverConfig fakerDesignerConfig = defaultSolverConfig.copyConfig();
        modifyConstructionHeuristicMultiPhase(solverTypeEnum.getHardLevelSize(), solverTypeEnum.getSoftLevelSize(), fakerDesignerConfig, problem);

        benchmarkConfigs.add(createBenchmarkConfig(
                "ConstructionHeuristicMultiPhase",
                fakerDesignerConfig
        ));

        // 创建基准测试总配置
        PlannerBenchmarkConfig benchmarkConfig = new PlannerBenchmarkConfig();
        benchmarkConfig.setSolverBenchmarkConfigList(benchmarkConfigs);
        // 设置输出目录
        benchmarkConfig.setBenchmarkDirectory(new File("target/benchmarks"));
        // 预热 十秒
        benchmarkConfig.setWarmUpSecondsSpentLimit(10L);
        BenchmarkReportConfig benchmarkReportConfig = new BenchmarkReportConfig();

        benchmarkReportConfig.setSolverRankingType(SolverRankingType.TOTAL_SCORE);
        benchmarkConfig.setBenchmarkReportConfig(benchmarkReportConfig);
        PlannerBenchmarkFactory benchmarkFactory = PlannerBenchmarkFactory.create(benchmarkConfig);

        // 执行基准测试
        PlannerBenchmark plannerBenchmark = benchmarkFactory.buildPlannerBenchmark(problem.convertToSolution());
        plannerBenchmark.benchmark();
    }

    private SolverBenchmarkConfig createBenchmarkConfig(String name, SolverConfig config) {
        SolverBenchmarkConfig benchmarkConfig = new SolverBenchmarkConfig();
        benchmarkConfig.setName(name);
        benchmarkConfig.setSolverConfig(config);
        return benchmarkConfig;
    }

    private void modifyConstructionHeuristicMultiPhase(int hardLevel, int softLevel, SolverConfig config, Problem problem) {
        List<PhaseConfig> phaseConfigList = config.getPhaseConfigList();
        ArrayList<PhaseConfig> phaseConfigs = new ArrayList<>(phaseConfigList);

        phaseConfigs.remove(1);
        phaseConfigs.remove(2);

        ConstructionHeuristicPhaseConfig lowPriorityTaskConstructionHeuristicPhaseConfig = new ConstructionHeuristicPhaseConfig();
        lowPriorityTaskConstructionHeuristicPhaseConfig.setConstructionHeuristicType(ConstructionHeuristicType.CHEAPEST_INSERTION);
//        lowPriorityTaskConstructionHeuristicPhaseConfig.setTerminationConfig(terminationConfig);

        ConstructionHeuristicForagerConfig constructionHeuristicForagerConfig1 = new ConstructionHeuristicForagerConfig();
        // 第一个可行分数或非恶化困难
        constructionHeuristicForagerConfig1.setPickEarlyType(ConstructionHeuristicPickEarlyType.FIRST_FEASIBLE_SCORE_OR_NON_DETERIORATING_HARD);
        lowPriorityTaskConstructionHeuristicPhaseConfig.setForagerConfig(constructionHeuristicForagerConfig1);
        lowPriorityTaskConstructionHeuristicPhaseConfig.withEntitySorterManner(EntitySorterManner.DECREASING_DIFFICULTY);
        phaseConfigs.add(1, lowPriorityTaskConstructionHeuristicPhaseConfig);
        config.setPhaseConfigList(phaseConfigs);
    }
}

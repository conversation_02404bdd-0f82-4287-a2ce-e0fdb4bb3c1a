package com.angelalign.tas;

import com.angelalign.tas.domain.assignment.enums.CaseAllocateTypeEnum;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import com.angelalign.tas.rest.vo.support.DesignerVo;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.FlexibleProblemGenerator;
import com.angelalign.tas.util.TasUnitTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class TaskAssigmentSimulatorTest {

    @Test
    @Timeout(600_000)
    public void taskAssigmentSimulatorTest() {
        int maxTask = 10;
        int maxDesigner = 5;
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .tasks(maxTask)
                .designers(maxDesigner)
                .defaultDesignerCapacity(12) // 设置默认产能
                .defaultCapacityRatio(0.3f) // 产能有0.3的浮动
                .defaultCapacityAllocationType(CaseAllocateTypeEnum.MIXED_TASK_QUANTITY)
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(20)
                                .code("case designer1")
                                .enableAllSkills() // 掌握所有技能
                )
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(20)
                                .code("case designer2")
                                .enableAllSkills()
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .priority(10) // 最高优先级
                                .code("task case designer")
                                .preferDesigners("1", "2") // 偏好前两个设计师
                                .requireSkill(List.of("3","5"))
                )
                .skills(10) // 生成10个技能
                .build();

        problemVo.setSolverName("CHINA_DESIGN_SCHEDULE");

        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo, true);
        Assertions.assertEquals(maxTask, assignedTaskVos.size());

        Map<String, List<AssignedTaskVo>> collect = assignedTaskVos.stream()
                .filter(assignedTaskVo -> StringUtils.isNotBlank(assignedTaskVo.getDesignerId()))
                .filter(assignedTaskVo -> !assignedTaskVo.getDesignerCode().contains("faker"))
                .collect(Collectors.groupingBy(AssignedTaskVo::getDesignerCode));
        Assertions.assertEquals(maxDesigner, collect.size());

        List<DesignerVo> designers = problemVo.getDesigners();

        // 1、非病例指定分配的设计师按照分配工单数量按照日比例均衡
        List<Integer> list = collect.entrySet()
                .stream()
                .filter(entity -> !entity.getKey().contains("faker"))
                .filter(entity -> !entity.getKey().contains("case designer"))
                .map(entry -> {
                    int quota = designers.stream()
                            .filter(d -> d.getCode().equals(entry.getKey()))
                            .findFirst()
                            .map(d -> d.getCapacityInfos().getOriginalQuota())
                            .orElse(1000);
                    if (1000 == quota) return null;
                    return entry.getValue().size() / quota;
                })
                .filter(Objects::nonNull)
                .toList();
        // todo
        double calculateVariance = TasUnitTestUtil.calculateVariance(list.stream().mapToLong(a -> (long) a).toArray());
        Assertions.assertTrue(calculateVariance < 1);

        // 2、如果指定了病例分配，工单必须分给病例指定分配的设计师
        boolean match = assignedTaskVos.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getTaskCode().contains("case designer"))
                .anyMatch(assignedTaskVo -> assignedTaskVo.getDesignerCode().contains("case designer"));
        Assertions.assertTrue(match);


        // 3、保证大额度的工单也能分出去 满足优先填满设计师产能约束
//        boolean unAssigneeTask1 = assignedTaskVos.stream()
//                .filter(assignedTaskVo->assignedTaskVo.getDesignerCode().contains("faker"))
//                .noneMatch(assignedTaskVo -> assignedTaskVo.getTaskCode().contains("unAssigneeTask1"));
//        Assertions.assertTrue(unAssigneeTask1);


        // 4、如果需要溢出 应该溢出工单优先级较低的工单
        //todo
    }
}

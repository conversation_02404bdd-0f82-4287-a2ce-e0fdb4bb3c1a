//package com.angelalign.tas.rest.claim;
//
//import com.angelalign.tas.domain.assignment.enums.CaseAllocateTypeEnum;
//import com.angelalign.tas.domain.assignment.enums.PhaseType;
//import com.angelalign.tas.rest.vo.ProblemVo;
//import com.angelalign.tas.rest.vo.support.*;
//import com.angelalign.tas.service.CleanDataService;
//import com.fasterxml.jackson.core.JsonProcessingException;
//import com.fasterxml.jackson.databind.ObjectMapper;
//import com.google.common.collect.Lists;
//import io.restassured.http.ContentType;
//import io.restassured.response.Response;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.AfterEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.Timeout;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import java.time.LocalDate;
//
//import static io.restassured.RestAssured.given;
//import static org.hamcrest.Matchers.equalTo;
//import static org.hamcrest.Matchers.greaterThan;
//
//@SpringBootTest(properties = {
//        // Effectively disable spent-time termination in favor of the best-score-limit
//        "optaplanner.solver.termination.spent-limit=1h",
//        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
//        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
//@Slf4j
//public class ClaimProblemControllerTest {
//
//    @Autowired
//    private ObjectMapper objectMapper;
//    @Autowired
//    private CleanDataService cleanupDataService;
//    @AfterEach
//    public void cleanup() {
//        cleanupDataService.cleanData();
//    }
//    @Test
//    @Timeout(600_000)
//    public void solveClaimDataUntilFeasible_2t1d_Amount() throws JsonProcessingException, InterruptedException {
//        ProblemVo problemVo = ProblemVo.builder()
//                .code("demo_2t1d")
//                .client("test")
//                .solverType("CLAIM")
//                .skills(Lists.newArrayList(
//                        SkillVo.builder()
//                                .id("skill#1")
//                                .code("skill1")
//                                .build()
//                ))
//                .taskTypes(Lists.newArrayList(
//                        TaskTypeVo.builder()
//                                .id("taskType#1")
//                                .code("taskType1")
//                                .build()
//                ))
//                .tasks(Lists.newArrayList(
//                        TaskVo.builder()
//                                .id("1")
//                                .code("task1")
//                                .deadlineRemain(0)
//                                .caseCode("caseCode#1")
//                                .taskTypeId("taskType#1")
//                                .dentistId("dentist#1")
//                                .requiredSkillId("skill#1")
//                                .build(),
//                        TaskVo.builder()
//                                .id("2")
//                                .code("task2")
//                                .deadlineRemain(0)
//                                .caseCode("caseCode#2")
//                                .taskTypeId("taskType#1")
//                                .dentistId("dentist#1")
//                                .requiredSkillId("skill#1")
//                                .build()
//                ))
//                .caseTasks(Lists.newArrayList(
//                        CaseTaskVO.builder()
//                                .caseCode("case#1")
//                                .phaseType(PhaseType.NEW_PHASE)
//                                .taskTypeCode("taskType#1")
//                                .taskId(1L)
//                                .caseCode("caseCode#1")
//                                .build(),
//                        CaseTaskVO.builder()
//                                .caseCode("case#1")
//                                .phaseType(PhaseType.NEW_PHASE)
//                                .taskTypeCode("taskType#1")
//                                .taskId(1L)
//                                .caseCode("caseCode#2")
//                                .build()
//                ))
//                .designers(Lists.newArrayList(
//                        DesignerVo.builder()
//                                .id("designer#1")
//                                .code("designer1")
//                                .capacityInfos(CapacityInfoVO.builder()
//                                        .id(1L)
//                                        .caseAllocateType(  CaseAllocateTypeEnum.WORKING_HOURS)
//                                        .workingday(LocalDate.now())
//                                        .designerId("designer#1")
//                                        .overAllocationLimit(0)
//                                        .modifyDesignExclusive(Boolean.FALSE)
//                                        .capacityTaskTypeQuotaVOList(Lists.newArrayList(
//                                                CapacityTaskTypeQuotaVO.builder()
//                                                        .id(1L)
//                                                        .taskType("taskType#1")
//                                                        .quota(10)
//                                                        .build()
//                                        ))
//                                        .build())
//                                .skillIds(Lists.newArrayList("skill#1"))
//                                .build()
//                ))
//                .dentists(Lists.newArrayList(
//                        DentistVo.builder()
//                                .id("dentist#1")
//                                .code("dentist1")
//                                .build()
//                ))
//                .build();
//
//        // 调用 API 并验证结果
//        Response response = given()
//                .contentType(ContentType.JSON)
//                .when()
//                .body(objectMapper.writeValueAsString(problemVo))
//                .post("/api/task-assignment/problem")
//                .then()
//                .statusCode(200)
//                .contentType(ContentType.JSON)
//                .body("success", equalTo(Boolean.TRUE))
//                .body("data.id", greaterThan(1))
//                .extract()
//                .response();
//
//        int id = response.getBody().jsonPath().getInt("data.id");
//        log.info("problem id= " + id);
//
//        given().contentType(ContentType.JSON)
//                .when()
//                .get("/api/task-assignment/problem/" + id)
//                .then()
//                .statusCode(200)
//                .body("success", equalTo(Boolean.TRUE))
//                .body("data.id", equalTo(id))
//                .body("data.code", equalTo("demo_2t1d"));
//
//        given().contentType(ContentType.JSON)
//                .when()
//                .post("/api/task-assignment/blockSolve/" + id)
//                .then()
//                .statusCode(200)
//                .body("data", equalTo(1));
//        log.info("solution ready");
//    }
//}

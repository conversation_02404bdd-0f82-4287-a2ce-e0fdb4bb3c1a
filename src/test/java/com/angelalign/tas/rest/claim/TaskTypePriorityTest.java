package com.angelalign.tas.rest.claim;

import com.angelalign.tas.domain.assignment.enums.PhaseType;
import com.angelalign.tas.domain.assignment.enums.TaskTypeCodeEnum;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.*;
import com.angelalign.tas.service.CleanDataService;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.ProblemGenerator;
import com.angelalign.tas.util.TasUnitTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.stream.IntStream;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class TaskTypePriorityTest {
    @Autowired
    private CleanDataService cleanupDataService;
    @AfterEach
    public void cleanup() {
        cleanupDataService.cleanData();
    }
    @Test
    @Timeout(600_000)
    public void test() {
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 1);
        problemVo.setSolverName(SolverTypeEnum.COMMON_MANUAL_CLAIM.name());
        List<TaskVo> task = IntStream.rangeClosed(1, 5)
                .mapToObj(i -> ProblemGenerator.generateTasks(1))
                .flatMap(Collection::stream)
                .toList();
        // 领取这个工单
        TaskVo taskVo = task.get(1);
        String taskId = taskVo.getId();
        taskVo.setPriorityScore(200);
        problemVo.setTasks(task);

        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        Assertions.assertEquals(assignedTaskVos.size(), 1);
        List<AssignedTaskVo> list = assignedTaskVos.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getTaskId().equals(taskId))
                .toList();
        Assertions.assertEquals(list.size(),1);
    }
}

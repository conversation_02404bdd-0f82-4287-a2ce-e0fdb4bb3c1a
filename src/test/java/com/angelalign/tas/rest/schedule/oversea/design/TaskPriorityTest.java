package com.angelalign.tas.rest.schedule.oversea.design;

import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import com.angelalign.tas.rest.vo.support.DesignerVo;
import com.angelalign.tas.rest.vo.support.TaskVo;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.ProblemGenerator;
import com.angelalign.tas.util.TasUnitTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Collection;
import java.util.List;
import java.util.stream.IntStream;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class TaskPriorityTest {
    @Test
    @Timeout(600_000)
    public void taskPriorityTest() {
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 1);
        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_SCHEDULE.name());
        List<TaskVo> task = IntStream.rangeClosed(1, 5)
                .mapToObj(i -> ProblemGenerator.generateTasks(1))
                .flatMap(Collection::stream)
                .toList();


        TaskVo taskVo = task.get(1);
        String taskId = taskVo.getId();
        taskVo.setPriorityScore(200);
        problemVo.setTasks(task);
        problemVo.getDesigners().get(0).getCapacityInfos().setOriginalQuota(1);
        problemVo.getDesigners().get(0).getCapacityInfos().setOverAllocationLimit(1);

        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        Assertions.assertEquals(assignedTaskVos.size(), 1);
        List<AssignedTaskVo> list = assignedTaskVos.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getTaskId().equals(taskId))
                .toList();
        Assertions.assertEquals(list.size(), 1);
    }
}

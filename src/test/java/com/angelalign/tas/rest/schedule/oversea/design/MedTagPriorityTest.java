package com.angelalign.tas.rest.schedule.oversea.design;

import com.angelalign.tas.domain.assignment.enums.DesignerRankEnum;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import com.angelalign.tas.rest.vo.support.DesignerVo;
import com.angelalign.tas.rest.vo.support.MedTagVo;
import com.angelalign.tas.rest.vo.support.TaskVo;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.ProblemGenerator;
import com.angelalign.tas.util.TasUnitTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.stream.IntStream;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class MedTagPriorityTest {
//    @Test
//    @Timeout(600_000)
//    public void medTagPriorityTest() {
//        // 按照标签等级溢出给组长，即低优先级的标签溢出组长，高优先级的给设计师
//        ProblemVo problem = ProblemGenerator.generateProblem(1, 1);
//        problem.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_SCHEDULE.name());
//        problem.getMedTags().get(0).setLevelScore(10);
//        // 五个工单 标签等级是10
//        List<TaskVo> taskList = IntStream.rangeClosed(1, 5).mapToObj(i -> {
//                    List<TaskVo> taskVos = ProblemGenerator.generateTasks(1);
//                    taskVos.get(0).setBaseDurationInMinutes(10);
//                    return taskVos;
//                })
//                .flatMap(List::stream)
//                .toList();
//
//        // 创建一个低优先级的tag
//        List<MedTagVo> medTagVos = ProblemGenerator.generateMedTag(1);
//        MedTagVo medTagVo = medTagVos.get(0);
//        medTagVo.setLevelScore(5);
//        medTagVo.setId("low med tag level");
//        medTagVos.add(problem.getMedTags().get(0));
//
//        // 生成两个设计师，能做所有的标签，但是每个人的产能都是10分钟
//        List<DesignerVo> designer = IntStream.rangeClosed(1, 1).mapToObj(i -> {
//                    List<DesignerVo> designerVos = ProblemGenerator.generateDesigners(1);
//                    designerVos.get(0).getCapacityInfos().getCapacityTaskTypeQuotaVOList().get(0).setQuota(11);
//                    designerVos.get(0).setMedTagIds(problem.getMedTags().stream().map(MedTagVo::getId).toList());
//                    return designerVos;
//                })
//                .flatMap(List::stream)
//                .toList();
//
//        // 高优先级标签，其他的都需要分给组长
//        TaskVo taskVo = taskList.get(1);
//        taskVo.getAttributes().put("caseTag", medTagVos.get(1).getId())
//        taskVo.setMedTagIds(List.of(medTagVos.get(1).getId()));
//        TaskVo taskVo1 = taskList.get(0);
//        taskVo1.setMedTagIds(List.of(medTagVos.get(1).getId()));
//
//        // 低优先级标签列表，这些都需要分给组长
//        List<String> lowLevelMedTagTaskIds = taskList.stream()
//                .filter(t -> t.getMedTagIds().contains(medTagVos.get(0).getId()))
//                .map(TaskVo::getId)
//                .toList();
//
//        problem.setTasks(taskList);
//        problem.setDesigners(designer);
//        problem.setMedTags(medTagVos);
//        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problem, true);
//
//        // 找到分不出去的工单
//        List<String> unAssignTaskIds = assignedTaskVos.stream()
//                .filter(assignedTaskVo -> !DesignerRankEnum.VISUAL.name().equals(assignedTaskVo.getDesignerRand()))
//                .map(AssignedTaskVo::getTaskId)
//                .toList();
//        log.info("unAssignTaskIds: {}", unAssignTaskIds);
//        Assertions.assertTrue(unAssignTaskIds.containsAll(lowLevelMedTagTaskIds));
//    }
}

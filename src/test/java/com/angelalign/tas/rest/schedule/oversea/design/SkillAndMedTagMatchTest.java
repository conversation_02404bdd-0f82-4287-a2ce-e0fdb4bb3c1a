package com.angelalign.tas.rest.schedule.oversea.design;

import com.angelalign.tas.domain.assignment.enums.DesignerRankEnum;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import com.angelalign.tas.rest.vo.support.DesignerVo;
import com.angelalign.tas.service.CleanDataService;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.TasUnitTestUtil;
import com.angelalign.tas.util.ProblemGenerator;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class SkillAndMedTagMatchTest {
    @Autowired
    private CleanDataService cleanupDataService;
    @AfterEach
    public void cleanup() {
        cleanupDataService.cleanData();
    }
    @Test
    @Timeout(600_000)
    public void oneTaskVsFiveDesignerSchedule() {
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 5);
        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_SCHEDULE.name());
        TasUnitTestUtil.createAndSolve(problemVo, 1);
    }
//    @Test
//    @Timeout(60000_000)
//    public void Task1000VsDesigner100Schedule() {
//        ProblemVo problemVo = ProblemGenerator.generateProblem(1000, 100);
//        problemVo.setSolverType("SCHEDULE");
//            problemVo.setTeamType("DESIGN");
//        problemVo.setTeamLocation("BRAZIL_A");
//        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
//        int size = assignedTaskVos.size();
//        Assertions.assertEquals(100, size);
//    }

    @Test
    @Timeout(600_000)
    public void twoTaskVsTenDesignerSchedule() {
        ProblemVo problemVo = ProblemGenerator.generateProblem(2, 5);
        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_SCHEDULE.name());
        TasUnitTestUtil.createAndSolve(problemVo, 2);
    }
}

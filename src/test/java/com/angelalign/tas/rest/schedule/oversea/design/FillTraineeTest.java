package com.angelalign.tas.rest.schedule.oversea.design;

import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import com.angelalign.tas.rest.vo.support.DesignerVo;
import com.angelalign.tas.rest.vo.support.TaskVo;
import com.angelalign.tas.domain.assignment.enums.EntityAttributeKey;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.ProblemGenerator;
import com.angelalign.tas.util.TasUnitTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.Collection;
import java.util.List;
import java.util.stream.IntStream;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class FillTraineeTest {
    @Test
    @Timeout(600_000)
    public void fillTraineeTest() {
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 1);
        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_SCHEDULE.name());
        List<TaskVo> task = IntStream.rangeClosed(1, 5)
                .mapToObj(i -> ProblemGenerator.generateTasks(1))
                .flatMap(Collection::stream)
                .toList();
        problemVo.setTasks(task);

        List<DesignerVo> designer = IntStream.rangeClosed(1, 3)
                .mapToObj(i -> {
                    DesignerVo designerVo = ProblemGenerator.generateDesigners(1).get(0);
                    designerVo.getAttributes().put(EntityAttributeKey.IS_INTERN.getKey(),Boolean.TRUE.toString());
                    designerVo.setDesignerLevelSequence(-1);
                    designerVo.getCapacityInfos().getCapacityTaskTypeQuotaVOList()
                            .forEach(capacityTaskTypeQuotaVO -> capacityTaskTypeQuotaVO.setQuota(60));
                    return designerVo;
                })
                .toList();
        // 这个人分不到
        DesignerVo designerVo = designer.get(1);
        designerVo.getAttributes().put(EntityAttributeKey.IS_INTERN.getKey(),Boolean.FALSE.toString());

        problemVo.setDesigners(designer);
        //
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        Assertions.assertEquals(assignedTaskVos.size(), 5);
        List<AssignedTaskVo> list = assignedTaskVos.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getDesignerCode().equals(designerVo.getCode()))
                .toList();
        Assertions.assertTrue(list.isEmpty());
    }
}

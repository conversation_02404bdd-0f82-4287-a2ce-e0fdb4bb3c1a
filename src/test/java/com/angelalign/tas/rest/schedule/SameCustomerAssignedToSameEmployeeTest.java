//package com.angelalign.tas.rest.schedule;
//
//import com.angelalign.tas.rest.vo.ProblemVo;
//import com.angelalign.tas.rest.vo.support.*;
//import com.angelalign.tas.util.ProblemGenerator;
//import com.angelalign.tas.util.TasUnitTestUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Assertions;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.Timeout;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import java.util.List;
//import java.util.stream.IntStream;
//
//@SpringBootTest(properties = {
//        // Effectively disable spent-time termination in favor of the best-score-limit
//        "optaplanner.solver.termination.spent-limit=1h",
//        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
//        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
//@Slf4j
//public class SameCustomerAssignedToSameEmployeeTest {
//    @Test
//    @Timeout(600_000)
//    public void sameCustomerAssignedToSameEmployeeTest() {
//        //同一个医生的病例尽可能分给同一个设计师
//        ProblemVo problem = ProblemGenerator.generateProblem(1, 1);
//        problem.setSolverType("SCHEDULE");
//        // 生成10个全部能分配的工单和设计师
//        List<TaskVo> taskList = IntStream.rangeClosed(1, 10).mapToObj(i ->
//                        // 全是医生1的病例
//                        ProblemGenerator.generateTasks(1)
//                )
//                .flatMap(List::stream)
//                .toList();
//
//        for (int i = 1; i <= taskList.size(); i++) {
//            // 手动修改每个工单的医生
//            taskList.get(i - 1).setDentistId(String.valueOf(i));
//        }
//        List<DentistVo> dentistVos = ProblemGenerator.generateDentists(10);
//        List<DesignerVo> designerVoList = IntStream.rangeClosed(1, 10).mapToObj(i ->
//                        ProblemGenerator.generateDesigners(1)
//                )
//                .flatMap(List::stream)
//                .toList();
//        // 应该10平均分配，每人一个
//        // 修改第二个设计师，使得第二个设计师做过第二个医生的病例
//        DesignerVo designerVo = designerVoList.get(1);
//        designerVo.getConsumedQuotaTasks().add(ProblemGenerator.generateDesignerConsumedQuotaTask(designerVo
//                , Integer.parseInt(problem.getTaskTypes().get(0).getId())));
//        // 这个人肯定可以分到第二个工单
//        String designer = designerVo.getId();
//        String taskId = taskList.get(1).getId();
//
//        problem.setDentists(dentistVos);
//        problem.setTasks(taskList);
//        problem.setDesigners(designerVoList);
//        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problem);
//
//        // 十个工单全部分配
//        Assertions.assertEquals(assignedTaskVos.size(), 10);
//
//        // 找到预期的设计师是否分配给了预期的工单
//        boolean present = assignedTaskVos.stream()
//                .filter(assignedTaskVo -> assignedTaskVo.getDesignerId().equals(designer))
//                .anyMatch(assignedTaskVo -> assignedTaskVo.getTaskId().equals(taskId));
//        Assertions.assertTrue(present);
//    }
//
//}

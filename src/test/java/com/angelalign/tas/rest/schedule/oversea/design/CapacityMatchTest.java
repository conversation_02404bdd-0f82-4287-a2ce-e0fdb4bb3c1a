package com.angelalign.tas.rest.schedule.oversea.design;

import com.angelalign.tas.domain.assignment.enums.CaseAllocateTypeEnum;
import com.angelalign.tas.domain.assignment.enums.TaskTypeCodeEnum;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.*;
import com.angelalign.tas.service.CleanDataService;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.FlexibleProblemGenerator;
import com.angelalign.tas.util.ProblemGenerator;
import com.angelalign.tas.util.TasUnitTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class CapacityMatchTest {
    @Autowired
    private CleanDataService cleanupDataService;

    @AfterEach
    public void cleanup() {
        cleanupDataService.cleanData();
    }

    @Test
    @Timeout(600_000)
    public void oneTaskVsTenDesignerWithNoCapacitySchedule() {
        // 生成1个工单和5个设计师
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 5);

        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_SCHEDULE.name());
        // 每一个设计师都不配置产能信息
        List<DesignerVo> list = problemVo.getDesigners()
                .stream()
                .peek(designer -> designer.setCapacityInfos(null))
                .toList();
        problemVo.setDesigners(list);
        // 断言一个也分不了
        TasUnitTestUtil.createAndSolve(problemVo, 0);
    }

    @Test
    @Timeout(600_000)
    public void oneTaskVsTenDesignerWithNoCapacityQuotaSchedule() {
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 5);
        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_SCHEDULE.name());
        List<DesignerVo> list = problemVo.getDesigners()
                .stream()
                .peek(designer -> {
                    // 移除所有产能额度
                    CapacityInfoVO capacityInfos = designer.getCapacityInfos();
                    capacityInfos.setOverAllocationLimit(0);
                    capacityInfos.setOriginalQuota(0);
                    List<CapacityTaskTypeQuotaVO> list1 = capacityInfos.getCapacityTaskTypeQuotaVOList().stream()
                            .peek(capacityTaskTypeQuotaVO -> capacityTaskTypeQuotaVO.setQuota(0))
                            .toList();
                    capacityInfos.setCapacityTaskTypeQuotaVOList(list1);
                    designer.setCapacityInfos(capacityInfos);
                })
                .toList();
        problemVo.setDesigners(list);
        TasUnitTestUtil.createAndSolve(problemVo, 0);
    }

    @Test
    @Timeout(600_000)
    public void fiveTaskVsTwoDesignerWithNoCapacityQuotaSchedule() {
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .tasks(5)
                .designers(2)
                .solverName(SolverTypeEnum.OVERSEA_DESIGN_SCHEDULE.name())
                .defaultCapacityRatio(0)
                .defaultCapacityAllocationType(CaseAllocateTypeEnum.MIXED_TASK_QUANTITY)
                .withDesigner(
                        // 这个设计师会分到所有的
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(0)
                                .code("designer1")
                                .enableAllSkills()
                              //  .enableAllMedTags()
                )
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(5)
                                .code("designer2")
                                .enableAllSkills()
                              //  .enableAllMedTags()
                )
                .skills(1)
                .build();
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        // 五个工单全部分配并且全部分给一个人
        int designerCount = assignedTaskVos.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getDesignerCode().equals("designer2"))
                .toList()
                .size();
        Assertions.assertEquals(5, designerCount);
    }

    @Test
    @Timeout(600_000)
    public void fillDesignerCapacity() {
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .tasks(3)
                .solverName(SolverTypeEnum.OVERSEA_DESIGN_SCHEDULE.name())
                .designers(2)
                .defaultDesignerCapacity(0)
                .defaultCapacityRatio(0)
                .defaultCapacityAllocationType(CaseAllocateTypeEnum.WORKING_HOURS)
                .withDesigner(
                        // 这个设计师会分到所有的
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(50)
                                .code("designer1")
                                .enableAllSkills()
                              //  .enableAllMedTags()
                )
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(0)
                                .code("designer2")
                                .enableAllSkills()
                              //  .enableAllMedTags()
                )
                .skills(1)
                .build();
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        // 全部分配
        Assertions.assertEquals(3, assignedTaskVos.size());
    }


    // 设计组按照产能的比例划分
    @Test
    @Timeout(600_000)
    public void fillDesignerCapacityByRatio() {
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 1);
        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_SCHEDULE.name());
        List<TaskVo> task = IntStream.rangeClosed(1, 15)
                .mapToObj(i -> ProblemGenerator.generateTasks(1))
                .flatMap(Collection::stream)
                .toList();
        problemVo.setTasks(task);

        List<DesignerVo> designer = IntStream.rangeClosed(1, 2)
                .mapToObj(i -> ProblemGenerator.generateDesigners(1))
                .flatMap(Collection::stream)
                .toList();
        // 第一个设计师额度30
        DesignerVo designer1 = designer.get(0);
        designer1.getCapacityInfos().setOverAllocationLimit(30 * 15);
        designer1.getCapacityInfos().setOriginalQuota(30 * 15);
        designer1.getCapacityInfos()
                .getCapacityTaskTypeQuotaVOList()
                .forEach(capacityTaskTypeQuotaVO -> capacityTaskTypeQuotaVO.setQuota(30 * 15));
        // 第二个设计师额度15
        DesignerVo designer2 = designer.get(1);
        designer2.getCapacityInfos().setOverAllocationLimit(15 * 15);
        designer2.getCapacityInfos().setOriginalQuota(30 * 15);
        designer2.getCapacityInfos()
                .getCapacityTaskTypeQuotaVOList()
                .forEach(capacityTaskTypeQuotaVO -> capacityTaskTypeQuotaVO.setQuota(15 * 15));
        problemVo.setDesigners(designer);

        // 分配的工单应该是2:1 也就是 10 和5
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        Assertions.assertEquals(15, assignedTaskVos.size());

        Map<String, List<AssignedTaskVo>> collect = assignedTaskVos.stream().collect(Collectors.groupingBy(AssignedTaskVo::getDesignerCode));
        Assertions.assertEquals(2, collect.size());
        List<Integer> list = collect.values().stream()
                .map(List::size)
                .sorted(Comparator.comparing(Integer::intValue).reversed())
                .toList();
        double radio = list.get(0) / (double) list.get(1);
        Assertions.assertEquals(2, radio, 1);
    }

    // 维护全设计独占其他人不能分
    @Test
    @Timeout(600_000)
    public void modifyAssigneeCaseAssigmentDesignerAndOtherDesigner() {
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 5);
        TaskVo taskVo = problemVo.getTasks().get(0);
        taskVo.setPreferredDesignerIds(List.of("case assigment designer#1"));

        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_SCHEDULE.name());
        List<DesignerVo> designers = problemVo.getDesigners();

        // 生成一个设计师，并设置为病例分配的设计师，应该优先分给他
        ArrayList<DesignerVo> generatedDesigners = ProblemGenerator.generateDesigners(5).stream()
                .peek(designer -> {
                    designer.setId("case assigment designer#1");
                    designer.setCode("case assigment designer");
                    designer.setOnDuty(Boolean.FALSE);
                })
                .collect(Collectors.toCollection(ArrayList::new));

        generatedDesigners.addAll(designers);
        problemVo.setDesigners(generatedDesigners);

        List<AssignedTaskVo> result = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        // 不分配
        Assertions.assertEquals(0, result.size());
    }
}

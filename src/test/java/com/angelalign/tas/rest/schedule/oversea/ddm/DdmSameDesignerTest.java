package com.angelalign.tas.rest.schedule.oversea.ddm;

import com.angelalign.tas.domain.assignment.enums.CaseAllocateTypeEnum;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import com.angelalign.tas.rest.vo.support.DesignerVo;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.FlexibleProblemGenerator;
import com.angelalign.tas.util.TasUnitTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class DdmSameDesignerTest {
    @Test
    @Timeout(600_000)
    public void test() {
        int maxTask = 10000;
        int maxDesigner = 1000;
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .tasks(maxTask)
                .designers(maxDesigner)
                .defaultDesignerCapacity(10) // 设置默认产能
                .defaultCapacityRatio(0.3f) // 产能有0.3的浮动
                .defaultCapacityAllocationType(CaseAllocateTypeEnum.MIXED_TASK_QUANTITY)
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(20)
                                .code("case designer1")
                                .enableAllSkills() // 掌握所有技能
//                                .enableAllMedTags()
                )
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(20)
                                .code("case designer2")
                                .enableAllSkills()
//                                .enableAllMedTags()
                )
                .withTask(
                        FlexibleProblemGenerator.TaskConfig.create()
                                .priority(10) // 最高优先级
                                .code("task case designer")
                                .preferDesigners("1", "2") // 偏好前两个设计师
                                .requireSkill(List.of("3"))
//                                .withMedTag("5")
                )
//                .withTask(
//                        FlexibleProblemGenerator.TaskConfig.create()
//                                .code("unAssigneeTask1")
//                                // 保证没有设计师可以有产能分到他
//                                .requireQuota(10000)
//                )
                .skills(5) // 生成10个技能
                .build();

        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_SCHEDULE.name());

        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo, true);
        Assertions.assertEquals(maxTask, assignedTaskVos.size());

        Map<String, List<AssignedTaskVo>> collect = assignedTaskVos.stream()
                .filter(assignedTaskVo -> StringUtils.isNotBlank(assignedTaskVo.getDesignerId()))
                .filter(assignedTaskVo -> !assignedTaskVo.getDesignerCode().contains("faker"))
                .collect(Collectors.groupingBy(AssignedTaskVo::getDesignerCode));
        Assertions.assertEquals(maxDesigner, collect.size());

        List<DesignerVo> designers = problemVo.getDesigners();
    }
}

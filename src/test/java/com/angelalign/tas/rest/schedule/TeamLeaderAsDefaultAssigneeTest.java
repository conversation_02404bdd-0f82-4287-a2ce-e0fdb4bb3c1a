//package com.angelalign.tas.rest.schedule;
//
//import com.angelalign.tas.domain.assignment.enums.DesignerRankEnum;
//import com.angelalign.tas.rest.vo.ProblemVo;
//import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
//import com.angelalign.tas.rest.vo.support.DesignerVo;
//import com.angelalign.tas.util.ProblemGenerator;
//import com.angelalign.tas.util.TasUnitTestUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.junit.jupiter.api.Assertions;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.Timeout;
//import org.springframework.boot.test.context.SpringBootTest;
//
//import java.util.List;
//
//@SpringBootTest(properties = {
//        // Effectively disable spent-time termination in favor of the best-score-limit
//        "optaplanner.solver.termination.spent-limit=1h",
//        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
//        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
//@Slf4j
//public class TeamLeaderAsDefaultAssigneeTest {
//    @Test
//    @Timeout(600_000)
//    public void assigneeTeamLeader() {
//        // 其中第三个task是标签技能不匹配的
//        ProblemVo problemVo = ProblemGenerator.generateProblem(3, 2);
//        problemVo.setSolverType("SCHEDULE");
//
//        DesignerVo designerVo = problemVo.getDesigners().get(0);
//        String designerId = designerVo.getId();
//        designerVo.setRank(DesignerRankEnum.TEAM_LEADER.name());
//
//        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
//        int size = assignedTaskVos.size();
//        Assertions.assertEquals(3, size);
//        // 三个工单全部分配，并且有一个分给了组长
//        int designerCount = assignedTaskVos.stream()
//                .filter(assignedTaskVo -> assignedTaskVo.getDesignerId().equals(designerId))
//                .toList()
//                .size();
//        Assertions.assertEquals(2, designerCount);
//    }
//    @Test
//    @Timeout(600_000)
//    public void assigneeDeputyTeamLeader() {
//        // 1个技能标签不匹配
//        ProblemVo problemVo = ProblemGenerator.generateProblem(3, 2);
//        problemVo.setSolverType("SCHEDULE");
//        DesignerVo designerVo = problemVo.getDesigners().get(0);
//        String designerId = designerVo.getId();
//        designerVo.setRank(DesignerRankEnum.DEPUTY_TEAM_LEADER.name());
//
//        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
//        int size = assignedTaskVos.size();
//        Assertions.assertEquals(3, size);
//        // 三个工单全部分配，并且有一个分给了副组长
//        int designerCount = assignedTaskVos.stream()
//                .filter(assignedTaskVo -> assignedTaskVo.getDesignerId().equals(designerId))
//                .toList()
//                .size();
//        Assertions.assertEquals(2, designerCount);
//    }
//    @Test
//    @Timeout(600_000)
//    public void assigneeTeamLeaderOverMatchDeputyTeamLeader() {
//        // 2个task不能被分配
//        ProblemVo problemVo = ProblemGenerator.generateProblem(5, 3);
//        problemVo.setSolverType("SCHEDULE");
//        DesignerVo designerVo = problemVo.getDesigners().get(1);
//        String designerId = designerVo.getId();
//        designerVo.setRank(DesignerRankEnum.DEPUTY_TEAM_LEADER.name());
//
//        DesignerVo teamLeaderDesigner = problemVo.getDesigners().get(2);
//        String teamLeaderId = teamLeaderDesigner.getId();
//        teamLeaderDesigner.setRank(DesignerRankEnum.TEAM_LEADER.name());
//
//        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
//        int size = assignedTaskVos.size();
//        // 五个全部被分配
//        Assertions.assertEquals(5, size);
//        // 四个分给了组长
//        int designerCount = assignedTaskVos.stream()
//                .filter(assignedTaskVo -> assignedTaskVo.getDesignerId().equals(teamLeaderId))
//                .toList()
//                .size();
//        Assertions.assertEquals(4, designerCount);
//
//    }
//}

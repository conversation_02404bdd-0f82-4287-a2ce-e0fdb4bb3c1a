package com.angelalign.tas.rest.schedule.oversea.ddm;

import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.*;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.ProblemGenerator;
import com.angelalign.tas.util.TasUnitTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class TaskTypeBalanceTest {
    @Test
    @Timeout(600_000)
    public void testTaskTypeBalance() {
        // 工单类型均衡
        ProblemVo problem = ProblemGenerator.generateProblem(5, 1);
        problem.setSolverName(SolverTypeEnum.OVERSEA_DDM_SCHEDULE.name());
        List<TaskVo> taskVoList = IntStream.rangeClosed(1, 2)
                .mapToObj(i -> ProblemGenerator.generateTasks(5))
                .flatMap(List::stream)
                .toList();
        problem.getTasks().addAll(taskVoList);

        List<DesignerVo> designerList = IntStream.rangeClosed(1, 3)
                .mapToObj(i -> {
                    List<DesignerVo> designerVos = ProblemGenerator.generateDesigners(1);
                    // 设计师可以做所有的工单
//                    designerVos.get(0).setMedTagIds(problem.getMedTags().stream().map(MedTagVo::getId).toList());
                    designerVos.get(0).setSkillIds(problem.getSkills().stream().map(SkillVo::getId).toList());
                    return designerVos;
                })
                .flatMap(List::stream)
                .toList();

        problem.setDesigners(designerList);

        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problem);

        // 全部分配
        Assertions.assertEquals(15, assignedTaskVos.size());
        List<Long> list = assignedTaskVos.stream()
                .collect(Collectors.groupingBy(AssignedTaskVo::getTaskCode, Collectors.counting()))
                .values()
                .stream()
                .toList();

        // 计算方差判断平均
        double calculateVariance = TasUnitTestUtil.calculateVariance(list.stream().mapToLong(Long::longValue).toArray());
        // 方差在合理范围内
        Assertions.assertTrue(calculateVariance < 1);
    }
}

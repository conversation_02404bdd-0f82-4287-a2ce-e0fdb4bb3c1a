package com.angelalign.tas.rest.runtime.atp;

import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.FlexibleProblemGenerator;
import com.angelalign.tas.util.TasUnitTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
@Transactional
public class AtpRuntimeTest {
    @Test
    @Timeout(600_000)
    public void atpRuntimeTest(){
        int maxTask = 1;
        int maxDesigner = 5;
        ProblemVo problemVo = FlexibleProblemGenerator.builder()
                .tasks(maxTask)
                .designers(maxDesigner)
                .defaultDesignerCapacity(100) // 设置默认产能
                .defaultCapacityRatio(0.3f) // 产能有0.3的浮动
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(100)
                                .code("case designer1")
                                .enableAllSkills() // 掌握所有技能
                              //  .enableAllMedTags()
                )
                .withDesigner(
                        FlexibleProblemGenerator.DesignerConfig.create()
                                .capacity(100)
                                .code("case designer2")
                                .enableAllSkills()
                              //  .enableAllMedTags()
                )
                .skills(5) // 生成10个技能
//                .medTags(5) // 生成8个标签
                .build();

        problemVo.setSolverName(SolverTypeEnum.COMMON_QUALITY_INSPECTION_RUNTIME.name());

        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo, true);
        Assertions.assertEquals(maxTask, assignedTaskVos.size());

    }
}

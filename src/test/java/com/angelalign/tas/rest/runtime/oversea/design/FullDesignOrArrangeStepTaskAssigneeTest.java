package com.angelalign.tas.rest.runtime.oversea.design;

import com.angelalign.tas.domain.assignment.enums.TaskTypeCodeEnum;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import com.angelalign.tas.rest.vo.support.DesignerVo;
import com.angelalign.tas.rest.vo.support.TaskTypeVo;
import com.angelalign.tas.rest.vo.support.TaskVo;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.ProblemGenerator;
import com.angelalign.tas.util.TasUnitTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class FullDesignOrArrangeStepTaskAssigneeTest {
    @Test
    @Timeout(600_000)
    public void fullDesignerAssigneeCaseDesigner() {
        // 生成工单和设计师
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 1);
        // 修改为是修改工单
        TaskVo taskVo = problemVo.getTasks().get(0);
        taskVo.setTaskTypeId("fullDesign");
        taskVo.setDeadlineRemain(0);
        // 设置为病例指定的设计师
        taskVo.setPreferredDesignerIds(List.of("case assigment designer#1"));
        String taskId = taskVo.getId();

        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_RUNTIME.name());
        // 添加全设计修改工单类型
        List<TaskTypeVo> taskTypes = problemVo.getTaskTypes();
        taskTypes.add(TaskTypeVo.builder().id("fullDesign")
                .code(TaskTypeCodeEnum.executeFullDesign.name())
                .build());

        List<DesignerVo> designers = problemVo.getDesigners();
        // 生成一个设计师，并设置为病例分配的设计师，应该优先分给他
        ArrayList<DesignerVo> generatedDesigners = ProblemGenerator.generateDesigners(1).stream()
                .peek(designer -> {
                    designer.setId("case assigment designer#1");
                    designer.setCode("case assigment designer");
                })
                .collect(Collectors.toCollection(ArrayList::new));

        generatedDesigners.addAll(designers);
        problemVo.setDesigners(generatedDesigners);
        // 校验结果 是病例指定分配的设计师
        List<AssignedTaskVo> result = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        boolean match = result.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getTaskId().equals(taskId))
                .anyMatch(assignedTaskVo -> assignedTaskVo.getDesignerId().equals("case assigment designer#1"));
        Assertions.assertTrue(match);
    }

    @Test
    @Timeout(600_000)
    public void ArrangeStepAssigneeCaseDesigner() {
        // 生成工单和设计师
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 1);
        // 修改为是修改工单
        TaskVo taskVo = problemVo.getTasks().get(0);
        taskVo.setTaskTypeId("aaaa");
        taskVo.setDeadlineRemain(0);
        // 设置为病例指定的设计师
        taskVo.setPreferredDesignerIds(List.of("case assigment designer#1"));
        String taskId = taskVo.getId();

        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_RUNTIME.name());
        // 添加全设计修改工单类型
        List<TaskTypeVo> taskTypes = problemVo.getTaskTypes();
        taskTypes.add(TaskTypeVo.builder().id("aaaa")
                .code(TaskTypeCodeEnum.executeArrangeSteps.name())
                .build());

        List<DesignerVo> designers = problemVo.getDesigners();
        // 生成一个设计师，并设置为病例分配的设计师，应该优先分给他
        ArrayList<DesignerVo> generatedDesigners = ProblemGenerator.generateDesigners(1).stream()
                .peek(designer -> {
                    designer.setId("case assigment designer#1");
                    designer.setCode("case assigment designer");
                })
                .collect(Collectors.toCollection(ArrayList::new));

        generatedDesigners.addAll(designers);
        problemVo.setDesigners(generatedDesigners);
        // 校验结果 是病例指定分配的设计师
        List<AssignedTaskVo> result = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        boolean match = result.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getTaskId().equals(taskId))
                .anyMatch(assignedTaskVo -> assignedTaskVo.getDesignerId().equals("case assigment designer#1"));
        Assertions.assertTrue(match);
    }
}

package com.angelalign.tas.rest.runtime.oversea.ddm;

import com.angelalign.tas.domain.assignment.enums.CaseAllocateTypeEnum;
import com.angelalign.tas.domain.assignment.enums.PhaseType;
import com.angelalign.tas.domain.assignment.enums.TaskTypeCodeEnum;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.*;
import com.angelalign.tas.service.CleanDataService;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.ProblemGenerator;
import com.angelalign.tas.util.TasUnitTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.List;
import java.util.stream.IntStream;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class SegmentTaskAssigmentTest {
    @Autowired
    private CleanDataService cleanupDataService;

    @AfterEach
    public void cleanup() {
        cleanupDataService.cleanData();
    }

    @Test
    @Timeout(600_000)
    public void segmentTaskAssigmentTest() {
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 2);
        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DDM_RUNTIME.name());

        List<DesignerVo> designerList = IntStream.rangeClosed(1, 3)
                .mapToObj(i -> {
                    List<DesignerVo> designerVos = ProblemGenerator.generateDesigners(1);
                    return designerVos;
                })
                .flatMap(List::stream)
                .toList();

        DesignerVo designerVo = designerList.get(0);
        String designerId = designerVo.getId();

        TaskVo taskVo = problemVo.getTasks().get(0);
        taskVo.setTaskTypeId("ddm");
        String taskId = taskVo.getId();

        // 添加咬合调整的工单类型
        List<TaskTypeVo> taskTypes = problemVo.getTaskTypes();
        taskTypes.add(TaskTypeVo.builder().id("ddm")
                .code(TaskTypeCodeEnum.executeOcclusalCorrection.name())
                .build());

        // 生成之前的病例工单信息 数字化的
        CaseTaskVO caseTaskVO = CaseTaskVO.builder()
                .taskId(Long.valueOf("999"))
                .caseCode(taskVo.getCaseCode())
                .phaseType(PhaseType.NEW_PHASE)
                .taskTypeCode(TaskTypeCodeEnum.executeManualSegment.name())
                .assigneeId(Long.valueOf(designerId))
                .build();
        problemVo.setCaseTasks(List.of(caseTaskVO));
        problemVo.setDesigners(designerList);
        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        int size = assignedTaskVos.size();
        Assertions.assertEquals(1, size);
        boolean match = assignedTaskVos.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getTaskId().equals(taskId))
                .anyMatch(assignedTaskVo -> assignedTaskVo.getDesignerId().equals(designerId));
        Assertions.assertTrue(match);
    }
}

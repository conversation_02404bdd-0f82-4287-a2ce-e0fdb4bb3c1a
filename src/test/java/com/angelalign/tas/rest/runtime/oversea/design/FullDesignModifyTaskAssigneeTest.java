package com.angelalign.tas.rest.runtime.oversea.design;

import com.angelalign.tas.domain.assignment.enums.PhaseType;
import com.angelalign.tas.domain.assignment.enums.TaskTypeCodeEnum;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.*;
import com.angelalign.tas.service.CleanDataService;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.ProblemGenerator;
import com.angelalign.tas.util.TasUnitTestUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
public class FullDesignModifyTaskAssigneeTest {
    @Autowired
    private CleanDataService cleanupDataService;

    @AfterEach
    public void cleanup() {
        cleanupDataService.cleanData();
    }

    @Test
    @Timeout(600_000)
    public void modifyAssigneeRecentlyTaskDesigner() {
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 2);
        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_RUNTIME.name());

        DesignerVo designerVo = problemVo.getDesigners().get(0);
        String designerId = designerVo.getId();
        // 不看额度和技能
        designerVo.setCapacityInfos(null);
        // 全设计修改工单
        TaskVo taskVo = problemVo.getTasks().get(0);
        taskVo.setTaskTypeId("modify");
        String taskId = taskVo.getId();

        // 添加全设计修改工单类型
        List<TaskTypeVo> taskTypes = problemVo.getTaskTypes();
        taskTypes.add(TaskTypeVo.builder().id("modify")
                .code(TaskTypeCodeEnum.executeFullDesignModification.name())
                .build());

        // 生成之前的病例工单信息 全设计
        CaseTaskVO caseTaskVO = CaseTaskVO.builder()
                .taskId(Long.valueOf("3"))
                .caseCode(taskVo.getCaseCode())
                .phaseType(PhaseType.NEW_PHASE)
                .taskTypeCode(TaskTypeCodeEnum.executeFullDesign.name())
                .assigneeId(Long.valueOf(designerId))
                .build();
        problemVo.setCaseTasks(List.of(caseTaskVO));

        List<AssignedTaskVo> assignedTaskVos = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        int size = assignedTaskVos.size();
        Assertions.assertEquals(1, size);
        boolean match = assignedTaskVos.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getTaskId().equals(taskId))
                .anyMatch(assignedTaskVo -> assignedTaskVo.getDesignerId().equals(designerId));
        Assertions.assertTrue(match);
    }

    @Test
    @Timeout(600_000)
    public void modifyAssigneeCaseAssigmentDesigner() {
        // 生成工单和设计师
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 1);
        // 修改为是修改工单
        TaskVo taskVo = problemVo.getTasks().get(0);
        taskVo.setTaskTypeId("modify");
        taskVo.setDeadlineRemain(0);
        // 设置为病例指定的设计师
        taskVo.setPreferredDesignerIds(List.of("case assigment designer#1"));
        String taskId = taskVo.getId();

        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_RUNTIME.name());
        // 添加全设计修改工单类型
        List<TaskTypeVo> taskTypes = problemVo.getTaskTypes();
        taskTypes.add(TaskTypeVo.builder().id("modify")
                .code(TaskTypeCodeEnum.executeFullDesignModification.name())
                .build());

        List<DesignerVo> designers = problemVo.getDesigners();
        // 生成一个设计师，并设置为病例分配的设计师，应该优先分给他
        ArrayList<DesignerVo> generatedDesigners = ProblemGenerator.generateDesigners(1).stream()
                .peek(designer -> {
                    designer.setId("case assigment designer#1");
                    designer.setCode("case assigment designer");
                })
                .collect(Collectors.toCollection(ArrayList::new));

        generatedDesigners.addAll(designers);
        problemVo.setDesigners(generatedDesigners);
        // 校验结果 是病例指定分配的设计师
        List<AssignedTaskVo> result = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        boolean match = result.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getTaskId().equals(taskId))
                .anyMatch(assignedTaskVo -> assignedTaskVo.getDesignerId().equals("case assigment designer#1"));
        Assertions.assertTrue(match);
    }

    @Test
    @Timeout(600_000)
    public void fullDesignModifyAssignee() {
        // 生成工单和设计师
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 1);
        // 修改为是修改工单
        TaskVo taskVo = problemVo.getTasks().get(0);
        taskVo.setTaskTypeId("modify");
        taskVo.setDeadlineRemain(0);
        // 设置为病例指定的设计师
        taskVo.setPreferredDesignerIds(List.of("case assigment designer#1"));
        String taskId = taskVo.getId();

        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_RUNTIME.name());
        // 添加全设计修改工单类型
        List<TaskTypeVo> taskTypes = problemVo.getTaskTypes();
        taskTypes.add(TaskTypeVo.builder().id("modify")
                .code(TaskTypeCodeEnum.executeFullDesignModification.name())
                .build());
        // 生成一个设计师，并设置为病例分配的设计师，应该优先分给他
        ArrayList<DesignerVo> generatedDesigners = ProblemGenerator.generateDesigners(1).stream()
                .peek(designer -> {
                    designer.setId("case assigment designer#1");
                    designer.setCode("case assigment designer");
                    designer.setOnDuty(Boolean.FALSE);
                })
                .collect(Collectors.toCollection(ArrayList::new));

        generatedDesigners.addAll(problemVo.getDesigners());
        problemVo.setDesigners(generatedDesigners);

        // 校验结果 是病例指定分配的设计师
        List<AssignedTaskVo> result = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        // 有病例指定分配，但是病例指定分配不在岗， 就不分
        Assertions.assertEquals(0, result.size());
    }

    @Test
    @Timeout(600_000)
    public void modifyAssigneeCaseAssigmentDesignerAndOtherDesigner() {
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 5);
        TaskVo taskVo = problemVo.getTasks().get(0);
        String taskId = taskVo.getId();
        taskVo.setPreferredDesignerIds(List.of("case assigment designer#1"));
        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_RUNTIME.name());
        List<DesignerVo> designers = problemVo.getDesigners();
        // 生成一个设计师，并设置为病例分配的设计师，应该优先分给他
        ArrayList<DesignerVo> generatedDesigners = ProblemGenerator.generateDesigners(5).stream()
                .peek(designer -> {
                    designer.setId("case assigment designer#1");
                    designer.setCode("case assigment designer");
                    designer.setOnDuty(Boolean.FALSE);
                })
                .collect(Collectors.toCollection(ArrayList::new));

        generatedDesigners.addAll(designers);
        problemVo.setDesigners(generatedDesigners);
        List<AssignedTaskVo> result = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        // 不分配
        Assertions.assertEquals(0, result.size());
    }
}

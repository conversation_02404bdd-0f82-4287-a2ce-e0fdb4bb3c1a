package com.angelalign.tas.rest.runtime.oversea.design;

import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import com.angelalign.tas.rest.vo.support.DesignerVo;
import com.angelalign.tas.rest.vo.support.TaskVo;
import com.angelalign.tas.service.CleanDataService;
import com.angelalign.tas.solver.SolverTypeEnum;
import com.angelalign.tas.util.TasUnitTestUtil;
import com.angelalign.tas.util.ProblemGenerator;
import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@SpringBootTest(properties = {
        // Effectively disable spent-time termination in favor of the best-score-limit
        "optaplanner.solver.termination.spent-limit=1h",
        "optaplanner.solver.termination.best-score-limit=0hard/*soft"},
        webEnvironment = SpringBootTest.WebEnvironment.DEFINED_PORT)
@Slf4j
@Transactional
public class CaseAssigmentMatchTest {
    @Autowired
    private CleanDataService cleanupDataService;

    @AfterEach
    public void cleanup() {
        cleanupDataService.cleanData();
    }

    @Test
    @Timeout(600_000)
    public void testCaseAssigmentMatch() {
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 1);
        TaskVo taskVo = problemVo.getTasks().get(0);
        String taskId = taskVo.getId();
        taskVo.setPreferredDesignerIds(List.of("case assigment designer#1"));
        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_RUNTIME.name());
        List<DesignerVo> designers = problemVo.getDesigners();
        // 生成一个设计师，并设置为病例分配的设计师，应该优先分给他
        ArrayList<DesignerVo> generatedDesigners = ProblemGenerator.generateDesigners(1).stream()
                .peek(designer -> {
                    designer.setId("case assigment designer#1");
                    designer.setCode("case assigment designer");
                })
                .collect(Collectors.toCollection(ArrayList::new));

        generatedDesigners.addAll(designers);
        problemVo.setDesigners(generatedDesigners);
        // 校验结果 是病例指定分配的设计师
        List<AssignedTaskVo> result = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        boolean match = result.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getTaskId().equals(taskId))
                .anyMatch(assignedTaskVo -> assignedTaskVo.getDesignerId().equals("case assigment designer#1"));
        Assertions.assertTrue(match);
    }

    @Test
    @Timeout(600_000)
    public void testCaseAssigmentMatchWithMultipleDesigners() {
        ProblemVo problemVo = ProblemGenerator.generateProblem(1, 5);
        TaskVo taskVo = problemVo.getTasks().get(0);
        String taskId = taskVo.getId();
        taskVo.setPreferredDesignerIds(List.of("case assigment designer#1"));
        problemVo.setSolverName(SolverTypeEnum.OVERSEA_DESIGN_RUNTIME.name());
        List<DesignerVo> designers = problemVo.getDesigners();
        // 生成一个设计师，并设置为病例分配的设计师，应该优先分给他
        ArrayList<DesignerVo> generatedDesigners = ProblemGenerator.generateDesigners(5).stream()
                .peek(designer -> {
                    designer.setId("case assigment designer#1");
                    designer.setCode("case assigment designer");
                })
                .collect(Collectors.toCollection(ArrayList::new));

        generatedDesigners.addAll(designers);
        problemVo.setDesigners(generatedDesigners);
        // 校验结果 是病例指定分配的设计师
        List<AssignedTaskVo> result = TasUnitTestUtil.solveAndReturnAssignedTasks(problemVo);
        boolean match = result.stream()
                .filter(assignedTaskVo -> assignedTaskVo.getTaskId().equals(taskId))
                .anyMatch(assignedTaskVo -> "case assigment designer#1".equals(assignedTaskVo.getDesignerId()));
        Assertions.assertTrue(match);
    }
}

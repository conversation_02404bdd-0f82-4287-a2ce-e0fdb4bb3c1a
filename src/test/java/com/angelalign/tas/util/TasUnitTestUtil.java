package com.angelalign.tas.util;

import com.angelalign.tas.domain.assignment.enums.DesignerRankEnum;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.AssignedTaskVo;
import io.restassured.http.ContentType;
import io.restassured.response.Response;
import io.restassured.response.ValidatableResponse;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Assertions;

import java.util.List;

import static io.restassured.RestAssured.given;
import static org.hamcrest.Matchers.equalTo;
import static org.hamcrest.Matchers.greaterThan;

@Slf4j
public class TasUnitTestUtil {


    @SneakyThrows
    public static void createAndSolve(ProblemVo problemVo, int expectedCount) {
        List<AssignedTaskVo> assignedTaskVos = solveAndReturnAssignedTasks(problemVo);
        Assertions.assertEquals(assignedTaskVos.stream()
                        .filter(assignedTaskVo -> StringUtils.isNotBlank(assignedTaskVo.getDesignerId()))
                        .count()
                , expectedCount);
    }

    public static List<AssignedTaskVo> solveAndReturnAssignedTasks(ProblemVo problemVo) {
        return solveAndReturnAssignedTasks(problemVo, false);
    }

    @SneakyThrows
    public static List<AssignedTaskVo> solveAndReturnAssignedTasks(ProblemVo problemVo, Boolean original) {
        Response response = given()
                .contentType(ContentType.JSON)
                .when()
                .body(Jackson.toJsonPrettyString(problemVo))
                .post("/api/task-assignment/problem")
                .then()
                .statusCode(200)
                .contentType(ContentType.JSON)
                .body("success", equalTo(Boolean.TRUE))
                .body("data.id", greaterThan(1))
                .extract()
                .response();
        int id = response.getBody().jsonPath().getInt("data.id");
        ValidatableResponse success = given().contentType(ContentType.JSON)
                .when()
                .post("/api/task-assignment/blockSolve/" + id)
                .then()
                .statusCode(200)
                .body("success", equalTo(Boolean.TRUE));
        String data = Jackson.jsonNodeOf(success.extract().body().asString()).path("data").path("tasks").toString();
        return Jackson.covertObjectListFromJsonString(data, AssignedTaskVo.class)
                .stream()
                .filter(task -> StringUtils.isNotBlank(task.getDesignerId()))
                .filter(task -> {
                    if (original) return true;
                    return !DesignerRankEnum.FAKER.name().equals(task.getDesignerRand());
                })
                .toList();
    }

    /**
     * 计算整数集合的方差
     *
     * @param numbers 整数集合
     * @return 方差值
     * @throws IllegalArgumentException 如果输入集合为空或长度为0
     */
    public static double calculateVariance(long[] numbers) {
        if (numbers == null || numbers.length == 0) {
            throw new IllegalArgumentException("输入集合不能为空或长度为0");
        }

        // 计算平均值
        double mean = calculateMean(numbers);

        // 计算方差
        double variance = 0.0;
        for (long num : numbers) {
            variance += Math.pow(num - mean, 2);
        }
        variance /= numbers.length;

        return variance;
    }

    /**
     * 计算整数集合的平均值
     *
     * @param numbers 整数集合
     * @return 平均值
     */
    private static double calculateMean(long[] numbers) {
        long sum = 0;
        for (long num : numbers) {
            sum += num;
        }
        return (double) sum / numbers.length;
    }
    /**
     * 计算列表中最大值与最小值的差值
     * @param list 整数列表
     * @return 最大值 - 最小值
     * @throws IllegalArgumentException 如果列表为空或为null
     */
    public static int calculateDifference(List<Integer> list) {
        if (list == null || list.isEmpty()) {
            throw new IllegalArgumentException("列表不能为空");
        }

        int max = list.get(0);
        int min = list.get(0);

        // 单次遍历同时寻找最大值和最小值
        for (int num : list) {
            if (num > max) {
                max = num;
            }
            if (num < min) {
                min = num;
            }
        }

        return max - min;
    }
}

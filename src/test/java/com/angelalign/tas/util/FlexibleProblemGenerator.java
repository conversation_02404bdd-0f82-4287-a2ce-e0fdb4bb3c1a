package com.angelalign.tas.util;

import com.angelalign.tas.domain.assignment.enums.CaseAllocateTypeEnum;
import com.angelalign.tas.domain.assignment.enums.DesignerRankEnum;
import com.angelalign.tas.domain.assignment.enums.PhaseType;
import com.angelalign.tas.domain.parser.model.PreferTask;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.*;
import com.angelalign.tas.domain.assignment.enums.EntityAttributeKey;
import com.google.common.collect.Lists;
import lombok.Data;
import net.bytebuddy.utility.RandomString;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.platform.commons.util.StringUtils;

import java.time.LocalDate;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class FlexibleProblemGenerator {
    private static final AtomicLong ID_GENERATOR = new AtomicLong(1);

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private final ProblemConfig config = new ProblemConfig();

        public Builder tasks(int count) {
            config.taskCount = count;
            return this;
        }

        public Builder solverName(String solverName) {
            config.solverName = solverName;
            return this;
        }

        public Builder designers(int count) {
            config.designerCount = count;
            return this;
        }

        public Builder defaultDesignerCapacity(int capacity) {
            config.defaultDesignerCapacity = capacity;
            return this;
        }

        public Builder defaultCapacityRatio(float ratio) {
            config.capacityRatio = ratio;
            return this;
        }

        public Builder defaultCapacityAllocationType(CaseAllocateTypeEnum caseAllocateTypeEnum) {
            config.defaultCaseAllocateType = caseAllocateTypeEnum;
            return this;
        }

        public Builder withDesigner(DesignerConfig designerConfig) {
            config.designerConfigs.add(designerConfig);
            return this;
        }

        public Builder withTask(TaskConfig taskConfig) {
            config.taskConfigs.add(taskConfig);
            return this;
        }

        public Builder withSkill(SkillConfig skillConfig) {
            config.skillConfigs.add(skillConfig);
            return this;
        }

        public Builder skills(int count) {
            config.skillCount = count;
            return this;
        }

//        public Builder medTags(int count) {
//            config.medTagCount = count;
//            return this;
//        }

        public ProblemVo build() {
            return new FlexibleProblemGenerator(config).generate();
        }
    }

    @Data
    private static class ProblemConfig {
        int taskCount = 10;
        int designerCount = 5;
        int skillCount = 5;
        //        int medTagCount = 5;
        int dentistCount = 5;
        int defaultDesignerCapacity = 10000;
        CaseAllocateTypeEnum defaultCaseAllocateType = CaseAllocateTypeEnum.MIXED_TASK_QUANTITY;
        float capacityRatio = 0.3f;
        List<DesignerConfig> designerConfigs = new ArrayList<>();
        List<TaskConfig> taskConfigs = new ArrayList<>();
        List<SkillConfig> skillConfigs = new ArrayList<>();
        int taskTypeCount = 5;
        private String solverName;
    }

    public static class SkillConfig {
        int id;
        String code;

        public static SkillConfig create() {
            return new SkillConfig();
        }

        public SkillConfig id(int id) {
            this.id = id;
            return this;
        }

        public SkillConfig code(String code) {
            this.code = code;
            return this;
        }
    }


    public static class DesignerConfig {
        Long id;
        Integer designerLevel;
        Integer capacity;
        List<String> skills = new ArrayList<>();
        List<String> medTags = new ArrayList<>();
        String code = null;
        boolean enableAllSkills;
        List<PreferTask> preferTask;
        List<String> preferSkillExpression;
        HashMap<String, String> attributes = new HashMap<>();

        public static DesignerConfig create() {
            return new DesignerConfig();
        }

        public DesignerConfig id(Long id) {
            this.id = id;
            return this;
        }

        public DesignerConfig capacity(int capacity) {
            this.capacity = capacity;
            return this;
        }

        public DesignerConfig designerLevel(int designerLevel) {
            this.designerLevel = designerLevel;
            return this;
        }

        public DesignerConfig preferSkillExpression(List<String> preferSkillExpression) {
            this.preferSkillExpression = preferSkillExpression;
            return this;
        }

        public DesignerConfig preferTaskExpression(List<PreferTask> preferTask) {
            this.preferTask = preferTask;
            return this;
        }

        public DesignerConfig code(String code) {
            this.code = code;
            return this;
        }

        public DesignerConfig withSkill(String... skills) {
            this.skills.addAll(Arrays.asList(skills));
            return this;
        }

        public DesignerConfig enableAllSkills() {
            this.enableAllSkills = true;
            return this;
        }

        public DesignerConfig addAttribute(String key, String value) {
            this.attributes.put(key, value);
            return this;
        }
    }

    public static class TaskConfig {
        Integer priority = 1;
        List<String> preferredDesigners = new ArrayList<>();
        List<String> requiredSkill;
        String code;
        float requiredQuotaCount = 1;
        Integer requiredQuotaMinute = 15;
        HashMap<String, String> attributes = new HashMap<>();

        public TaskConfig addAttribute(String key, String value) {
            attributes.put(key, value);
            return this;
        }

        public TaskConfig orderTag(List<MedTagVo> medTagVoList) {
            attributes.put(EntityAttributeKey.ORDER_TAG.getKey(), Jackson.toJsonString(medTagVoList));
            return this;
        }

        public TaskConfig caseTag(List<MedTagVo> medTagVoList) {
            attributes.put(EntityAttributeKey.CASE_TAG.getKey(), Jackson.toJsonString(medTagVoList));
            return this;
        }

        public static TaskConfig create() {
            return new TaskConfig();
        }

        public TaskConfig priority(int priority) {
            this.priority = priority;
            return this;
        }

        public TaskConfig code(String code) {
            this.code = code;
            return this;
        }

        public TaskConfig preferDesigners(String... designerIds) {
            this.preferredDesigners.addAll(Arrays.asList(designerIds));
            return this;
        }

        public TaskConfig requireSkill(List<String> skillId) {
            this.requiredSkill = skillId;
            return this;
        }

        public TaskConfig requireQuotaMinute(Integer quota) {
            this.requiredQuotaMinute = quota;
            return this;
        }

        public TaskConfig requireQuotaCount(float quota) {
            this.requiredQuotaCount = quota;
            return this;
        }

//        public TaskConfig withMedTag(String medTagId) {
//            this.medTag = medTagId;
//            return this;
//        }
    }

    private final ProblemConfig config;
    private final List<SkillVo> allSkills;
    //    private final List<MedTagVo> allMedTags;
    private final List<DentistVo> dentists;
    private final List<DesignerVo> designers;

    private FlexibleProblemGenerator(ProblemConfig config) {
        this.config = config;
        this.allSkills = generateSkills(config);
//        this.allMedTags = generateMedTag(config.medTagCount);
        this.dentists = generateDentists(config.dentistCount);
        this.designers = generateDesigners();
    }

    public static List<DentistVo> generateDentists(int count) {
        return IntStream.rangeClosed(1, count)
                .mapToObj(i -> DentistVo.builder()
                        .id(String.valueOf(i))
                        .code("dentist" + i)
                        .build())
                .collect(Collectors.toCollection(Lists::newArrayList));
    }

    private ProblemVo generate() {
        List<TaskVo> tasks = generateTasks();

        return ProblemVo.builder()
                .code("PROBLEM_" + RandomString.make(6))
                .client("dynamic_client")
                .skills(allSkills)
                .solverName(config.solverName)
                .taskTypes(generateTaskTypes(config.taskTypeCount))
                .tasks(tasks)
                .callBackUrl("")
                .designers(designers)
                .dentists(dentists)
//                .medTags(allMedTags)
                .build();
    }

    private List<DesignerVo> generateDesigners() {
        List<DesignerVo> designers = new ArrayList<>();

        // 生成预配置的设计师
        for (DesignerConfig dc : config.designerConfigs) {
            designers.add(createDesigner(
                    dc.id != null ? dc.id : ID_GENERATOR.getAndIncrement(),
                    dc.capacity != null ? dc.capacity : config.defaultDesignerCapacity,
                    dc.skills,
                    dc.enableAllSkills,
                    dc.code,
                    config, dc.preferTask, dc.preferSkillExpression,dc.attributes
            ));
        }

        // 填充剩余默认设计师
        int remaining = config.designerCount - config.designerConfigs.size();
        for (int i = 0; i < remaining; i++) {
            designers.add(createDesigner(
                    ID_GENERATOR.getAndIncrement(),
                    config.defaultDesignerCapacity,
                    Collections.emptyList(),
                    false,
                    null, config, null, null,new HashMap<>()
            ));
        }

        return designers;
    }

    private DesignerVo createDesigner(long id, int capacity,
                                      List<String> skills
            , boolean allSkills
            , String code
            , ProblemConfig config
            , List<PreferTask> preferTask
            , List<String> preferSkillExpression,HashMap<String,String> attributes) {
        float ratio = 1f;
        if (config.capacityRatio > 0) {
            ratio = ThreadLocalRandom.current().nextFloat(1 - config.capacityRatio, 1 + config.capacityRatio);
        }
        capacity = Math.round(capacity * ratio);
        DesignerVo designer = DesignerVo.builder()
                .id(String.valueOf(id))
                .onDuty(true)
                .code((StringUtils.isNotBlank(code) ? code : "DESIGNER_" + id))
                .rank(DesignerRankEnum.DESIGNER.name())
                .capacityInfos(CapacityInfoVO.builder()
                        .id(id)
                        .originalQuota(capacity)
                        .caseAllocateType(config.defaultCaseAllocateType)
                        .workingday(LocalDate.now())
                        .designerId(String.valueOf(id))
                        .overAllocationLimit(capacity)
                        .modifyDesignExclusive(false)
                        .capacityTaskTypeQuotaVOList(Collections.singletonList(
                                CapacityTaskTypeQuotaVO.builder()
                                        .id(id)
                                        .taskType("equivalentTaskType")
                                        .quota(capacity)
                                        .build()))
                        .build())
                .skillIds(allSkills ? getAllSkillIds() :
                        CollectionUtils.isNotEmpty(skills) ? skills : randomSkillIdList(config.skillCount))
                .build();

        if (CollectionUtils.isNotEmpty(preferTask)) {
            attributes.put(EntityAttributeKey.PREFER_TASK.getKey(), Jackson.toJsonString(preferTask));
        }
        if (CollectionUtils.isNotEmpty(preferSkillExpression)) {
            attributes.put(EntityAttributeKey.PREFER_SKILL.getKey(), Jackson.toJsonString(preferSkillExpression));
        }
        designer.setAttributes(attributes);
        return designer;
    }

    private List<TaskVo> generateTasks() {
        List<TaskVo> tasks = new ArrayList<>();

        // 生成预配置的工单
        for (TaskConfig tc : config.taskConfigs) {
            tasks.add(createTask(tc, config));
        }

        // 填充剩余默认工单
        int remaining = config.taskCount - config.taskConfigs.size();
        for (int i = 0; i < remaining; i++) {
            tasks.add(createTask(null, config));
        }

        return tasks;
    }

    private TaskVo createTask(TaskConfig config, ProblemConfig problemConfig) {
        return TaskVo.builder()
                .id(String.valueOf(ID_GENERATOR.getAndIncrement()))
                .code((config != null && StringUtils.isNotBlank(config.code) ? config.code : "TASK_" + RandomString.make(4)))
                .deadlineRemain(1)
                .baseDurationInMinutes(config != null ? config.requiredQuotaMinute : 15)
                .baseDurationInCount(config != null ? config.requiredQuotaCount : 1f)
                .phaseType(PhaseType.NEW_PHASE)
                .priorityScore(config != null && config.priority != null ?
                        config.priority : ThreadLocalRandom.current().nextInt(300, 1000))
//                .medTagIds(config != null && config.medTag != null ?
//                        Collections.singletonList(config.medTag) :
//                        randomMedTagIdsForTask(problemConfig.medTagCount))
                .caseCode("CASE_" + RandomString.make(6))
                .taskTypeId(randomTaskTypeId(problemConfig.getTaskTypeCount()))
                .dentistId(randomDentistId(problemConfig.getDentistCount()))
                .requiredSkillId(config != null && config.requiredSkill != null ?
                        config.requiredSkill :
                        randomSkillId(problemConfig.getSkillCount()))
                .preferredDesignerIds(config != null ?
                        config.preferredDesigners :
                        Collections.emptyList())
                .attributes(config != null && config.attributes != null ? config.attributes : new HashMap<>())
                .build();
    }

    private List<String> randomSkillId(int count) {
        int i = ThreadLocalRandom.current().nextInt(0, count + 1);
        ArrayList<String> list = new ArrayList<>();
        for (int j = 1; j <= i; j++) {
            list.add(String.valueOf(ThreadLocalRandom.current().nextInt(1, count + 1)));
        }
        return list;
    }

    private List<String> randomSkillIdList(int count) {

        int i = ThreadLocalRandom.current().nextInt(Math.min(2, count), count + 1);
        ArrayList<String> list = new ArrayList<>();
        for (int j = 1; j <= i; j++) {
            list.add(String.valueOf(ThreadLocalRandom.current().nextInt(1, count + 1)));
        }
        return list;
    }

    private String randomDentistId(int count) {
        return String.valueOf(ThreadLocalRandom.current().nextInt(2, count + 1));
    }

    private String randomTaskTypeId(int tasTypeId) {
        return String.valueOf(ThreadLocalRandom.current().nextInt(1, tasTypeId + 1));

    }

    private List<String> randomMedTagIds(int medTagCount) {
        int i = ThreadLocalRandom.current().nextInt(3, medTagCount + 1);
        ArrayList<String> list = new ArrayList<>();
        for (int j = 1; j <= i; j++) {
            list.add(String.valueOf(ThreadLocalRandom.current().nextInt(1, medTagCount + 1)));
        }
        return list;
    }

    private List<String> randomMedTagIdsForTask(int medTagCount) {
        int i = ThreadLocalRandom.current().nextInt(0, 2);
        ArrayList<String> list = new ArrayList<>();
        for (int j = 1; j <= i; j++) {
            list.add(String.valueOf(ThreadLocalRandom.current().nextInt(1, medTagCount + 1)));
        }
        return list;
    }

    // 以下辅助方法保持类似原有实现，但使用配置数据
    public static List<MedTagVo> generateMedTag(int count) {
        return IntStream.rangeClosed(1, count)
                .mapToObj(i -> MedTagVo.builder()
//                        .id(String.valueOf(i))
                        .code("medTag" + i)
                        .levelScore(ThreadLocalRandom.current().nextInt(1, 10 + 1))
                        .build())
                .collect(Collectors.toCollection(Lists::newArrayList));
    }

    public static List<SkillVo> generateSkills(ProblemConfig config) {
        int skillCount = config.getSkillCount();
        List<SkillVo> skillVoList = config.getSkillConfigs().stream()
                .map(skillConfig -> SkillVo.builder()
                        .id(String.valueOf(skillConfig.id))
                        .code(skillConfig.code)
                        .build())
                .collect(Collectors.toCollection(ArrayList::new));

        ArrayList<SkillVo> collect = IntStream.rangeClosed(skillVoList.size(), Math.max(0, skillCount - skillVoList.size()))
                .mapToObj(i -> SkillVo.builder()
                        .id(String.valueOf(i))
                        .code("skill" + i)
                        .build())
                .collect(Collectors.toCollection(Lists::newArrayList));
        skillVoList.addAll(collect);
        return skillVoList;
    }

    public static List<TaskTypeVo> generateTaskTypes(int count) {
        ArrayList<TaskTypeVo> collect = IntStream.rangeClosed(1, count)
                .mapToObj(i -> TaskTypeVo.builder()
                        .id(String.valueOf(i))
                        .code("taskType" + i)
                        .build())
                .collect(Collectors.toCollection(Lists::newArrayList));
        collect.add(TaskTypeVo.builder()
                .id("equivalentTaskType#")
                .code("equivalentTaskType")
                .build());
        return collect;
    }

    private List<String> getAllSkillIds() {
        return allSkills.stream().map(SkillVo::getId).collect(Collectors.toList());
    }

//    private List<String> getAllMedTagIds() {
//        return allMedTags.stream().map(MedTagVo::getId).collect(Collectors.toList());
//    }

    // 其他辅助方法类似.
// ..
    public static void main(String[] args) {
        // 基本用法（生成10个工单，5个设计师）
        ProblemVo problem = FlexibleProblemGenerator.builder()
                .tasks(10)
                .designers(5)
                .build();
        System.out.println(problem);
        // 高级用法（配置特定设计师和工单）
        ProblemVo complexProblem = FlexibleProblemGenerator.builder()
                .tasks(15)
                .designers(5)
                .defaultDesignerCapacity(100) // 设置默认产能
                .withDesigner(
                        DesignerConfig.create()
                                .capacity(200) // 高产能设计师
                                .enableAllSkills() // 掌握所有技能
//                              //  .enableAllMedTags()
                )
                .withDesigner(
                        DesignerConfig.create()
                                .capacity(50) // 低产能设计师
                                .withSkill("1", "3") // 特定技能
//                                .withMedTag("2")
                )
                .withTask(
                        TaskConfig.create()
                                .priority(10) // 最高优先级
                                .preferDesigners("1", "2") // 偏好前两个设计师
                                .requireSkill(List.of("3", "5"))
                )
                .skills(10) // 生成10个技能
//                .medTags(8) // 生成8个标签
                .build();
        System.out.println(problem);
    }
}

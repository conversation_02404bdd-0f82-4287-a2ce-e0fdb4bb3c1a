package com.angelalign.tas.util;

import com.angelalign.tas.domain.assignment.enums.CaseAllocateTypeEnum;
import com.angelalign.tas.domain.assignment.enums.DesignerRankEnum;
import com.angelalign.tas.domain.assignment.enums.PhaseType;
import com.angelalign.tas.rest.vo.ProblemVo;
import com.angelalign.tas.rest.vo.support.*;
import com.google.common.collect.Lists;
import net.bytebuddy.utility.RandomString;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

public class ProblemGenerator {

    public static ProblemVo generateProblem(int taskCount, int designerCount) {
        List<DentistVo> dentistVos = generateDentists(Math.max(taskCount, designerCount));

        List<TaskTypeVo> taskTypeVos = generateTaskTypes(Math.max(taskCount, designerCount));

        List<TaskVo> tasks = generateTasks(taskCount);

        List<DesignerVo> designers = generateDesigners(designerCount);

//        List<CaseTaskVO> caseTasks = generateCaseTasks(tasks);

        List<SkillVo> skillVos = generateSkills(Math.max(taskCount, designerCount));

        List<MedTagVo> medTagVos = generateMedTag(Math.max(taskCount, designerCount));
        return ProblemVo.builder()
                .code("test_" + RandomString.make(6))
                .client("test")
                .skills(skillVos)
                .taskTypes(taskTypeVos)
                .tasks(tasks)
                .callBackUrl("")
//                .caseTasks(caseTasks)
                .designers(designers)
                .dentists(dentistVos)
                .medTags(medTagVos)
                .build();
    }

    public static List<MedTagVo> generateMedTag(int count) {
        return IntStream.rangeClosed(1, count)
                .mapToObj(i -> MedTagVo.builder()
//                        .id(String.valueOf(i))
                        .code("medTag" + i)
                        .build())
                .collect(Collectors.toCollection(Lists::newArrayList));
    }

    public static List<SkillVo> generateSkills(int count) {
        return IntStream.rangeClosed(1, count)
                .mapToObj(i -> SkillVo.builder()
                        .id(String.valueOf(i))
                        .code("skill" + i)
                        .build())
                .collect(Collectors.toCollection(Lists::newArrayList));
    }

    public static List<TaskTypeVo> generateTaskTypes(int count) {
        ArrayList<TaskTypeVo> collect = IntStream.rangeClosed(1, count)
                .mapToObj(i -> TaskTypeVo.builder()
                        .id(String.valueOf(i))
                        .code("taskType" + i)
                        .build())
                .collect(Collectors.toCollection(Lists::newArrayList));
        collect.add(TaskTypeVo.builder()
                .id("equivalentTaskType#")
                .code("equivalentTaskType")
                .build());
        return collect;
    }

    public static List<TaskVo> generateTasks(int count) {
        return IntStream.rangeClosed(1, count)
                .mapToObj(i -> TaskVo.builder()
                        .id(String.valueOf(ThreadLocalRandom.current().nextInt(9999)))
                        .code("task " + RandomString.make(5))
                        .deadlineRemain(1)
                        .baseDurationInMinutes(15)
                        .baseDurationInCount(1f)
                        .phaseType(PhaseType.NEW_PHASE)
                        .priorityScore(1)
                        .caseCode(RandomString.make(5))
                        .taskTypeId(String.valueOf(i))
                        .dentistId(String.valueOf(i))
                        .preferredDesignerIds(Lists.newArrayList())
                        .requiredSkillId(List.of(String.valueOf(i)))
                        .build())
                .collect(Collectors.toCollection(Lists::newArrayList));
    }

    public static List<CaseTaskVO> generateCaseTasks(List<TaskVo> tasks) {
        return tasks.stream()
                .map(task -> CaseTaskVO.builder()
                        .phaseType(PhaseType.NEW_PHASE)
                        .taskTypeCode("taskType1")
                        .taskId(Long.valueOf(task.getId()))
                        .caseCode(task.getCaseCode())
                        .build())
                .collect(Collectors.toCollection(Lists::newArrayList));
    }

    public static List<DesignerVo> generateDesigners(int count) {
        return IntStream.rangeClosed(1, count)
                .mapToObj(i -> DesignerVo.builder()
                        .id(String.valueOf(ThreadLocalRandom.current().nextInt(1,9999)))
                        .onDuty(Boolean.TRUE)
                        .code("designer" + i + "_" + RandomString.make(5))
                        .rank(DesignerRankEnum.DESIGNER.name())
                        .capacityInfos(CapacityInfoVO.builder()
                                .originalQuota(50000)
                                .id(ThreadLocalRandom.current().nextLong(10,5555))
                                .caseAllocateType(CaseAllocateTypeEnum.MIXED_TASK_QUANTITY)
                                .workingday(LocalDate.now())
                                .designerId(String.valueOf(i))
                                .overAllocationLimit(50000)
                                .modifyDesignExclusive(Boolean.FALSE)
                                .capacityTaskTypeQuotaVOList(List.of(
                                        CapacityTaskTypeQuotaVO.builder()
                                                .taskType("equivalentTaskType")
                                                .quota(50000)
                                                .build()
                                ))
                                .build())
                        .skillIds(Collections.singletonList(String.valueOf(i)))
                        .medTagIds(Collections.singletonList(String.valueOf(i)))
                        .build())
                .collect(Collectors.toCollection(Lists::newArrayList));
    }

    private static List<String> generateAllSkillIds(int count) {
        return IntStream.rangeClosed(1, count)
                .boxed()
                .map(String::valueOf)
                .collect(Collectors.toCollection(Lists::newArrayList));
    }

    private static List<String> generateAlMedTagIds(int count) {
        return IntStream.rangeClosed(1, count)
                .boxed()
                .map(String::valueOf)
                .collect(Collectors.toCollection(Lists::newArrayList));
    }

    public static List<DentistVo> generateDentists(int count) {
        return IntStream.rangeClosed(1, count)
                .mapToObj(i -> DentistVo.builder()
                        .id(String.valueOf(i))
                        .code("dentist" + i)
                        .build())
                .collect(Collectors.toCollection(Lists::newArrayList));
    }

    public static DesignerConsumedQuotaTaskVO generateDesignerConsumedQuotaTask(DesignerVo designerVo, int taskTypeId) {
        return DesignerConsumedQuotaTaskVO.builder()
                .designerId(Long.valueOf(designerVo.getId()))
                .taskId((long) ThreadLocalRandom.current().nextInt(9999))
                .taskTypeId((long) taskTypeId)
                .consumedMinuteQuota(15)
                .consumedCountQuota(1f)
                .dentistCode("dentist1")
                .build();
    }
}
